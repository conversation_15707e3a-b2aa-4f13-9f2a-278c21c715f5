<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Limpiar IndexedDB</title>
</head>
<body>
    <h1>Limpiar IndexedDB</h1>
    <button onclick="clearDatabase()">Limpiar Base de Datos</button>
    <div id="output"></div>
    
    <script>
        function log(message) {
            console.log(message);
            document.getElementById('output').innerHTML += '<p>' + message + '</p>';
        }
        
        function clearDatabase() {
            log('Limpiando base de datos IndexedDB...');
            
            // Eliminar la base de datos
            const deleteRequest = indexedDB.deleteDatabase('ipram_innerdb');
            
            deleteRequest.onsuccess = () => {
                log('✅ Base de datos eliminada correctamente');
                log('Puedes cerrar esta página y recargar la aplicación');
            };
            
            deleteRequest.onerror = () => {
                log('❌ Error al eliminar la base de datos: ' + deleteRequest.error);
            };
            
            deleteRequest.onblocked = () => {
                log('⚠️ La eliminación está bloqueada. Cierra todas las pestañas de la aplicación e inténtalo de nuevo.');
            };
        }
    </script>
</body>
</html>
