@echo off
REM Script para ver logs del servidor IPRA_I (Windows) - MOVIDO A AUX

setlocal enabledelayedexpansion

REM Cargar configuración
set "CONFIG_FILE=%~dp0..\config.conf"
if not exist "%CONFIG_FILE%" (
    echo ERROR: Archivo config.conf no encontrado.
    pause
    exit /b 1
)

REM Leer configuración
for /f "tokens=1,2 delims==" %%a in ('type "%CONFIG_FILE%" ^| findstr /v "^#" ^| findstr "="') do (
    set "%%a=%%b"
)

set "SERVER_LOG=%LOG_DIR%\server.log"

if not exist "%SERVER_LOG%" (
    echo Log del servidor no encontrado: %SERVER_LOG%
    pause
    exit /b 1
)

echo === LOGS DEL SERVIDOR ===
echo Archivo: %SERVER_LOG%
echo.

REM Mostrar últimas 50 líneas
powershell -Command "Get-Content '%SERVER_LOG%' | Select-Object -Last 50"

echo.
pause

endlocal
