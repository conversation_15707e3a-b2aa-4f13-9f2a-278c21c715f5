<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Limpiar Todo</title>
</head>
<body>
    <h1>Limpiar Todo</h1>
    <button onclick="clearAll()">Limpiar localStorage e IndexedDB</button>
    <div id="output"></div>
    
    <script>
        function log(message) {
            console.log(message);
            document.getElementById('output').innerHTML += '<p>' + message + '</p>';
        }
        
        function clearAll() {
            log('Limpiando localStorage...');
            localStorage.clear();
            log('✅ localStorage limpiado');
            
            log('Limpiando IndexedDB...');
            
            // Eliminar la base de datos
            const deleteRequest = indexedDB.deleteDatabase('ipram_innerdb');
            
            deleteRequest.onsuccess = () => {
                log('✅ IndexedDB eliminado correctamente');
                log('🎉 Todo limpiado. Puedes cerrar esta página y recargar la aplicación');
            };
            
            deleteRequest.onerror = () => {
                log('❌ Error al eliminar IndexedDB: ' + deleteRequest.error);
            };
            
            deleteRequest.onblocked = () => {
                log('⚠️ La eliminación está bloqueada. Cierra todas las pestañas de la aplicación e inténtalo de nuevo.');
            };
        }
    </script>
</body>
</html>
