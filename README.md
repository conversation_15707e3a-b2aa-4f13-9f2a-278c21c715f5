# IPRA_I - Dashboard Colaborativo en Tiempo Real

Sistema de dashboards colaborativos con sincronización en tiempo real mediante WebSockets.

## 🚀 Características

- **Colaboración en tiempo real**: Múltiples usuarios pueden editar el mismo dashboard simultáneamente
- **Reconexión automática**: Sistema robusto de reconexión con backoff exponencial
- **Widgets dinámicos**: Soporte para diferentes tipos de widgets (texto, gráficas, etc.)
- **Almacenamiento local**: Usa IndexedDB para persistencia en el navegador
- **Build optimizado**: Sistema de minificación y combinación de archivos
- **Instalación desatendida**: Configuración simple y scripts automatizados

## 📋 Requisitos del Sistema

### Windows
- Node.js 16+ y npm
- Navegador moderno con soporte para WebSockets e IndexedDB

### Linux (Linux Mint 22, Ubuntu, Debian)
- Node.js 16+ y npm
- Permisos sudo para configuración del firewall

## 🛠️ Instalación Desatendida

### Paso 1: Editar configuración

Edita el archivo **`scripts/config.conf`** con tu configuración:

```bash
# Puerto del servidor
SERVER_PORT=3000

# Host del servidor
SERVER_HOST=localhost

# Configuración regional (es_ES o en_US)
LC_NUMERIC=es_ES

# Directorios (normalmente no necesitas cambiar)
BUILD_DIR=build
DOCS_DIR=docs_generated
LOG_DIR=logs
```

### Paso 2: Instalar

**Windows:**
```cmd
scripts\install.bat
```

**Linux:**
```bash
sudo scripts/install.sh
```

### Paso 3: Usar

**Iniciar servidor:**
```cmd
scripts\start.bat     # Windows
scripts/start.sh      # Linux
```

**Detener servidor:**
```cmd
scripts\stop.bat      # Windows
scripts/stop.sh       # Linux
```

## 🎯 Opciones Avanzadas

### Iniciar en background

**Windows:**
```cmd
scripts\start.bat -b
```

**Linux:**
```bash
scripts/start.sh -b
```

### Herramientas adicionales

Los siguientes scripts están disponibles en `scripts/aux/` para diagnóstico:

- `scripts/aux/status.bat` / `scripts/aux/status.sh` - Ver estado del servidor
- `scripts/aux/logs.bat` / `scripts/aux/logs.sh` - Ver logs del servidor

## 🔧 Build del Proyecto

```bash
# Build completo
npm run build
```

Los archivos se generan en el directorio `build/`:
- `aplicacion.js` - JavaScript combinado y minificado
- `aplicacion.css` - CSS combinado y minificado

## 🌐 Colaboración en Tiempo Real

- **Reconexión automática**: Si se pierde la conexión, el cliente reintenta automáticamente
- **Sincronización**: Los cambios se propagan inmediatamente a todos los usuarios
- **Indicadores visuales**: Muestra qué usuarios están conectados

## 📁 Estructura del Proyecto

```
ipra_i/
├── scripts/                 # Scripts de gestión
│   ├── config.conf         # ← EDITA ESTE ARCHIVO
│   ├── install.bat/.sh     # Instalación
│   ├── start.bat/.sh       # Iniciar servidor
│   ├── stop.bat/.sh        # Detener servidor
│   └── aux/                # Herramientas adicionales
├── backend/                # Servidor Node.js
│   └── server.js          # Servidor principal
├── js/                    # JavaScript del frontend
│   └── frontend.js        # Cliente WebSocket
├── build/                 # Archivos minificados
└── index.html            # Página principal
```

## ⚙️ Configuración

Edita **`scripts/config.conf`**:

```bash
SERVER_PORT=3000           # Puerto del servidor
SERVER_HOST=localhost      # Host del servidor
LC_NUMERIC=es_ES          # Regional: es_ES (1,23) o en_US (1.23)
```

## 🔍 Solución de Problemas

### El servidor no inicia
```bash
# Verificar puerto en uso
netstat -an | findstr :3000     # Windows
netstat -tuln | grep :3000      # Linux
```

### Ver estado y logs
```bash
scripts\aux\status.bat          # Windows
scripts/aux/status.sh           # Linux

scripts\aux\logs.bat            # Windows
scripts/aux/logs.sh             # Linux
```

## 🔄 Rebuild

```bash
npm run build
```
