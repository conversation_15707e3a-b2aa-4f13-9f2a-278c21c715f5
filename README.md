# IPRA_I - Dashboard Colaborativo en Tiempo Real

Sistema de dashboards colaborativos con sincronización en tiempo real mediante WebSockets.

## 🚀 Características

- **Colaboración en tiempo real**: Múltiples usuarios pueden editar el mismo dashboard simultáneamente
- **Reconexión automática**: Sistema robusto de reconexión con backoff exponencial
- **Widgets dinámicos**: Soporte para diferentes tipos de widgets (texto, gráficas, etc.)
- **Almacenamiento local**: Usa IndexedDB para persistencia en el navegador
- **Build optimizado**: Sistema de minificación y combinación de archivos
- **Documentación automática**: Generación de PDFs con WeasyPrint
- **Multiplataforma**: Scripts para Windows (.bat) y Linux (.sh)

## 📋 Requisitos del Sistema

### Windows
- Node.js 16+ y npm
- Python 3.7+ (opcional, para WeasyPrint)
- Navegador moderno con soporte para WebSockets e IndexedDB

### Linux (Linux Mint 22, Ubuntu, Debian)
- Node.js 16+ y npm
- Python 3.7+ (opcional, para WeasyPrint)
- Dependencias del sistema para WeasyPrint
- Permisos sudo para configuración del sistema

## 🛠️ Instalación

### Paso 1: Configuración

**Windows:**
```cmd
scripts\configure.bat
```

**Linux:**
```bash
scripts/configure.sh
```

Esto te permitirá configurar:
- Puerto del servidor (por defecto: 3000)
- Configuración regional (es_ES para números con coma, en_US para punto)
- Directorios de build, logs y documentación
- Configuración de WebSockets

### Paso 2: Instalación

**Windows:**
```cmd
scripts\install.bat
```

**Linux:**
```bash
sudo scripts/install.sh
```

Esto instalará:
- Node.js (si no está instalado)
- Dependencias del proyecto
- Python y WeasyPrint para documentación
- Configuración del firewall
- Servicio systemd (solo Linux)

## 🎯 Uso

### Iniciar el servidor

**Windows:**
```cmd
# Primer plano
scripts\start.bat

# Background
scripts\start.bat -b
```

**Linux:**
```bash
# Primer plano
scripts/start.sh

# Background
scripts/start.sh -b

# Con systemd
sudo systemctl start ipra-i
```

### Verificar estado

**Windows:**
```cmd
scripts\status.bat
```

**Linux:**
```bash
scripts/status.sh
```

### Ver logs

**Windows:**
```cmd
# Últimas 50 líneas
scripts\logs.bat

# Últimas 100 líneas
scripts\logs.bat -s 100

# Solo errores de las últimas 24 horas
scripts\logs.bat -e 24

# Buscar patrón
scripts\logs.bat -g "websocket" 20
```

**Linux:**
```bash
# Últimas 50 líneas
scripts/logs.sh

# Seguir logs en tiempo real
scripts/logs.sh -f

# Solo errores
scripts/logs.sh -e 12
```

### Detener el servidor

**Windows:**
```cmd
scripts\stop.bat
```

**Linux:**
```bash
scripts/stop.sh

# Con systemd
sudo systemctl stop ipra-i
```

## 📚 Documentación

### Generar documentación

**Windows:**
```cmd
# Toda la documentación
scripts\generate_docs.bat

# Solo manual de usuario
scripts\generate_docs.bat -u

# Solo documentación técnica
scripts\generate_docs.bat -t
```

**Linux:**
```bash
# Toda la documentación
scripts/generate_docs.sh

# Solo configuración actual
scripts/generate_docs.sh -c
```

La documentación se genera en:
- **HTML**: `docs_generated/html/`
- **PDF**: `docs_generated/pdf/`

## 🔧 Build del Proyecto

### Comandos de build

```bash
# Build completo
npm run build

# Solo JavaScript
npm run build:js

# Solo CSS
npm run build:css

# Watch mode (rebuild automático)
npm run watch

# Limpiar build
npm run clean
```

Los archivos se generan en el directorio `build/`:
- `aplicacion.js` - JavaScript combinado y minificado
- `aplicacion.css` - CSS combinado y minificado

## 🌐 Colaboración en Tiempo Real

### Características de WebSockets

- **Reconexión automática**: Si se pierde la conexión, el cliente reintenta automáticamente
- **Backoff exponencial**: Los reintentos aumentan progresivamente (1s, 2s, 4s, 8s...)
- **Cola de mensajes**: Los cambios se guardan durante desconexión y se envían al reconectar
- **Indicadores visuales**: Muestra qué usuarios están conectados
- **Sincronización**: Los cambios se propagan inmediatamente a todos los usuarios

### Eventos WebSocket

- `join_dashboard` - Unirse a un dashboard
- `leave_dashboard` - Salir de un dashboard
- `widget_update` - Actualizar widget existente
- `widget_create` - Crear nuevo widget
- `widget_delete` - Eliminar widget

## 📁 Estructura del Proyecto

```
ipra_i/
├── scripts/                 # Scripts de gestión
│   ├── config.conf         # Configuración centralizada
│   ├── configure.bat/.sh   # Configuración inicial
│   ├── install.bat/.sh     # Instalación
│   ├── start.bat/.sh       # Iniciar servidor
│   ├── stop.bat/.sh        # Detener servidor
│   ├── status.bat/.sh      # Estado del sistema
│   ├── logs.bat/.sh        # Gestión de logs
│   └── generate_docs.bat/.sh # Generar documentación
├── backend/                # Servidor Node.js
│   ├── server.js          # Servidor principal
│   └── package.json       # Dependencias del backend
├── js/                    # JavaScript del frontend
│   ├── frontend.js        # Cliente WebSocket
│   ├── app.js            # Aplicación principal
│   └── ...               # Otros módulos
├── css/                   # Estilos
├── build/                 # Archivos minificados
├── docs_generated/        # Documentación generada
├── logs/                  # Logs del sistema
├── package.json          # Dependencias del frontend
├── build.js              # Sistema de build
└── index.html            # Página principal
```

## ⚙️ Configuración

### Archivo de configuración (`scripts/config.conf`)

```bash
# Servidor
SERVER_PORT=3000
SERVER_HOST=localhost

# Regional
LC_NUMERIC=es_ES

# Directorios
BUILD_DIR=build
DOCS_DIR=docs_generated
LOG_DIR=logs

# WebSockets
WEBSOCKET_RECONNECT_INITIAL=1000
WEBSOCKET_RECONNECT_MAX=30000

# Logging
LOG_LEVEL=info
```

### Configuración regional

- **es_ES**: Números con coma decimal (1,23)
- **en_US**: Números con punto decimal (1.23)

## 🔍 Solución de Problemas

### El servidor no inicia

1. Verificar que el puerto no esté en uso:
   ```bash
   # Windows
   netstat -an | findstr :3000
   
   # Linux
   netstat -tuln | grep :3000
   ```

2. Verificar dependencias:
   ```bash
   scripts/status.sh  # Linux
   scripts\status.bat # Windows
   ```

### Problemas de WebSocket

1. Verificar firewall
2. Comprobar logs del servidor
3. Verificar conectividad de red

### Build falla

1. Verificar que UglifyJS esté instalado:
   ```bash
   npm list uglify-js
   ```

2. Verificar permisos de escritura en directorio `build/`

## 📞 Soporte

Para problemas o preguntas:

1. Revisar logs: `scripts/logs.sh -e 24`
2. Verificar estado: `scripts/status.sh`
3. Consultar documentación generada en `docs_generated/`

## 🔄 Actualizaciones

Para actualizar dependencias:

```bash
# Frontend
npm update

# Backend
cd backend && npm update
```

Para rebuild completo:

```bash
npm run clean
npm run build
```
