/**
 * Tema Neumorphic para iPRA
 * Un tema moderno con estilo neumórfico (soft UI)
 */

:root {
    /* Colores principales */
    --background-color: #e0e5ec;
    --surface-color: #e0e5ec;
    --primary-color: #4a6fa5;
    --primary-color-rgb: 74, 111, 165;
    /* RGB para efectos de transparencia */
    --accent-color: #6d5dfc;
    --text-color: #2d3748;
    --text-secondary-color: #4a5568;
    --border-color: #cbd5e0;
    --error-color: #e53e3e;
    --error-color-rgb: 229, 62, 62;
    --success-color: #38a169;

    /* Colores específicos para componentes */
    --gauge-value-color: #4a6fa5;
    --grid-lines: rgba(203, 213, 224, 0.8);

    /* Efectos */
    --shadow-small: 3px 3px 6px rgba(163, 177, 198, 0.5), -3px -3px 6px rgba(255, 255, 255, 0.8);
    --shadow-medium: 5px 5px 10px rgba(163, 177, 198, 0.5), -5px -5px 10px rgba(255, 255, 255, 0.8);
    --shadow-large: 10px 10px 20px rgba(163, 177, 198, 0.5), -10px -10px 20px rgba(255, 255, 255, 0.8);
    --shadow-inset: inset 2px 2px 5px rgba(163, 177, 198, 0.5), inset -2px -2px 5px rgba(255, 255, 255, 0.8);

    /* Tipografía */
    --font-family: 'Nunito', 'Segoe UI', Roboto, sans-serif;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-bold: 700;

    /* Bordes */
    --border-radius: 15px;
}

/* Para selección de widgets */
:root.theme-neumorphic {
    --selection-overlay: rgba(74, 111, 165, 0.1);
    --selection-border: transparent;
    --selection-check: var(--text-color);
    --selection-check-background: var(--surface-color);
}

.theme-neumorphic .widget.selected::before {
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-inset);
}

.theme-neumorphic .widget.selected::after {
    box-shadow: var(--shadow-small);
}

.theme-neumorphic .widget .widget-checkbox-container input[type="checkbox"] {
    width: 18px;
    height: 18px;
    border: none;
    border-radius: var(--border-radius);
    background-color: var(--surface-color);
    box-shadow: var(--shadow-small);
    cursor: pointer;
}

.theme-neumorphic .widget .widget-checkbox-container input[type="checkbox"]:checked {
    background-color: var(--primary-color);
    box-shadow: var(--shadow-inset);
}

:root.theme-neumorphic .widget-pasted {
    animation: widget-paste-flash-neumorphic 1s ease-out;
}

@keyframes widget-paste-flash-neumorphic {
    0% {
        box-shadow: var(--shadow-large);
        transform: scale(0.95);
        background-color: var(--surface-color);
    }

    50% {
        box-shadow: var(--shadow-medium);
        transform: scale(1.02);
    }

    100% {
        box-shadow: var(--shadow-small);
        transform: scale(1);
    }
}



/* Estilos generales */
.theme-neumorphic {
    font-family: var(--font-family);
    background-color: var(--background-color);
    color: var(--text-color);
}

.theme-neumorphic button {
    background-color: var(--surface-color);
    color: var(--text-color);
    border: none;
    border-radius: var(--border-radius);
    padding: 10px 15px;
    font-weight: var(--font-weight-medium);
    box-shadow: var(--shadow-small);
    transition: all 0.3s ease;
}

.theme-neumorphic button:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.theme-neumorphic button:active {
    box-shadow: var(--shadow-inset);
    transform: translateY(0);
}

.theme-neumorphic button:disabled {
    background-color: #e2e8f0;
    color: #a0aec0;
    box-shadow: var(--shadow-inset);
    cursor: not-allowed;
    opacity: 0.7;
    transform: none;
}

.theme-neumorphic button.primary-btn {
    background-color: var(--primary-color);
    color: white;
}

.theme-neumorphic button.danger-btn {
    background-color: var(--error-color);
    color: white;
}

.theme-neumorphic input[type="text"],
.theme-neumorphic input[type="number"],
.theme-neumorphic input[type="color"],
.theme-neumorphic select,
.theme-neumorphic textarea {
    background-color: var(--surface-color);
    border: none;
    border-radius: var(--border-radius);
    padding: 10px 15px;
    box-shadow: var(--shadow-inset);
    color: var(--text-color);
}

.theme-neumorphic input[type="color"] {
    height: 40px;
    padding: 5px;
}

.theme-neumorphic select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234a5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 20px;
    padding-right: 40px;
}

/* Header */
.theme-neumorphic header {
    background-color: var(--surface-color);
    box-shadow: var(--shadow-small);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    padding: 0 15px;
    /* Añadir padding horizontal */
}

/* Botón de opciones */
.theme-neumorphic #options-btn {
    background-color: var(--surface-color);
    color: var(--primary-color);
    font-weight: var(--font-weight-medium);
    border-radius: var(--border-radius);
    padding: 10px 20px;
    margin: 10px 5px;
    box-shadow: var(--shadow-small);
    transition: all 0.3s ease;
}

.theme-neumorphic #options-btn:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
    background-color: rgba(74, 111, 165, 0.1);
}

.theme-neumorphic #options-btn:active {
    box-shadow: var(--shadow-inset);
    transform: translateY(0);
}

.theme-neumorphic header h1 {
    color: var(--primary-color);
    font-weight: var(--font-weight-bold);
    padding-left: 10px;
    /* Padding adicional */
}

.theme-neumorphic .header-title-container {
    margin-left: 20px;
}

.theme-neumorphic .dashboard-quick-actions {
    margin-top: 8px;
}

.theme-neumorphic .icon-button {
    background-color: var(--surface-color);
    color: var(--primary-color);
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-small);
    transition: all 0.3s ease;
}

.theme-neumorphic .icon-button:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
    background-color: rgba(74, 111, 165, 0.1);
}

.theme-neumorphic .icon-button:active {
    box-shadow: var(--shadow-inset);
    transform: translateY(0);
}

.theme-neumorphic .dropdown-menu {
    background-color: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    padding: 10px 5px;
    /* Más padding interno */
    border: 1px solid rgba(203, 213, 224, 0.5);
    /* Borde sutil */
}

.theme-neumorphic .dropdown-menu button {
    box-shadow: none;
    border-radius: var(--border-radius);
    text-align: left;
    margin: 5px 8px;
    /* Margen entre botones */
    padding: 12px 15px;
    /* Más padding para botones más grandes */
    width: calc(100% - 16px);
    /* Ancho ajustado para respetar márgenes */
    font-size: 14px;
    /* Tamaño de fuente ligeramente mayor */
    position: relative;
    /* Para efectos de hover */
    overflow: hidden;
    /* Para efectos de hover */
}

.theme-neumorphic .dropdown-menu button:hover {
    background-color: rgba(74, 111, 165, 0.15);
    /* Color primario con transparencia */
    color: var(--primary-color);
    /* Texto en color primario */
    box-shadow: var(--shadow-inset);
    /* Efecto hundido */
    transform: translateY(1px);
    /* Ligero movimiento hacia abajo */
    font-weight: var(--font-weight-medium);
    /* Texto un poco más grueso */
    transition: all 0.2s ease;
    /* Transición más rápida */
}

/* Dashboard */
.theme-neumorphic .dashboard {
    background-color: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    background-image: none;
    /* Sin cuadrícula por defecto */
}

/* Dashboard con cuadrícula */
.theme-neumorphic .dashboard.show-grid {
    background-image:
        linear-gradient(0deg, var(--grid-lines) 1px, transparent 1px),
        linear-gradient(90deg, var(--grid-lines) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Widgets */
.theme-neumorphic .widget {
    background-color: var(--surface-color);
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-small);
    transition: all 0.3s ease;
}

/* Manejador de redimensionamiento */
.theme-neumorphic .widget .resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 15px;
    height: 15px;
    cursor: nwse-resize;
    background: transparent;
    z-index: 10;
    display: none;
    /* Oculto por defecto */
}

.theme-neumorphic .widget .resize-handle::before {
    content: '';
    position: absolute;
    bottom: 3px;
    right: 3px;
    width: 8px;
    height: 8px;
    border-right: 2px solid var(--border-color);
    border-bottom: 2px solid var(--border-color);
    opacity: 0.7;
}

.theme-neumorphic .edit-mode .widget .resize-handle {
    display: block;
    /* Visible en modo edición */
}

/* Aplicar bordes y sombra solo cuando está activada la opción */
.theme-neumorphic .show-widget-borders .widget {
    border: 1px solid var(--border-color);
}

/* Efecto hover */
.theme-neumorphic .widget:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.theme-neumorphic .text-widget .widget-content {
    color: var(--text-color);
    font-weight: var(--font-weight-medium);
}

.theme-neumorphic .value-widget .widget-content {
    color: var(--gauge-value-color);
    font-size: 1.8rem;
    font-weight: var(--font-weight-bold);
}

/* Gauges */
.theme-neumorphic .gauge-arc.gauge-background {
    stroke: rgba(203, 213, 224, 0.5);
}

.theme-neumorphic .gauge-arc.gauge-foreground {
    stroke: var(--primary-color);
}

.theme-neumorphic .gauge-value {
    color: var(--gauge-value-color);
    font-size: 1.2rem;
    font-weight: var(--font-weight-bold);
}

.theme-neumorphic .percentage-title {
    color: var(--text-secondary-color);
    font-size: 0.9rem;
    font-weight: var(--font-weight-medium);
}

.theme-neumorphic .percentage-value {
    color: var(--gauge-value-color);
    font-size: 1.8rem;
    font-weight: var(--font-weight-bold);
}

.theme-neumorphic .percentage-gauge {
    background-color: var(--surface-color);
    border-radius: 10px;
    height: 20px;
    box-shadow: var(--shadow-inset);
    width: 80%;
}

.theme-neumorphic .percentage-gauge-fill {
    background-color: var(--primary-color);
    background-image: linear-gradient(45deg,
            rgba(255, 255, 255, 0.15) 25%,
            transparent 25%,
            transparent 50%,
            rgba(255, 255, 255, 0.15) 50%,
            rgba(255, 255, 255, 0.15) 75%,
            transparent 75%,
            transparent);
    background-size: 20px 20px;
    border-radius: 10px;
    transition: width 0.5s ease;
}

/* Modales */
.theme-neumorphic .modal {
    backdrop-filter: blur(5px);
}

.theme-neumorphic .modal-content {
    background-color: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-large);
    padding: 25px;
    /* Más padding interno */
    border: 1px solid rgba(203, 213, 224, 0.5);
    /* Borde sutil */
}

.theme-neumorphic .modal-content h2 {
    color: var(--primary-color);
    margin-bottom: 10px;
    /* Reducido de 20px a 10px */
    margin-top: 0;
    padding-bottom: 8px;
    /* Reducido de 10px a 8px */
    border-bottom: 1px solid rgba(203, 213, 224, 0.5);
}

.theme-neumorphic .close-btn {
    color: var(--text-secondary-color);
    font-size: 24px;
    transition: all 0.3s ease;
}

.theme-neumorphic .close-btn:hover {
    color: var(--primary-color);
    transform: rotate(90deg);
}

/* Diálogo de confirmación de eliminación */
.theme-neumorphic .delete-confirm-dialog .dialog-content {
    border: 2px solid var(--error-color);
    background-color: rgba(var(--error-color-rgb), 0.03);
    box-shadow: var(--shadow-large), 0 0 10px rgba(var(--error-color-rgb), 0.2);
    border-radius: var(--border-radius);
}

.theme-neumorphic .delete-confirm-dialog .dialog-header {
    background-color: rgba(var(--error-color-rgb), 0.05);
    border-bottom: 1px solid rgba(var(--error-color-rgb), 0.1);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.theme-neumorphic .delete-confirm-dialog .dialog-header h3 {
    color: var(--error-color);
    font-weight: var(--font-weight-bold);
}

/* Botón de eliminar en el diálogo de confirmación */
.theme-neumorphic .delete-confirm-dialog .danger-btn {
    background-color: var(--error-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 12px 20px;
    font-weight: var(--font-weight-medium);
    box-shadow: 5px 5px 10px rgba(var(--error-color-rgb), 0.2), -5px -5px 10px rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.theme-neumorphic .delete-confirm-dialog .danger-btn:hover {
    background-color: #c53030;
    /* Rojo más oscuro */
    transform: translateY(-2px);
    box-shadow: 7px 7px 15px rgba(var(--error-color-rgb), 0.3), -7px -7px 15px rgba(255, 255, 255, 0.8);
}

.theme-neumorphic .delete-confirm-dialog .danger-btn:active {
    box-shadow: inset 2px 2px 5px rgba(var(--error-color-rgb), 0.5), inset -2px -2px 5px rgba(255, 255, 255, 0.2);
    transform: translateY(0);
}

/* Popup de opciones */
.theme-neumorphic .popup-menu {
    backdrop-filter: blur(5px);
}

.theme-neumorphic .popup-content {
    background-color: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-large);
    padding: 25px;
    border: 1px solid rgba(203, 213, 224, 0.5);
}

.theme-neumorphic .popup-content h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(203, 213, 224, 0.5);
    font-weight: var(--font-weight-bold);
}

.theme-neumorphic .popup-option {
    background-color: var(--surface-color);
    color: var(--text-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-small);
    margin-bottom: 12px;
    padding: 12px 20px;
    font-weight: var(--font-weight-medium);
    transition: all 0.3s ease;
}

.theme-neumorphic .popup-option.selected {
    background-color: rgba(74, 111, 165, 0.2);
    border-left: 4px solid var(--primary-color);
    box-shadow: var(--shadow-inset);
    color: var(--primary-color);
    font-weight: var(--font-weight-bold);
    border-radius: var(--border-radius);
    transform: translateY(0);
    position: relative;
}

.theme-neumorphic .popup-option.selected::after {
    content: '';
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background-color: var(--primary-color);
    border-radius: 50%;
}

.theme-neumorphic .popup-option.selected:hover {
    background-color: rgba(74, 111, 165, 0.3);
    transform: translateY(0);
}

.theme-neumorphic .popup-option:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
    background-color: rgba(74, 111, 165, 0.1);
}

.theme-neumorphic .popup-option:active {
    box-shadow: var(--shadow-inset);
    transform: translateY(0);
}

.theme-neumorphic .popup-option.danger-option {
    color: var(--error-color);
    font-weight: var(--font-weight-bold);
}

.theme-neumorphic .popup-option.danger-option:hover {
    background-color: rgba(229, 62, 62, 0.1);
}

/* Botones en modales */
.theme-neumorphic .button-group {
    display: flex;
    justify-content: space-between;
    margin-top: 25px;
}

.theme-neumorphic .button-group button {
    padding: 12px 20px;
    min-width: 100px;
}

.theme-neumorphic .primary-btn {
    background-color: var(--primary-color);
    color: white;
    font-weight: var(--font-weight-medium);
}

.theme-neumorphic .primary-btn:hover {
    background-color: var(--accent-color);
    transform: translateY(-2px);
}

.theme-neumorphic .primary-btn:disabled {
    background-color: rgba(74, 111, 165, 0.5);
    color: rgba(255, 255, 255, 0.7);
    box-shadow: var(--shadow-inset);
    transform: none;
}

.theme-neumorphic .danger-btn {
    background-color: var(--error-color);
    color: white;
}

.theme-neumorphic .danger-btn:hover {
    background-color: #c53030;
    /* Rojo más oscuro */
    transform: translateY(-2px);
}

/* Modo edición */
.theme-neumorphic .edit-mode .widget:hover {
    border: 2px dashed var(--primary-color);
    box-shadow: var(--shadow-medium);
}

.theme-neumorphic .delete-mode .widget:hover {
    border: 2px dashed var(--error-color);
    box-shadow: var(--shadow-medium);
}

/* Estilo para el widget durante el arrastre */
.theme-neumorphic .widget.dragging {
    opacity: 0.8;
    z-index: 100;
    box-shadow: var(--shadow-large);
}

/* Checkbox personalizado */
.theme-neumorphic .checkbox-group {
    display: flex;
    align-items: center;
}

.theme-neumorphic .checkbox-group input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    background-color: var(--surface-color);
    border-radius: 5px;
    box-shadow: var(--shadow-inset);
    position: relative;
    margin-right: 10px;
    cursor: pointer;
}

.theme-neumorphic .checkbox-group input[type="checkbox"]:checked {
    background-color: var(--primary-color);
    box-shadow: var(--shadow-inset);
}

.theme-neumorphic .checkbox-group input[type="checkbox"]:checked::before {
    content: '✓';
    position: absolute;
    color: white;
    font-size: 14px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.theme-neumorphic .checkbox-group label {
    color: var(--text-secondary-color);
}

/* Scrollbar personalizada */
.theme-neumorphic ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

.theme-neumorphic ::-webkit-scrollbar-track {
    background: var(--surface-color);
    border-radius: 5px;
}

.theme-neumorphic ::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 5px;
    box-shadow: var(--shadow-inset);
}

.theme-neumorphic ::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Transparencia de widgets */
.theme-neumorphic .transparent-widgets .widget {
    background-color: transparent;
    box-shadow: none;
}