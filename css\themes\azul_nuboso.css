/* Tema Azul Nuboso - Variante azul del tema por defecto */

/* Variables del tema */
:root.theme-azul_nuboso {
    --primary-color: #4a6fa5;
    --primary-color-rgb: 74, 111, 165;
    /* RGB para efectos de transparencia */
    --secondary-color: #3a5a8c;
    --accent-color: #4a6fa5;
    --background-color: #f5f5f5;
    --surface-color: #ffffff;
    --text-color: #333333;
    --text-secondary-color: #555555;
    --border-color: #dddddd;
    --success-color: #4a6fa5;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --error-color-rgb: 244, 67, 54;
    --shadow-color: rgba(0, 0, 0, 0.1);

    /* Fuentes */
    --font-family: Arial, sans-serif;
    --font-weight-normal: 400;
    --font-weight-bold: 700;
}

/* Para selección de widgets */
:root.theme-azul_nuboso {
    --selection-overlay: rgba(74, 111, 165, 0.2);
    --selection-border: var(--primary-color);
    --selection-check: var(--surface-color);
    --selection-check-background: var(--primary-color);
}

.theme-azul_nuboso .widget.selected::before {
    box-shadow: 0 2px 10px var(--shadow-color);
}

.theme-azul_nuboso .widget.selected::after {
    box-shadow: 0 2px 4px var(--shadow-color);
}

.theme-azul_nuboso .widget .widget-checkbox-container input[type="checkbox"] {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 3px;
    background-color: var(--surface-color);
    cursor: pointer;
}

.theme-azul_nuboso .widget .widget-checkbox-container input[type="checkbox"]:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

:root.theme-azul_nuboso .widget-pasted {
    animation: widget-paste-flash-azul-nuboso 1s ease-out;
}

@keyframes widget-paste-flash-azul-nuboso {
    0% {
        box-shadow: 0 0 15px rgba(var(--primary-color-rgb), 0.4);
        transform: scale(0.95);
        background-color: rgba(var(--primary-color-rgb), 0.1);
    }

    50% {
        box-shadow: 0 0 8px rgba(var(--primary-color-rgb), 0.6);
        transform: scale(1.02);
        background-color: rgba(var(--primary-color-rgb), 0.05);
    }

    100% {
        box-shadow: 0 2px 10px var(--shadow-color);
        transform: scale(1);
        background-color: transparent;
    }
}


/* Estilos generales */
.theme-azul_nuboso body {
    background-color: var(--background-color);
    color: var(--text-color);
    font-family: var(--font-family);
}

/* Contenedores */
.theme-azul_nuboso .container {
    background-color: transparent;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 10px var(--shadow-color);
    border-radius: 8px;
}

/* Excepción para el contenedor de login */
.theme-azul_nuboso #login-container {
    background-color: transparent;
    border: none;
    box-shadow: none;
}

/* Login */
.theme-azul_nuboso #login-container h1 {
    color: var(--text-color);
    font-size: 2rem;
    margin-bottom: 1.5rem;
}

.theme-azul_nuboso .login-form {
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 2px 10px var(--shadow-color);
    padding: 30px;
}

.theme-azul_nuboso .login-form h2 {
    color: var(--text-color);
    margin-bottom: 20px;
}

.theme-azul_nuboso .login-form input {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 10px;
    width: 100%;
}

.theme-azul_nuboso .login-form input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 5px rgba(var(--primary-color-rgb), 0.3);
}

.theme-azul_nuboso #login-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 15px;
    cursor: pointer;
    font-weight: var(--font-weight-bold);
    transition: background-color 0.3s ease;
}

.theme-azul_nuboso #login-btn:hover {
    background-color: var(--secondary-color);
}

/* Menú principal */
.theme-azul_nuboso .main-menu-container {
    padding: 20px;
}

.theme-azul_nuboso .main-menu {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.theme-azul_nuboso .menu-button {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 2px 5px var(--shadow-color);
}

.theme-azul_nuboso .menu-button:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px var(--shadow-color);
}

.theme-azul_nuboso .menu-button .icon {
    font-size: 2rem;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.theme-azul_nuboso .menu-button .title {
    font-weight: var(--font-weight-bold);
    color: var(--text-color);
}

/* Header */
.theme-azul_nuboso header {
    background-color: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.theme-azul_nuboso #main-menu-container header {
    margin-bottom: 0;
    margin-top: 40px;
    background-color: var(--primary-color);
    border-bottom: 3px solid lightskyblue;
}

.theme-azul_nuboso header h1 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--text-color);
    margin-bottom: 5px;
}

.theme-azul_nuboso #main-menu-container header h1 {
    color: lightblue;

}

.theme-azul_nuboso .logout-btn {
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Estilo específico para el botón de cerrar sesión en el menú principal */
.theme-azul_nuboso #main-menu-logout-btn {
    background-color: #a8d0f0;
    width: 24px;
    height: 24px;
    font-size: 1rem;
    color: var(--secondary-color);
}


.theme-azul_nuboso .logout-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

.theme-azul_nuboso #main-menu-logout-btn:hover {
    background-color: #7eb0f9;
}

/* Tablas de entidades */
.theme-azul_nuboso .entity-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    background-color: var(--surface-color);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px var(--shadow-color);
}

.theme-azul_nuboso .entity-table .checkbox-cell {
    width: 40px;
    vertical-align: middle;
    text-align: center;
    padding-left: 0;
    padding-right: 0;
}

.theme-azul_nuboso .entity-table .action-cell {
    width: 40px;
    vertical-align: middle;
    text-align: center;
    padding-left: 0;
    padding-right: 0;
}

.theme-azul_nuboso .entity-table th {
    background-color: var(--primary-color);
    color: white;
    font-weight: var(--font-weight-bold);
    text-align: left;
    padding: 12px 15px;
    border-bottom: 2px solid var(--border-color);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.theme-azul_nuboso .entity-table th:last-child {
    border-right: none;
}

.theme-azul_nuboso .entity-table td {
    padding: 10px 15px;
    border-bottom: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);
}

.theme-azul_nuboso .entity-table td:last-child {
    border-right: none;
}

.theme-azul_nuboso .entity-table tr:last-child td {
    border-bottom: none;
}

.theme-azul_nuboso .entity-table tr:hover td {
    background-color: rgba(var(--primary-color-rgb), 0.05);
}

/* Filtros de entidades */
.theme-azul_nuboso .entity-filter-container {
    margin-bottom: 20px;
    position: relative;
}

.theme-azul_nuboso .entity-filter {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
}

.theme-azul_nuboso .entity-filter:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 5px rgba(var(--primary-color-rgb), 0.3);
}

.theme-azul_nuboso .clear-filter-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary-color);
    cursor: pointer;
}

.theme-azul_nuboso .clear-filter-btn:hover {
    color: var(--primary-color);
}

/* Botones de acción */
.theme-azul_nuboso .action-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 15px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.theme-azul_nuboso .action-btn:hover {
    background-color: var(--secondary-color);
}

/* Dashboard con cuadrícula */
.theme-azul_nuboso .dashboard.show-grid {
    background-image: linear-gradient(var(--grid-color, #dddddd) 1px, transparent 1px),
        linear-gradient(90deg, var(--grid-color, #dddddd) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Widgets */
.theme-azul_nuboso .widget {
    background-color: #f9f9f9;
    border-radius: 4px;
    padding: 10px;
    box-shadow: 0 1px 3px var(--shadow-color);
    position: absolute !important;
    /* Forzar posición absoluta para permitir movimiento */
}

/* Manejador de redimensionamiento */
.theme-azul_nuboso .widget .resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 15px;
    height: 15px;
    cursor: nwse-resize;
    background: transparent;
    z-index: 10;
    display: none;
    /* Oculto por defecto */
}

.theme-azul_nuboso .widget:hover .resize-handle {
    display: block;
    /* Mostrar al pasar el ratón */
}

.theme-azul_nuboso .widget .resize-handle::after {
    content: "";
    position: absolute;
    bottom: 3px;
    right: 3px;
    width: 8px;
    height: 8px;
    border-right: 2px solid var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
}

/* Modales */
.theme-azul_nuboso .modal-content {
    background-color: var(--surface-color);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.theme-azul_nuboso .modal-content h2 {
    margin-bottom: 10px;
    /* Reducido de 20px a 10px */
    margin-top: 0;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
    /* Reducido de 10px a 8px */
}

.theme-azul_nuboso .close-btn {
    color: var(--text-color);
    transition: all 0.3s ease;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-azul_nuboso .close-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Botones */
.theme-azul_nuboso .primary-btn {
    background-color: var(--primary-color);
    color: white;
}

.theme-azul_nuboso .primary-btn:hover {
    background-color: var(--secondary-color);
}

.theme-azul_nuboso .danger-btn {
    background-color: var(--error-color);
    color: white;
}

.theme-azul_nuboso .danger-btn:hover {
    background-color: #d32f2f;
    /* Rojo más oscuro */
}

/* Botón de eliminar (cruz) en la cabecera del diálogo de editar widget */
.theme-azul_nuboso .icon-action-btn.danger-icon-btn {
    background-color: var(--error-color);
    color: white;
    border: 1px solid #d32f2f;
}

.theme-azul_nuboso .icon-action-btn.danger-icon-btn:hover {
    background-color: #d32f2f;
    box-shadow: 0 3px 6px rgba(211, 47, 47, 0.3);
    transform: translateY(-2px);
}

/* Estilo específico para los botones "Defecto" en el tema azul nuboso */
.theme-azul_nuboso .reset-color-btn {
    color: #333333;
    font-weight: bold;
}

/* Asegurar que las etiquetas de checkbox en los diálogos de añadir y editar widget tengan el color gris */
.theme-azul_nuboso #edit-widget-modal .checkbox-group label,
.theme-azul_nuboso #add-widget-modal .checkbox-group label {
    color: gray !important;
}

/* Transparencia de widgets */
.theme-azul_nuboso .transparent-widgets .widget {
    background-color: transparent;
    box-shadow: none;
}

/* Título del widget */
.theme-azul_nuboso .widget-title {
    font-weight: var(--font-weight-bold);
    margin-bottom: 10px;
    color: var(--text-color);
    font-size: 14px;
    text-align: center;
}

/* Valor del widget */
.theme-azul_nuboso .widget-value {
    font-size: 24px;
    font-weight: var(--font-weight-bold);
    text-align: center;
    color: var(--primary-color);
}

/* Unidad del widget */
.theme-azul_nuboso .widget-unit {
    font-size: 14px;
    color: var(--text-secondary-color);
    text-align: center;
}

/* Gauges */
.theme-azul_nuboso .gauge-arc.gauge-background {
    stroke: #eee;
}

.theme-azul_nuboso .gauge-arc.gauge-foreground {
    stroke: var(--primary-color);
}

.theme-azul_nuboso .gauge-value {
    font-weight: var(--font-weight-bold);
}

.theme-azul_nuboso .percentage-title {
    font-size: 16px;
    color: var(--text-secondary-color);
    margin-bottom: 5px;
}

.theme-azul_nuboso .percentage-value {
    font-size: 28px;
    font-weight: var(--font-weight-bold);
    margin-bottom: 15px;
}

.theme-azul_nuboso .percentage-gauge {
    background-color: #eee;
    border-radius: 15px;
    height: 30px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.theme-azul_nuboso .percentage-gauge-fill {
    background-color: var(--primary-color);
    background-image: linear-gradient(45deg,
            rgba(255, 255, 255, 0.15) 25%,
            transparent 25%,
            transparent 50%,
            rgba(255, 255, 255, 0.15) 50%,
            rgba(255, 255, 255, 0.15) 75%,
            transparent 75%,
            transparent);
    background-size: 40px 40px;
    border-radius: 15px;
}


#dashboard-menu #options-btn:hover {
    background-color: var(--secondary-color);
}

.theme-azul_nuboso #dashboard-menu #options-btn {
    background-color: var(--primary-color);
}


.theme-azul_nuboso .dropdown-menu button:hover {
    background-color: var(--accent-color);
    color: var(--background-color);
}