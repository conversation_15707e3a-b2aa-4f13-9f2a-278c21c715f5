@echo off
REM Script para iniciar el servidor IPRA_I (Windows)

setlocal enabledelayedexpansion

REM Colores para Windows
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "NC=[0m"

REM Función para logging
:log
echo %GREEN%[%date% %time%] %~1%NC%
goto :eof

:warn
echo %YELLOW%[%date% %time%] WARNING: %~1%NC%
goto :eof

:error
echo %RED%[%date% %time%] ERROR: %~1%NC%
exit /b 1

REM Cargar configuración
set "CONFIG_FILE=%~dp0config.conf"
if not exist "%CONFIG_FILE%" (
    call :error "Archivo config.conf no encontrado. Ejecuta scripts\configure.bat primero."
    exit /b 1
)

REM Leer configuración
for /f "tokens=1,2 delims==" %%a in ('type "%CONFIG_FILE%" ^| findstr /v "^#" ^| findstr "="') do (
    set "%%a=%%b"
)

REM Configurar entorno regional
if not "%LC_NUMERIC%"=="" (
    set "LC_NUMERIC=%LC_NUMERIC%"
    call :log "Configuración regional: LC_NUMERIC=%LC_NUMERIC%"
)

call :log "=== INICIANDO SERVIDOR IPRA_I ==="

REM Verificar configuración
if "%SERVER_PORT%"=="" (
    call :error "SERVER_PORT no configurado en config.conf"
    exit /b 1
)

REM Verificar dependencias
call :log "Verificando dependencias..."

node --version >nul 2>&1
if errorlevel 1 (
    call :error "Node.js no está instalado. Ejecuta scripts\install.bat primero."
    exit /b 1
)

if not exist "backend\node_modules" (
    call :error "Dependencias del backend no instaladas. Ejecuta scripts\install.bat primero."
    exit /b 1
)

if not exist "backend\server.js" (
    call :error "Archivo del servidor no encontrado: backend\server.js"
    exit /b 1
)

call :log "Dependencias verificadas correctamente"

REM Crear directorios de logs
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"
if not exist "backend\logs" mkdir "backend\logs"
call :log "Directorios de logs creados"

REM Verificar si el puerto está en uso
call :log "Verificando puerto %SERVER_PORT%..."
netstat -an | findstr ":%SERVER_PORT% " >nul 2>&1
if not errorlevel 1 (
    call :warn "El puerto %SERVER_PORT% parece estar en uso"
    set /p "CONTINUE=¿Continuar de todas formas? (y/N): "
    if /i not "!CONTINUE!"=="y" (
        exit /b 1
    )
)

REM Procesar argumentos
set "START_MODE=foreground"
if "%~1"=="-b" set "START_MODE=background"
if "%~1"=="--background" set "START_MODE=background"
if "%~1"=="-h" goto :show_help
if "%~1"=="--help" goto :show_help

if "%START_MODE%"=="background" (
    call :start_background
) else (
    call :start_foreground
)

goto :eof

:start_foreground
call :log "Iniciando servidor en primer plano..."
call :log "Presiona Ctrl+C para detener el servidor"
call :log "URL: http://%SERVER_HOST%:%SERVER_PORT%"

REM Cambiar al directorio del backend
cd backend

REM Ejecutar servidor
node server.js

goto :eof

:start_background
call :log "Iniciando servidor en background..."

REM Verificar si ya está ejecutándose
if exist "%LOG_DIR%\ipra-i.pid" (
    set /p "EXISTING_PID=" < "%LOG_DIR%\ipra-i.pid"
    tasklist /FI "PID eq !EXISTING_PID!" 2>nul | findstr "!EXISTING_PID!" >nul
    if not errorlevel 1 (
        call :warn "El servidor ya está ejecutándose (PID: !EXISTING_PID!)"
        exit /b 1
    ) else (
        del "%LOG_DIR%\ipra-i.pid" 2>nul
    )
)

REM Cambiar al directorio del backend
cd backend

REM Iniciar servidor en background usando start
start /B node server.js > "..\%LOG_DIR%\server.log" 2>&1

REM Obtener PID del proceso (aproximado en Windows)
timeout /t 2 /nobreak >nul

REM Buscar el proceso Node.js más reciente
for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq node.exe" /FO CSV ^| findstr "node.exe" ^| tail -1') do (
    set "SERVER_PID=%%~i"
)

if not "%SERVER_PID%"=="" (
    echo %SERVER_PID% > "..\%LOG_DIR%\ipra-i.pid"
    call :log "Servidor iniciado en background (PID: %SERVER_PID%)"
    call :log "URL: http://%SERVER_HOST%:%SERVER_PORT%"
    call :log "Logs: %LOG_DIR%\server.log"
    call :log "Para detener: scripts\stop.bat"
) else (
    call :error "Error al iniciar el servidor en background"
    exit /b 1
)

goto :eof

:show_help
echo Uso: %~nx0 [OPCIONES]
echo.
echo Opciones:
echo   -h, --help       Mostrar esta ayuda
echo   -b, --background Iniciar en background
echo   -f, --foreground Iniciar en primer plano (por defecto)
echo.
echo Ejemplos:
echo   %~nx0               # Iniciar en primer plano
echo   %~nx0 -b            # Iniciar en background
echo.
goto :eof

endlocal
