@echo off
REM Script para iniciar el servidor IPRA_I (Windows)

setlocal enabledelayedexpansion

echo === INICIANDO SERVIDOR IPRA_I ===

REM Cargar configuración
set "CONFIG_FILE=%~dp0config.conf"
if not exist "%CONFIG_FILE%" (
    echo ERROR: Archivo scripts/config.conf no encontrado.
    pause
    exit /b 1
)

REM Leer configuración
for /f "tokens=1,2 delims==" %%a in ('type "%CONFIG_FILE%" ^| findstr /v "^#" ^| findstr "="') do (
    set "%%a=%%b"
)

echo Puerto: %SERVER_PORT%
echo URL: http://%SERVER_HOST%:%SERVER_PORT%
echo.

REM Verificar si ya está ejecutándose
netstat -an | findstr ":%SERVER_PORT% " >nul 2>&1
if not errorlevel 1 (
    echo El puerto %SERVER_PORT% ya está en uso
    echo ¿El servidor ya está ejecutándose?
    pause
    exit /b 1
)

REM Crear directorios de logs
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

REM Procesar argumentos
if "%~1"=="-b" goto :start_background
if "%~1"=="--background" goto :start_background

REM Iniciar en primer plano (por defecto)
echo Iniciando servidor en primer plano...
echo Presiona Ctrl+C para detener
echo.
cd backend
node server.js
goto :eof

:start_background
echo Iniciando servidor en background...

REM Cambiar al directorio del backend
cd backend

REM Iniciar servidor en background
start /B node server.js > "..\%LOG_DIR%\server.log" 2>&1

echo Servidor iniciado en background
echo Logs: %LOG_DIR%\server.log
echo Para detener: scripts\stop.bat
pause

endlocal
