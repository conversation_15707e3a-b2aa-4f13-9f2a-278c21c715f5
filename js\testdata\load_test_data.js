/**
 * Script para cargar datos de prueba en la aplicación
 */

// Función para cargar el script de generación de datos
function loadTestData() {
    // Verificar si ya existen datos
    const existingCompanies = localStorage.getItem('companies');
    if (existingCompanies) {
        const companies = JSON.parse(existingCompanies);
        if (companies.length >= 500) {
            console.log(`Ya existen ${companies.length} empresas en localStorage.`);
            return;
        } else {
            console.log(`Existen ${companies.length} empresas en localStorage, generando hasta 500...`);
        }
    }

    // Cargar el script de generación de datos
    const script = document.createElement('script');
    script.src = 'js/testdata/generate_test_data.js';
    script.onload = function () {
        // Generar empresas si hay menos de 500
        if (typeof saveTestCompanies === 'function') {
            const existingCompanies = localStorage.getItem('companies');
            if (!existingCompanies) {
                saveTestCompanies(500);
            } else {
                const companies = JSON.parse(existingCompanies);
                if (companies.length < 500) {
                    saveTestCompanies(500);
                }
            }
            console.log('Datos de prueba cargados correctamente.');
        } else {
            console.error('La función saveTestCompanies no está disponible.');
        }
    };
    script.onerror = function () {
        console.error('Error al cargar el script de datos de prueba.');
    };
    document.body.appendChild(script);
}

// Ejecutar la función al cargar la página
document.addEventListener('DOMContentLoaded', loadTestData);