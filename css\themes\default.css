/* Tema por defecto - Estilo original de la aplicación */

/* Variables del tema */
:root.theme-default {
    --primary-color: #4CAF50;
    --primary-color-rgb: 76, 175, 80;
    --primary-hover-color: #388E3C;
    /* RGB para efectos de transparencia */
    --secondary-color: #45a049;
    --accent-color: #4CAF50;
    --background-color: #f5f5f5;
    --surface-color: #ffffff;
    --text-color: #333333;
    --text-secondary-color: #555555;
    --border-color: #dddddd;
    --success-color: #4CAF50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --error-color-rgb: 244, 67, 54;
    --shadow-color: rgba(0, 0, 0, 0.1);

    /* Fuentes */
    --font-family: Arial, sans-serif;
    --font-weight-normal: 400;
    --font-weight-bold: 700;
}

/* Para seleccionar widgets */
:root.theme-default {
    --selection-overlay: rgba(76, 175, 80, 0.2);
    --selection-border: var(--primary-color);
    --selection-check: var(--surface-color);
    --selection-check-background: var(--primary-color);
}

.theme-default .widget.selected::before {
    box-shadow: 0 2px 10px var(--shadow-color);
}

.theme-default .widget.selected::after {
    box-shadow: 0 2px 4px var(--shadow-color);
}

.theme-default .widget .widget-checkbox-container input[type="checkbox"] {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 3px;
    background-color: var(--surface-color);
    cursor: pointer;
}

.theme-default .widget .widget-checkbox-container input[type="checkbox"]:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

:root.theme-default .widget-pasted {
    box-shadow: 0 0 0 2px var(--primary-color);
}

:root.theme-default .widget-pasted {
    box-shadow: 0 0 0 2px var(--primary-color);
}

/* Animaciones específicas por tema */
:root.theme-default .widget-pasted {
    animation: widget-paste-flash-default 1s ease-out;
}

@keyframes widget-paste-flash-default {
    0% {
        box-shadow: 0 0 0 4px rgba(var(--primary-color-rgb), 0.5);
        transform: scale(0.95);
    }

    50% {
        box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.8);
        transform: scale(1.02);
    }

    100% {
        box-shadow: none;
        transform: scale(1);
    }
}



/* Estilos generales */
.theme-default body {
    background-color: var(--background-color);
    color: var(--text-color);
    font-family: var(--font-family);
}

/* Contenedores */
.theme-default .container {
    background-color: transparent;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 10px var(--shadow-color);
    border-radius: 8px;
}

/* Excepción para el contenedor de login */
.theme-default #login-container {
    background-color: transparent;
    border: none;
    box-shadow: none;
}

/* Login */
.theme-default #login-container h1 {
    color: var(--text-color);
    font-size: 2rem;
    margin-bottom: 1.5rem;
}

.theme-default .login-form {
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 2px 10px var(--shadow-color);
    padding: 30px;
}

.theme-default .login-form h2 {
    color: var(--text-color);
    margin-bottom: 20px;
    text-align: center;
}

/* Estilo general para etiquetas en formularios */
.theme-default .form-group label {
    color: gray;
    font-weight: var(--font-weight-bold);
    margin-bottom: 5px;
}

/* Asegurar que todas las etiquetas en los diálogos de añadir y editar widget tengan el color gris */
.theme-default #edit-widget-modal .form-group:not(.widget-header-row) label,
.theme-default #add-widget-modal .form-group:not(.widget-header-row) label {
    color: gray !important;
}

/* Mantener el color original SOLO para "Tipo Widget:" y "Personalización de apariencia" */
.theme-default #edit-widget-modal .widget-type-label,
.theme-default #add-widget-modal .form-group label[for="widget-type"] {
    color: var(--text-color) !important;
    font-weight: var(--font-weight-bold) !important;
}

.theme-default #edit-widget-modal h3,
.theme-default #add-widget-modal h3 {
    color: var(--text-color) !important;
    font-weight: var(--font-weight-bold);
}

/* Asegurar que las etiquetas de colores tengan el color gris */
.theme-default #edit-widget-modal h3~.form-group label,
.theme-default #add-widget-modal h3~.form-group label {
    color: gray !important;
}

/* Estilos para la sección de series */
.theme-default .button-field-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 10px;
    position: relative;
    /* Asegurar que los botones se posicionen correctamente */
    z-index: 1;
    /* Asegurar que los botones estén por encima */
}

/* Los estilos para los botones de series se han movido a styles2.css */

/* Colocar los botones de series en la misma fila que el selector */
.theme-default #form-group-edit-chart-series-type {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0;
    position: relative;
    padding-right: 90px;
    z-index: 1;
    /* Asegurar que el selector esté por encima */
}

.theme-default #form-group-edit-chart-series-type select {
    width: calc(100% - 90px);
}

.theme-default #form-group-edit-chart-add-series,
.theme-default #form-group-edit-chart-remove-series {
    margin: 0;
    padding: 0;
    display: inline-block;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    /* Centrar verticalmente */
    z-index: 2;
    /* Asegurar que los botones estén por encima */
}

.theme-default #form-group-edit-chart-add-series {
    right: 40px;
}

.theme-default #form-group-edit-chart-remove-series {
    right: 5px;
}

/* Ocultar los contenedores de los botones para que no ocupen espacio adicional */
.theme-default #form-group-edit-chart-add-series label,
.theme-default #form-group-edit-chart-remove-series label {
    display: none;
}

/* Estilos para la lista de series */
.theme-default .current-series-list {
    margin-top: 10px;
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
    max-height: 150px;
    overflow-y: auto;
}

.theme-default .series-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.theme-default .series-list li {
    display: flex;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

.theme-default .series-list li:last-child {
    border-bottom: none;
}

.theme-default .series-color {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 8px;
}

.theme-default .series-name {
    font-size: 14px;
}

.theme-default .form-group input:not([type="checkbox"]),
.theme-default .form-group select,
.theme-default .form-group textarea {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 8px;
    border-radius: 4px;
}

.theme-default button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.theme-default button:hover {
    background-color: var(--secondary-color);
}

.theme-default .error-message {
    color: var(--error-color);
    margin-top: 10px;
    text-align: center;
}

/* Header */
.theme-default header {
    border-bottom: 1px solid var(--border-color);
    padding: 15px 0;
    margin-bottom: 20px;
}

.theme-default header h1 {
    color: var(--text-color);
    padding-left: 10px;
}

.theme-default .header-title-container {
    margin-left: 20px;
}

.theme-default .dashboard-quick-actions {
    margin-top: 5px;
}

.theme-default .icon-button {
    background-color: #f5f5f5;
    color: var(--text-color);
    border: 1px solid #e0e0e0;
    transition: all 0.2s ease;
}

.theme-default .icon-button:hover {
    background-color: #e0e0e0;
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.theme-default .dropdown-menu {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    box-shadow: 0 2px 10px var(--shadow-color);
    padding: 8px 0;
    min-width: 180px;
}

.theme-default .dropdown-menu button {
    background-color: transparent;
    color: var(--text-color);
    border: none;
    border-bottom: 1px solid var(--border-color);
    padding: 10px 15px;
    width: 100%;
    text-align: left;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.theme-default .dropdown-menu button:last-child {
    border-bottom: none;
}

.theme-default .dropdown-menu button:hover {
    background-color: #f0f0f0;
    color: var(--primary-color);
    font-weight: var(--font-weight-bold);
}

/* Dashboard */
.theme-default .dashboard {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-image: none;
    /* Sin cuadrícula por defecto */
}

/* Dashboard con cuadrícula */
.theme-default .dashboard.show-grid {
    background-image: linear-gradient(var(--grid-color, #dddddd) 1px, transparent 1px),
        linear-gradient(90deg, var(--grid-color, #dddddd) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Widgets */
.theme-default .widget {
    background-color: #f9f9f9;
    border-radius: 4px;
    padding: 10px;
    box-shadow: 0 1px 3px var(--shadow-color);
    position: absolute !important;
    /* Forzar posición absoluta para permitir movimiento */
}

/* Manejador de redimensionamiento */
.theme-default .widget .resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 15px;
    height: 15px;
    cursor: nwse-resize;
    background: transparent;
    z-index: 10;
    display: none;
    /* Oculto por defecto */
}

.theme-default .widget .resize-handle::before {
    content: '';
    position: absolute;
    bottom: 3px;
    right: 3px;
    width: 8px;
    height: 8px;
    border-right: 2px solid #aaa;
    border-bottom: 2px solid #aaa;
    opacity: 0.7;
}

.theme-default .edit-mode .widget .resize-handle {
    display: block;
    /* Visible en modo edición */
}

.theme-default .show-widget-borders .widget {
    border: 1px solid var(--border-color);
}

.theme-default .widget-header {
    border-bottom: 1px solid #eee;
}

.theme-default .widget-title {
    font-weight: var(--font-weight-bold);
    font-size: 14px;
}

.theme-default .text-widget .widget-content {
    font-size: 16px;
}

.theme-default .value-widget .widget-content {
    font-size: 24px;
    font-weight: var(--font-weight-bold);
}

/* Gauges */
.theme-default .gauge-arc.gauge-background {
    stroke: #eee;
}

.theme-default .gauge-arc.gauge-foreground {
    stroke: var(--primary-color);
}

.theme-default .gauge-value {
    font-weight: var(--font-weight-bold);
}

.theme-default .percentage-title {
    font-size: 16px;
    color: var(--text-secondary-color);
    margin-bottom: 5px;
}

.theme-default .percentage-value {
    font-size: 28px;
    font-weight: var(--font-weight-bold);
    margin-bottom: 15px;
}

.theme-default .percentage-gauge {
    background-color: #eee;
    border-radius: 15px;
    height: 30px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.theme-default .percentage-gauge-fill {
    background-color: var(--primary-color);
    background-image: linear-gradient(45deg,
            rgba(255, 255, 255, 0.15) 25%,
            transparent 25%,
            transparent 50%,
            rgba(255, 255, 255, 0.15) 50%,
            rgba(255, 255, 255, 0.15) 75%,
            transparent 75%,
            transparent);
    background-size: 40px 40px;
    border-radius: 15px;
}

/* Modales */
.theme-default .modal-content {
    background-color: var(--surface-color);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.theme-default .modal-content h2 {
    margin-bottom: 10px;
    /* Reducido de 20px a 10px */
    margin-top: 0;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
    /* Reducido de 10px a 8px */
}

.theme-default .close-btn {
    color: var(--text-color);
    transition: color 0.3s ease;
}

.theme-default .close-btn:hover {
    background-color: var(--error-color);
    color: white;
}

/* Diálogo de confirmación de eliminación */
.theme-default .delete-confirm-dialog .dialog-content {
    border: 2px solid var(--error-color);
    background-color: rgba(var(--error-color-rgb), 0.05);
    box-shadow: 0 5px 15px rgba(var(--error-color-rgb), 0.2);
}

.theme-default .delete-confirm-dialog .dialog-header {
    background-color: rgba(var(--error-color-rgb), 0.1);
    border-bottom: 1px solid rgba(var(--error-color-rgb), 0.2);
}

.theme-default .delete-confirm-dialog .dialog-header h3 {
    color: var(--error-color);
    font-weight: var(--font-weight-bold);
}

/* Botón de eliminar en el diálogo de confirmación */
.theme-default .delete-confirm-dialog .danger-btn {
    background-color: var(--error-color);
    color: white;
    border: none;
    box-shadow: 0 2px 5px rgba(var(--error-color-rgb), 0.3);
}

.theme-default .delete-confirm-dialog .danger-btn:hover {
    background-color: #d32f2f;
    /* Rojo más oscuro */
    box-shadow: 0 3px 7px rgba(var(--error-color-rgb), 0.5);
}

/* Popup de opciones */
.theme-default .popup-content {
    background-color: var(--surface-color);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.theme-default .popup-content h3 {
    margin-bottom: 15px;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
    text-align: center;
}

.theme-default .popup-option {
    background-color: var(--surface-color);
    color: #2E7D32;
    /* Verde oscuro para el texto */
    border: none;
    border-radius: 4px;
    padding: 12px 15px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 5px var(--shadow-color);
    text-align: left;
    width: 100%;
}

.theme-default .popup-option:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.theme-default .popup-option.danger-option {
    color: var(--error-color);
}

.theme-default .popup-option.danger-option:hover {
    background-color: var(--error-color);
    color: white;
}

/* Estilos para el popup de datos de período en el tema por defecto */
.theme-default .period-data-popup {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.theme-default .period-data-popup .popup-header {
    background-color: #f5f5f5;
    border-bottom: 1px solid var(--border-color);
}

.theme-default .period-data-popup .popup-header h3 {
    color: var(--text-color);
    font-weight: var(--font-weight-bold);
}

.theme-default .period-data-popup .close-btn {
    color: var(--text-secondary-color);
}

.theme-default .period-data-popup .close-btn:hover {
    color: var(--error-color);
}

.theme-default .period-data-table th {
    background-color: #f5f5f5;
    color: var(--text-color);
    font-weight: var(--font-weight-bold);
}

.theme-default .period-points-table th {
    background-color: #f5f5f5;
    color: var(--text-color);
    font-weight: var(--font-weight-bold);
}

.theme-default .period-points-table {
    border: 1px solid var(--border-color);
}

/* Botones en modales */
.theme-default .button-group {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.theme-default .primary-btn {
    background-color: var(--primary-color);
    color: white;
}

.theme-default .primary-btn:hover {
    background-color: var(--secondary-color);
}

.theme-default .danger-btn {
    background-color: var(--error-color);
    color: white;
}

.theme-default .danger-btn:hover {
    background-color: #d32f2f;
    /* Rojo más oscuro */
}

/* Botón de eliminar (cruz) en la cabecera del diálogo de editar widget */
.theme-default .icon-action-btn.danger-icon-btn {
    background-color: var(--error-color);
    color: white;
    border: 1px solid #d32f2f;
}

.theme-default .icon-action-btn.danger-icon-btn:hover {
    background-color: #d32f2f;
    box-shadow: 0 3px 6px rgba(211, 47, 47, 0.3);
    transform: translateY(-2px);
}

/* Modo edición */
.theme-default .edit-mode .widget:hover {
    border: 2px dashed var(--primary-color);
}

.theme-default .delete-mode .widget:hover {
    border: 2px dashed var(--error-color);
}

/* Estilo para el widget durante el arrastre */
.theme-default .widget.dragging {
    opacity: 0.8;
    z-index: 100;
    cursor: move;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    /* Sombra más pronunciada durante el arrastre */
    transition: opacity 0.2s ease, box-shadow 0.2s ease;
}

/* Checkbox personalizado */
.theme-default .checkbox-group {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.theme-default .checkbox-group input[type="checkbox"] {
    margin-right: 10px;
}

.theme-default .checkbox-group label {
    font-weight: normal;
    margin-bottom: 0;
    color: gray;
}

/* Asegurar que las etiquetas de checkbox en los diálogos de añadir y editar widget tengan el color gris */
.theme-default #edit-widget-modal .checkbox-group label,
.theme-default #add-widget-modal .checkbox-group label {
    color: gray !important;
}

/* Transparencia de widgets */
.theme-default .transparent-widgets .widget {
    background-color: transparent;
    box-shadow: none;
}