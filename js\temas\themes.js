/**
 * <PERSON><PERSON><PERSON><PERSON> para la gestión de temas
 */
class ThemeManager {
    constructor() {
        this.currentTheme = 'default';
        app['themeManager'] = this;
    }

    /**
     * Configura los eventos relacionados con el tema
     */
    setupThemeEvents() {
        on(document, 'change', '#login-container #theme', (e) => {
            this.applyThemeWithTransition(e.target.value, true);
        });
    }

    /**
     * Aplica el tema con una transición suave
     * @param {string} theme - Nombre del tema a aplicar
     * @param {boolean} remember - Si se debe guardar el tema en localStorage}
     */
    applyThemeWithTransition(theme, remember = false) {
        const htmlElement = document.documentElement;

        // Función que se ejecutará cuando termine la animación de fade out
        const onFadeOutComplete = () => {
            // Cambiar el tema
            this.changeTheme(theme, remember);

            // Remover el evento y la clase de fade out
            htmlElement.removeEventListener('animationend', onFadeOutComplete);
            htmlElement.classList.remove('theme-fade-out');

            // Añadir la clase de fade in
            htmlElement.classList.add('theme-fade-in');

            // Función que se ejecutará cuando termine la animación de fade in
            const onFadeInComplete = () => {
                // Remover el evento y las clases de transición
                htmlElement.removeEventListener('animationend', onFadeInComplete);
                htmlElement.classList.remove('theme-transition', 'theme-fade-in');

                // Dar el foco a la caja de login
                const usernameInput = document.getElementById('username');
                if (usernameInput) {
                    usernameInput.focus();
                }
            };

            // Añadir el evento para la animación de fade in
            htmlElement.addEventListener('animationend', onFadeInComplete);
        };

        // Añadir el evento para la animación de fade out
        htmlElement.addEventListener('animationend', onFadeOutComplete);

        // Añadir clase de transición y evento de animación
        htmlElement.classList.remove('theme-transition', 'theme-fade-out');
        htmlElement.classList.add('theme-transition', 'theme-fade-out');

    }

    /**
     * Cambia el tema de la aplicación
     * @param {string} theme - Nombre del tema a aplicar
     * @param {boolean} remember - Si se debe guardar el tema en localStorage}
     */
    changeTheme(theme, remember = false) {
        // Remover tema actual
        Array.from(document.documentElement.classList)
            .filter(className => className.startsWith('theme-'))
            .forEach(className => document.documentElement.classList.remove(className));

        // Aplicar nuevo tema
        document.documentElement.classList.add(`theme-${theme}`);
        this.currentTheme = theme;

        // Guardar tema en localStorage
        if (remember)
            localStorage.setItem('ipram_theme', theme);
    }

    /**
     * Carga el tema guardado
     */
    loadSavedTheme() {
        const savedTheme = localStorage.getItem('ipram_theme');
        const themeSelect = document.getElementById('theme');

        if (savedTheme) {
            // Actualizar el tema actual
            this.currentTheme = savedTheme;

            // Actualizar el selector de tema primero
            if (themeSelect) {
                themeSelect.value = savedTheme;
            }

            // Aplicar el tema después de actualizar el selector
            this.changeTheme(savedTheme);
        } else {
            // Si no hay tema guardado, usar el tema por defecto
            this.currentTheme = 'default';

            // Actualizar el selector de tema primero
            if (themeSelect) {
                themeSelect.value = 'default';
            }

            // Aplicar el tema por defecto después de actualizar el selector
            this.changeTheme('default');
        }
    }

    /**
     * Obtiene el tema actual
     * @returns {string} - Nombre del tema actual
     */
    getCurrentTheme() {
        return this.currentTheme;
    }
}

// Exportar la instancia del gestor de temas
const themeManager = new ThemeManager();