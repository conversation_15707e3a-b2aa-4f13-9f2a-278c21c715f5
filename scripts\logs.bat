@echo off
REM Script para ver logs del servidor IPRA_I (Windows)

setlocal enabledelayedexpansion

REM Colores para Windows
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "NC=[0m"

REM Función para logging
:log
echo %GREEN%[INFO] %~1%NC%
goto :eof

:warn
echo %YELLOW%[WARN] %~1%NC%
goto :eof

:error
echo %RED%[ERROR] %~1%NC%
exit /b 1

REM Cargar configuración
set "CONFIG_FILE=%~dp0config.conf"
if not exist "%CONFIG_FILE%" (
    call :error "Archivo config.conf no encontrado. Ejecuta scripts\configure.bat primero."
    exit /b 1
)

REM Leer configuración
for /f "tokens=1,2 delims==" %%a in ('type "%CONFIG_FILE%" ^| findstr /v "^#" ^| findstr "="') do (
    set "%%a=%%b"
)

REM Procesar argumentos
set "LINES=50"
set "FOLLOW=false"

if "%~1"=="-h" goto :show_help
if "%~1"=="--help" goto :show_help
if "%~1"=="-s" (
    set "LINES=%~2"
    if "!LINES!"=="" set "LINES=50"
    call :show_server_logs
    goto :eof
)
if "%~1"=="--server" (
    set "LINES=%~2"
    if "!LINES!"=="" set "LINES=50"
    call :show_server_logs
    goto :eof
)
if "%~1"=="-e" (
    set "HOURS=%~2"
    if "!HOURS!"=="" set "HOURS=24"
    call :show_error_logs
    goto :eof
)
if "%~1"=="--errors" (
    set "HOURS=%~2"
    if "!HOURS!"=="" set "HOURS=24"
    call :show_error_logs
    goto :eof
)
if "%~1"=="-t" (
    call :show_log_stats
    goto :eof
)
if "%~1"=="--stats" (
    call :show_log_stats
    goto :eof
)
if "%~1"=="-c" (
    set "DAYS=%~2"
    if "!DAYS!"=="" set "DAYS=7"
    call :clean_logs
    goto :eof
)
if "%~1"=="--clean" (
    set "DAYS=%~2"
    if "!DAYS!"=="" set "DAYS=7"
    call :clean_logs
    goto :eof
)
if "%~1"=="-r" (
    call :rotate_logs
    goto :eof
)
if "%~1"=="--rotate" (
    call :rotate_logs
    goto :eof
)
if "%~1"=="-g" (
    if "%~2"=="" (
        call :error "Patrón de búsqueda requerido para --search"
        exit /b 1
    )
    set "PATTERN=%~2"
    set "SEARCH_LINES=%~3"
    if "!SEARCH_LINES!"=="" set "SEARCH_LINES=10"
    call :search_logs
    goto :eof
)
if "%~1"=="--search" (
    if "%~2"=="" (
        call :error "Patrón de búsqueda requerido para --search"
        exit /b 1
    )
    set "PATTERN=%~2"
    set "SEARCH_LINES=%~3"
    if "!SEARCH_LINES!"=="" set "SEARCH_LINES=10"
    call :search_logs
    goto :eof
)

REM Por defecto, mostrar logs del servidor
call :show_server_logs
goto :eof

:show_server_logs
set "SERVER_LOG=%LOG_DIR%\server.log"

if not exist "%SERVER_LOG%" (
    call :warn "Log del servidor no encontrado: %SERVER_LOG%"
    exit /b 1
)

call :log "Mostrando logs del servidor: %SERVER_LOG%"
echo.

REM Mostrar últimas líneas (simulando tail en Windows)
if exist "%SERVER_LOG%" (
    REM Usar PowerShell para simular tail
    powershell -Command "Get-Content '%SERVER_LOG%' | Select-Object -Last %LINES%"
)

goto :eof

:show_error_logs
set "HOURS=%~1"
if "%HOURS%"=="" set "HOURS=24"

call :log "Mostrando errores de las últimas %HOURS% horas"
echo.

REM Errores del servidor
set "SERVER_LOG=%LOG_DIR%\server.log"
if exist "%SERVER_LOG%" (
    echo %BLUE%=== ERRORES DEL SERVIDOR ===%NC%
    
    REM Buscar errores en el log (aproximado)
    findstr /i "error exception fail" "%SERVER_LOG%" 2>nul || echo No se encontraron errores
    echo.
)

goto :eof

:show_log_stats
call :log "Estadísticas de logs"
echo.

REM Estadísticas del servidor
set "SERVER_LOG=%LOG_DIR%\server.log"
if exist "%SERVER_LOG%" (
    echo %BLUE%=== SERVIDOR ===%NC%
    
    for %%F in ("%SERVER_LOG%") do (
        set "SIZE=%%~zF"
        set /a "SIZE_KB=!SIZE!/1024"
        echo Archivo: %SERVER_LOG%
        echo Tamaño: !SIZE_KB!KB
    )
    
    REM Contar líneas (aproximado)
    for /f %%i in ('type "%SERVER_LOG%" ^| find /c /v ""') do echo Líneas: %%i
    
    REM Contar errores
    for /f %%i in ('findstr /i "error" "%SERVER_LOG%" 2^>nul ^| find /c /v ""') do echo Errores: %%i
    
    REM Contar advertencias
    for /f %%i in ('findstr /i "warn" "%SERVER_LOG%" 2^>nul ^| find /c /v ""') do echo Advertencias: %%i
    
    echo.
)

REM Otros logs
if exist "%LOG_DIR%" (
    echo %BLUE%=== DIRECTORIO DE LOGS ===%NC%
    dir "%LOG_DIR%" /A-D 2>nul || echo Directorio vacío o no accesible
    echo.
)

goto :eof

:clean_logs
set "DAYS=%~1"
if "%DAYS%"=="" set "DAYS=7"

call :log "Limpiando logs más antiguos de %DAYS% días"

REM Limpiar logs del servidor (Windows no tiene find -mtime, usar forfiles)
if exist "%LOG_DIR%" (
    forfiles /P "%LOG_DIR%" /M "*.log" /D -%DAYS% /C "cmd /c del @path" 2>nul || echo No hay logs antiguos para limpiar
    forfiles /P "%LOG_DIR%" /M "*.log.*" /D -%DAYS% /C "cmd /c del @path" 2>nul || echo No hay logs rotados antiguos para limpiar
    call :log "Logs del directorio %LOG_DIR% limpiados"
)

goto :eof

:rotate_logs
call :log "Rotando logs del servidor"

set "SERVER_LOG=%LOG_DIR%\server.log"

if exist "%SERVER_LOG%" (
    set "TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
    set "TIMESTAMP=!TIMESTAMP: =0!"
    set "ROTATED_LOG=%LOG_DIR%\server.log.!TIMESTAMP!"
    
    move "%SERVER_LOG%" "!ROTATED_LOG!" >nul
    echo. > "%SERVER_LOG%"
    
    call :log "Log rotado: !ROTATED_LOG!"
    
    REM Verificar si hay procesos ejecutándose
    tasklist /FI "IMAGENAME eq node.exe" 2>nul | findstr "node.exe" >nul
    if not errorlevel 1 (
        call :warn "Considera reiniciar el servidor para usar el nuevo archivo de log"
    )
) else (
    call :warn "No se encontró log del servidor para rotar"
)

goto :eof

:search_logs
set "PATTERN=%~1"
set "LINES=%~2"
if "%LINES%"=="" set "LINES=10"

if "%PATTERN%"=="" (
    call :error "Patrón de búsqueda requerido"
    exit /b 1
)

call :log "Buscando '%PATTERN%' en logs (últimas %LINES% coincidencias)"
echo.

REM Buscar en log del servidor
set "SERVER_LOG=%LOG_DIR%\server.log"
if exist "%SERVER_LOG%" (
    echo %BLUE%=== SERVIDOR ===%NC%
    findstr /i "%PATTERN%" "%SERVER_LOG%" 2>nul | powershell -Command "$input | Select-Object -Last %LINES%" || echo No se encontraron coincidencias
    echo.
)

goto :eof

:show_help
echo Uso: %~nx0 [OPCIONES] [ARGUMENTOS]
echo.
echo Opciones:
echo   -h, --help              Mostrar esta ayuda
echo   -s, --server [LÍNEAS]   Mostrar logs del servidor (por defecto: 50 líneas)
echo   -e, --errors [HORAS]    Mostrar solo errores (por defecto: 24 horas)
echo   -t, --stats             Mostrar estadísticas de logs
echo   -c, --clean [DÍAS]      Limpiar logs antiguos (por defecto: 7 días)
echo   -r, --rotate            Rotar logs del servidor
echo   -g, --search PATRÓN [LÍNEAS]  Buscar patrón en logs
echo.
echo Ejemplos:
echo   %~nx0                      # Mostrar logs del servidor (50 líneas)
echo   %~nx0 -s 100               # Mostrar 100 líneas del servidor
echo   %~nx0 -e 12                # Errores de las últimas 12 horas
echo   %~nx0 -g "websocket" 20    # Buscar 'websocket' (20 coincidencias)
echo   %~nx0 -c 3                 # Limpiar logs de más de 3 días
echo.
goto :eof

endlocal
