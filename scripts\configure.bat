@echo off
REM Script de configuración para IPRA_I (Windows)
REM Permite configurar puerto, configuración regional y otras opciones

setlocal enabledelayedexpansion

REM Colores para Windows (limitados)
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "NC=[0m"

REM Función para logging
:log
echo %GREEN%[INFO] %~1%NC%
goto :eof

:warn
echo %YELLOW%[WARN] %~1%NC%
goto :eof

:error
echo %RED%[ERROR] %~1%NC%
exit /b 1

REM Verificar si existe configuración anterior
set "CONFIG_FILE=%~dp0config.conf"
set "DEFAULT_PORT=3000"
set "DEFAULT_HOST=localhost"
set "DEFAULT_LOCALE=es_ES"
set "DEFAULT_BUILD_DIR=build"
set "DEFAULT_DOCS_DIR=docs_generated"
set "DEFAULT_LOG_DIR=logs"
set "DEFAULT_LOG_LEVEL=info"

if exist "%CONFIG_FILE%" (
    call :log "Configuración anterior encontrada"
    REM Leer configuración existente
    for /f "tokens=1,2 delims==" %%a in ('type "%CONFIG_FILE%" ^| findstr /v "^#" ^| findstr "="') do (
        set "%%a=%%b"
    )
)

REM Configuración interactiva
call :log "=== CONFIGURACIÓN INTERACTIVA DE IPRA_I ==="
echo.

REM Puerto del servidor
set /p "INPUT_PORT=Puerto del servidor [%SERVER_PORT%]: "
if "!INPUT_PORT!"=="" set "INPUT_PORT=%SERVER_PORT%"
if "!INPUT_PORT!"=="" set "INPUT_PORT=%DEFAULT_PORT%"

REM Validar puerto
if !INPUT_PORT! LSS 1 (
    call :error "Puerto inválido: !INPUT_PORT!"
    goto :eof
)
if !INPUT_PORT! GTR 65535 (
    call :error "Puerto inválido: !INPUT_PORT!"
    goto :eof
)

set "SERVER_PORT=!INPUT_PORT!"

REM Host del servidor
set /p "INPUT_HOST=Host del servidor [%SERVER_HOST%]: "
if "!INPUT_HOST!"=="" set "INPUT_HOST=%SERVER_HOST%"
if "!INPUT_HOST!"=="" set "INPUT_HOST=%DEFAULT_HOST%"
set "SERVER_HOST=!INPUT_HOST!"

REM Configuración regional
echo.
echo Configuraciones regionales disponibles:
echo   es_ES - España (números con coma decimal: 1,23)
echo   en_US - Estados Unidos (números con punto decimal: 1.23)
echo.
set /p "INPUT_LOCALE=Configuración regional [%LC_NUMERIC%]: "
if "!INPUT_LOCALE!"=="" set "INPUT_LOCALE=%LC_NUMERIC%"
if "!INPUT_LOCALE!"=="" set "INPUT_LOCALE=%DEFAULT_LOCALE%"
set "LC_NUMERIC=!INPUT_LOCALE!"

REM Directorios
set /p "INPUT_BUILD_DIR=Directorio de build [%BUILD_DIR%]: "
if "!INPUT_BUILD_DIR!"=="" set "INPUT_BUILD_DIR=%BUILD_DIR%"
if "!INPUT_BUILD_DIR!"=="" set "INPUT_BUILD_DIR=%DEFAULT_BUILD_DIR%"
set "BUILD_DIR=!INPUT_BUILD_DIR!"

set /p "INPUT_DOCS_DIR=Directorio de documentación [%DOCS_DIR%]: "
if "!INPUT_DOCS_DIR!"=="" set "INPUT_DOCS_DIR=%DOCS_DIR%"
if "!INPUT_DOCS_DIR!"=="" set "INPUT_DOCS_DIR=%DEFAULT_DOCS_DIR%"
set "DOCS_DIR=!INPUT_DOCS_DIR!"

set /p "INPUT_LOG_DIR=Directorio de logs [%LOG_DIR%]: "
if "!INPUT_LOG_DIR!"=="" set "INPUT_LOG_DIR=%LOG_DIR%"
if "!INPUT_LOG_DIR!"=="" set "INPUT_LOG_DIR=%DEFAULT_LOG_DIR%"
set "LOG_DIR=!INPUT_LOG_DIR!"

REM Archivos de build
set /p "INPUT_JS_FILE=Archivo JavaScript combinado [%COMBINED_JS_FILE%]: "
if "!INPUT_JS_FILE!"=="" set "INPUT_JS_FILE=%COMBINED_JS_FILE%"
if "!INPUT_JS_FILE!"=="" set "INPUT_JS_FILE=aplicacion.js"
set "COMBINED_JS_FILE=!INPUT_JS_FILE!"

set /p "INPUT_CSS_FILE=Archivo CSS combinado [%COMBINED_CSS_FILE%]: "
if "!INPUT_CSS_FILE!"=="" set "INPUT_CSS_FILE=%COMBINED_CSS_FILE%"
if "!INPUT_CSS_FILE!"=="" set "INPUT_CSS_FILE=aplicacion.css"
set "COMBINED_CSS_FILE=!INPUT_CSS_FILE!"

REM WebSockets
set /p "INPUT_RECONNECT_INITIAL=Tiempo inicial de reconexión ms [%WEBSOCKET_RECONNECT_INITIAL%]: "
if "!INPUT_RECONNECT_INITIAL!"=="" set "INPUT_RECONNECT_INITIAL=%WEBSOCKET_RECONNECT_INITIAL%"
if "!INPUT_RECONNECT_INITIAL!"=="" set "INPUT_RECONNECT_INITIAL=1000"
set "WEBSOCKET_RECONNECT_INITIAL=!INPUT_RECONNECT_INITIAL!"

set /p "INPUT_RECONNECT_MAX=Tiempo máximo de reconexión ms [%WEBSOCKET_RECONNECT_MAX%]: "
if "!INPUT_RECONNECT_MAX!"=="" set "INPUT_RECONNECT_MAX=%WEBSOCKET_RECONNECT_MAX%"
if "!INPUT_RECONNECT_MAX!"=="" set "INPUT_RECONNECT_MAX=30000"
set "WEBSOCKET_RECONNECT_MAX=!INPUT_RECONNECT_MAX!"

REM Nivel de log
echo.
echo Niveles de log disponibles:
echo   debug - Información detallada para desarrollo
echo   info  - Información general (recomendado)
echo   warn  - Solo advertencias y errores
echo   error - Solo errores
echo.
set /p "INPUT_LOG_LEVEL=Nivel de log [%LOG_LEVEL%]: "
if "!INPUT_LOG_LEVEL!"=="" set "INPUT_LOG_LEVEL=%LOG_LEVEL%"
if "!INPUT_LOG_LEVEL!"=="" set "INPUT_LOG_LEVEL=%DEFAULT_LOG_LEVEL%"
set "LOG_LEVEL=!INPUT_LOG_LEVEL!"

REM Mostrar resumen
echo.
call :log "=== RESUMEN DE CONFIGURACIÓN ==="
echo.
echo Servidor:
echo   Host: !SERVER_HOST!
echo   Puerto: !SERVER_PORT!
echo.
echo Regional:
echo   Configuración numérica: !LC_NUMERIC!
echo.
echo Directorios:
echo   Build: !BUILD_DIR!
echo   Documentación: !DOCS_DIR!
echo   Logs: !LOG_DIR!
echo.
echo Archivos de build:
echo   JavaScript: !COMBINED_JS_FILE!
echo   CSS: !COMBINED_CSS_FILE!
echo.
echo WebSockets:
echo   Reconexión inicial: !WEBSOCKET_RECONNECT_INITIAL!ms
echo   Reconexión máxima: !WEBSOCKET_RECONNECT_MAX!ms
echo.
echo Logging:
echo   Nivel: !LOG_LEVEL!
echo.

REM Confirmar guardado
set /p "SAVE_CONFIRM=¿Guardar esta configuración? (Y/n): "
if /i "!SAVE_CONFIRM!"=="n" (
    call :warn "Configuración cancelada"
    exit /b 1
)

REM Crear backup si existe configuración anterior
if exist "%CONFIG_FILE%" (
    set "BACKUP_FILE=%CONFIG_FILE%.backup.%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
    copy "%CONFIG_FILE%" "!BACKUP_FILE!" >nul
    call :log "Backup de configuración anterior creado"
)

REM Guardar nueva configuración
(
echo # Configuración centralizada del proyecto IPRA_I
echo # Generado automáticamente el %date% %time%
echo # Todos los scripts deben cargar desde scripts\config.conf ^(Windows^) o scripts/config.conf ^(Linux^)
echo.
echo # === CONFIGURACIÓN DEL SERVIDOR ===
echo # Puerto donde correrá el servidor Node.js
echo SERVER_PORT=!SERVER_PORT!
echo.
echo # Host del servidor ^(normalmente localhost para desarrollo^)
echo SERVER_HOST=!SERVER_HOST!
echo.
echo # === CONFIGURACIÓN REGIONAL ===
echo # Configuración de números ^(es_ES usa coma decimal, en_US usa punto^)
echo # Valores: es_ES, en_US, etc.
echo LC_NUMERIC=!LC_NUMERIC!
echo.
echo # === CONFIGURACIÓN DE BUILD ===
echo # Directorio donde se generarán los archivos minificados
echo BUILD_DIR=!BUILD_DIR!
echo.
echo # Nombre del archivo JavaScript combinado
echo COMBINED_JS_FILE=!COMBINED_JS_FILE!
echo.
echo # Nombre del archivo CSS combinado
echo COMBINED_CSS_FILE=!COMBINED_CSS_FILE!
echo.
echo # === CONFIGURACIÓN DE DOCUMENTACIÓN ===
echo # Directorio para documentación generada
echo DOCS_DIR=!DOCS_DIR!
echo.
echo # === CONFIGURACIÓN DE WEBSOCKETS ===
echo # Tiempo de reconexión inicial en milisegundos
echo WEBSOCKET_RECONNECT_INITIAL=!WEBSOCKET_RECONNECT_INITIAL!
echo.
echo # Tiempo máximo de reconexión en milisegundos
echo WEBSOCKET_RECONNECT_MAX=!WEBSOCKET_RECONNECT_MAX!
echo.
echo # === CONFIGURACIÓN DE LOGS ===
echo # Nivel de log ^(debug, info, warn, error^)
echo LOG_LEVEL=!LOG_LEVEL!
echo.
echo # Directorio de logs
echo LOG_DIR=!LOG_DIR!
) > "%CONFIG_FILE%"

call :log "Configuración guardada en %CONFIG_FILE%"
call :log "Configuración completada. Ejecuta 'scripts\install.bat' para instalar."

endlocal
