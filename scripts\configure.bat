@echo off
REM Script de configuración para IPRA_I (Windows)
REM Permite configurar puerto, configuración regional y otras opciones

setlocal enabledelayedexpansion

REM Colores para Windows (limitados)
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "NC=[0m"

REM Función para logging
:log
echo %GREEN%[INFO] %~1%NC%
goto :eof

:warn
echo %YELLOW%[WARN] %~1%NC%
goto :eof

:error
echo %RED%[ERROR] %~1%NC%
exit /b 1

REM Verificar si existe configuración anterior
set "CONFIG_FILE=%~dp0config.conf"
set "DEFAULT_PORT=3000"
set "DEFAULT_HOST=localhost"
set "DEFAULT_LOCALE=es_ES"
set "DEFAULT_BUILD_DIR=build"
set "DEFAULT_DOCS_DIR=docs_generated"
set "DEFAULT_LOG_DIR=logs"
set "DEFAULT_LOG_LEVEL=info"

REM Inicializar variables con valores por defecto
set "SERVER_PORT=%DEFAULT_PORT%"
set "SERVER_HOST=%DEFAULT_HOST%"
set "LC_NUMERIC=%DEFAULT_LOCALE%"
set "BUILD_DIR=%DEFAULT_BUILD_DIR%"
set "DOCS_DIR=%DEFAULT_DOCS_DIR%"
set "LOG_DIR=%DEFAULT_LOG_DIR%"
set "LOG_LEVEL=%DEFAULT_LOG_LEVEL%"
set "COMBINED_JS_FILE=aplicacion.js"
set "COMBINED_CSS_FILE=aplicacion.css"
set "WEBSOCKET_RECONNECT_INITIAL=1000"
set "WEBSOCKET_RECONNECT_MAX=30000"

if exist "%CONFIG_FILE%" (
    call :log "Configuración anterior encontrada"
    REM Leer configuración existente
    for /f "tokens=1,2 delims==" %%a in ('type "%CONFIG_FILE%" ^| findstr /v "^#" ^| findstr "="') do (
        set "%%a=%%b"
    )
)

REM Configuración interactiva
call :log "=== CONFIGURACIÓN INTERACTIVA DE IPRA_I ==="
echo.

REM Puerto del servidor
echo Puerto del servidor actual: %SERVER_PORT%
set /p "INPUT_PORT=Nuevo puerto del servidor (Enter para mantener %SERVER_PORT%): "
if "!INPUT_PORT!"=="" (
    set "INPUT_PORT=%SERVER_PORT%"
    call :log "Manteniendo puerto actual: %SERVER_PORT%"
) else (
    call :log "Nuevo puerto configurado: !INPUT_PORT!"
)

set "SERVER_PORT=!INPUT_PORT!"

REM Host del servidor
echo Host del servidor actual: %SERVER_HOST%
set /p "INPUT_HOST=Nuevo host (Enter para mantener %SERVER_HOST%): "
if "!INPUT_HOST!"=="" (
    set "INPUT_HOST=%SERVER_HOST%"
) else (
    call :log "Nuevo host configurado: !INPUT_HOST!"
)
set "SERVER_HOST=!INPUT_HOST!"

echo.
REM Configuración regional
echo Configuración regional actual: %LC_NUMERIC%
echo Opciones: es_ES (coma decimal: 1,23) / en_US (punto decimal: 1.23)
set /p "INPUT_LOCALE=Nueva configuración regional (Enter para mantener %LC_NUMERIC%): "
if "!INPUT_LOCALE!"=="" (
    set "INPUT_LOCALE=%LC_NUMERIC%"
) else (
    call :log "Nueva configuración regional: !INPUT_LOCALE!"
)
set "LC_NUMERIC=!INPUT_LOCALE!"

echo.
REM Configuración simplificada - usar valores por defecto para el resto
call :log "Usando configuración por defecto para directorios y archivos..."
call :log "Build: %BUILD_DIR%"
call :log "Docs: %DOCS_DIR%"
call :log "Logs: %LOG_DIR%"
call :log "JS: %COMBINED_JS_FILE%"
call :log "CSS: %COMBINED_CSS_FILE%"

REM Mostrar resumen
echo.
call :log "=== RESUMEN DE CONFIGURACIÓN ==="
echo.
echo Servidor:
echo   Host: !SERVER_HOST!
echo   Puerto: !SERVER_PORT!
echo.
echo Regional:
echo   Configuración numérica: !LC_NUMERIC!
echo.
echo Directorios:
echo   Build: !BUILD_DIR!
echo   Documentación: !DOCS_DIR!
echo   Logs: !LOG_DIR!
echo.
echo Archivos de build:
echo   JavaScript: !COMBINED_JS_FILE!
echo   CSS: !COMBINED_CSS_FILE!
echo.
echo WebSockets:
echo   Reconexión inicial: !WEBSOCKET_RECONNECT_INITIAL!ms
echo   Reconexión máxima: !WEBSOCKET_RECONNECT_MAX!ms
echo.
echo Logging:
echo   Nivel: !LOG_LEVEL!
echo.

echo.
REM Confirmar guardado
set /p "SAVE_CONFIRM=¿Guardar esta configuración? (Y/n): "
if /i "!SAVE_CONFIRM!"=="n" (
    call :warn "Configuración cancelada"
    pause
    exit /b 1
)

REM Crear backup si existe configuración anterior
if exist "%CONFIG_FILE%" (
    set "BACKUP_FILE=%CONFIG_FILE%.backup"
    copy "%CONFIG_FILE%" "!BACKUP_FILE!" >nul 2>&1
    call :log "Backup de configuración anterior creado"
)

REM Guardar nueva configuración
(
echo # Configuración centralizada del proyecto IPRA_I
echo # Generado automáticamente el %date% %time%
echo # Todos los scripts deben cargar desde scripts\config.conf ^(Windows^) o scripts/config.conf ^(Linux^)
echo.
echo # === CONFIGURACIÓN DEL SERVIDOR ===
echo # Puerto donde correrá el servidor Node.js
echo SERVER_PORT=!SERVER_PORT!
echo.
echo # Host del servidor ^(normalmente localhost para desarrollo^)
echo SERVER_HOST=!SERVER_HOST!
echo.
echo # === CONFIGURACIÓN REGIONAL ===
echo # Configuración de números ^(es_ES usa coma decimal, en_US usa punto^)
echo # Valores: es_ES, en_US, etc.
echo LC_NUMERIC=!LC_NUMERIC!
echo.
echo # === CONFIGURACIÓN DE BUILD ===
echo # Directorio donde se generarán los archivos minificados
echo BUILD_DIR=!BUILD_DIR!
echo.
echo # Nombre del archivo JavaScript combinado
echo COMBINED_JS_FILE=!COMBINED_JS_FILE!
echo.
echo # Nombre del archivo CSS combinado
echo COMBINED_CSS_FILE=!COMBINED_CSS_FILE!
echo.
echo # === CONFIGURACIÓN DE DOCUMENTACIÓN ===
echo # Directorio para documentación generada
echo DOCS_DIR=!DOCS_DIR!
echo.
echo # === CONFIGURACIÓN DE WEBSOCKETS ===
echo # Tiempo de reconexión inicial en milisegundos
echo WEBSOCKET_RECONNECT_INITIAL=!WEBSOCKET_RECONNECT_INITIAL!
echo.
echo # Tiempo máximo de reconexión en milisegundos
echo WEBSOCKET_RECONNECT_MAX=!WEBSOCKET_RECONNECT_MAX!
echo.
echo # === CONFIGURACIÓN DE LOGS ===
echo # Nivel de log ^(debug, info, warn, error^)
echo LOG_LEVEL=!LOG_LEVEL!
echo.
echo # Directorio de logs
echo LOG_DIR=!LOG_DIR!
) > "%CONFIG_FILE%"

call :log "Configuración guardada en %CONFIG_FILE%"
call :log "Configuración completada. Ejecuta 'scripts\install.bat' para instalar."

endlocal
