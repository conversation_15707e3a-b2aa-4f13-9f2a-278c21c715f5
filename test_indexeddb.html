<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test IndexedDB</title>
</head>
<body>
    <h1>Test IndexedDB Database</h1>
    <div id="output"></div>
    
    <script src="js/database_innerdb.js"></script>
    <script>
        const output = document.getElementById('output');
        
        function log(message) {
            console.log(message);
            output.innerHTML += '<p>' + message + '</p>';
        }
        
        async function testDatabase() {
            try {
                log('Inicializando base de datos...');
                await dbManager.init();
                log('✅ Base de datos inicializada');
                
                log('Inicializando datos de prueba...');
                await dbManager.initializetestdata();
                log('✅ Datos de prueba inicializados');
                
                log('Obteniendo empresas...');
                const companies = await dbManager.getAllCompanies();
                log(`✅ Encontradas ${companies.length} empresas`);
                
                if (companies.length > 0) {
                    log(`Primera empresa: ${companies[0].nombre} (ID: ${companies[0].id})`);
                    
                    log('Obteniendo tableros de la primera empresa...');
                    const dashboards = await dbManager.getDashboards(companies[0].id);
                    log(`✅ Encontrados ${dashboards.length} tableros`);
                    
                    if (dashboards.length > 0) {
                        log(`Primer tablero: ${dashboards[0].name} (ID: ${dashboards[0].id})`);
                        
                        log('Probando búsqueda directa por ID de tablero...');
                        const result = await dbManager.findDashboardById(dashboards[0].id);
                        if (result) {
                            log(`✅ Tablero encontrado: ${result.dashboard.name} de empresa ${result.company.nombre}`);
                        } else {
                            log('❌ No se pudo encontrar el tablero');
                        }
                    }
                }
                
                log('🎉 Todas las pruebas completadas exitosamente');
                
            } catch (error) {
                log('❌ Error: ' + error.message);
                console.error('Error completo:', error);
            }
        }
        
        // Ejecutar pruebas cuando la página esté cargada
        document.addEventListener('DOMContentLoaded', testDatabase);
    </script>
</body>
</html>
