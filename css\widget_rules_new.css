/**
 * Estilos para la sección de reglas de widgets
 */

/* Contenedor de reglas */
.reglas-container {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 4px;
    margin-bottom: 0;
}

/* <PERSON><PERSON><PERSON>lo de la regla */
.regla-titulo {
    font-weight: bold;
    margin-bottom: 6px;
    font-size: 0.9em;
    color: #444;
    border-bottom: 1px dotted rgba(0, 0, 0, 0.1);
    padding-bottom: 3px;
}

/* Etiquetas */
.reglas-container label {
    display: inline-block;
    font-size: 0.85em;
    width: 40px;
    margin-right: 4px;
    vertical-align: middle;
}

/* Campos de número */
.reglas-container input[type="number"] {
    width: 60px;
    min-width: 60px;
    margin-right: 4px;
    vertical-align: middle;
}

.reglas-container .form-group label {
    flex: 0 0 50% !important;
    margin: 0;
    padding-right: 10px;
}

/* Campos de color */
.reglas-container input[type="color"] {
    width: 30px;
    height: 30px;
    padding: 0;
    margin-right: 4px;
    vertical-align: middle;
}

/* Asegurar alineación entre campos */
.reglas-container div:nth-child(odd) input[type="number"],
.reglas-container div:nth-child(even) input[type="color"]:first-of-type {
    margin-left: 0;
}

/* Espacio entre filas */
.reglas-container div {
    margin-bottom: 4px;
}

/* Estilos para textarea en widget de texto */
.textarea-widget {
    width: 100%;
    min-height: 80px;
    resize: vertical;
    padding: 8px;
    box-sizing: border-box;
    font-family: inherit;
    font-size: 0.9em;
    line-height: 1.4;
}

/* Estilos específicos para el tema tron */
.theme-tron .reglas-container {
    border-color: rgba(0, 162, 255, 0.3);
    background-color: rgba(0, 0, 0, 0.2);
}

.theme-tron .regla-titulo {
    color: #00a2ff;
    border-bottom-color: rgba(0, 162, 255, 0.3);
}

.theme-tron .textarea-widget {
    background-color: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 162, 255, 0.5);
    color: #00a2ff;
}

/* Estilos específicos para el tema neumorphic */
.theme-neumorphic .reglas-container {
    border: none;
    box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.1), -3px -3px 6px rgba(255, 255, 255, 0.5);
}

.theme-neumorphic .regla-titulo {
    color: #555;
    border-bottom-color: rgba(0, 0, 0, 0.1);
}

.theme-neumorphic .textarea-widget {
    border: none;
    box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.1), inset -2px -2px 5px rgba(255, 255, 255, 0.5);
}

/* Estilos específicos para el tema azul_nuboso */
.theme-azul_nuboso .reglas-container {
    border-color: rgba(74, 111, 165, 0.3);
    background-color: rgba(255, 255, 255, 0.05);
}

.theme-azul_nuboso .regla-titulo {
    color: #e0e9f5;
    border-bottom-color: rgba(74, 111, 165, 0.3);
}

.theme-azul_nuboso .textarea-widget {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(74, 111, 165, 0.5);
    color: #e0e9f5;
}