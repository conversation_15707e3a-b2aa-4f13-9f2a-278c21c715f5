# Manual de Usuario - iPRA Dashboard

## Índice

1. [Introducción](#introducción)
2. [Tipos de usuarios y permisos](#tipos-de-usuarios-y-permisos)
   - [Administrador](#administrador)
   - [Editor de Tableros](#editor-de-tableros)
   - [<PERSON>is<PERSON> de <PERSON>](#visor-de-tableros)
   - [Permisos especiales](#permisos-especiales)
3. [Pantallas y funcionalidades](#pantallas-y-funcionalidades)
   - [Pantalla de inicio de sesión](#pantalla-de-inicio-de-sesión)
   - [Menú principal](#menú-principal)
   - [Pantalla de tableros](#pantalla-de-tableros)
   - [Gestión de empresas](#gestión-de-empresas)
   - [Gestión de usuarios](#gestión-de-usuarios)
   - [Configuración de usuario](#configuración-de-usuario)
4. [Navegación entre pantallas](#navegación-entre-pantallas)
5. [Temas de la aplicación](#temas-de-la-aplicación)
6. [Tableros](#tableros)
   - [Configuración de tableros](#configuración-de-tableros)
   - [Operaciones con tableros](#operaciones-con-tableros)
   - [Widgets](#widgets)
     - [Manipulación de widgets](#manipulación-de-widgets)
     - [Tipos de widgets](#tipos-de-widgets)
     - [Configuración de widgets](#configuración-de-widgets)
7. [Funcionalidades genéricas](#funcionalidades-genéricas)
   - [Atajos de teclado y navegación](#atajos-de-teclado-y-navegación)
   - [Persistencia de sesión](#persistencia-de-sesión)
   - [Interacción con diálogos](#interacción-con-diálogos)
   - [Gestión de listados](#gestión-de-listados)
   - [Interacción gestual](#interacción-gestual)
8. [Datos de la aplicación](#datos-de-la-aplicación)
9. [Compatibilidad con dispositivos](#compatibilidad-con-dispositivos)
10. [Conclusiones](#conclusiones)

## Introducción

iPRA Dashboard es una aplicación que permite a los usuarios configurar y visualizar tableros personalizados (dashboards) para monitorizar datos en tiempo real. El objetivo principal de la aplicación es proporcionar una herramienta flexible y fácil de usar para la visualización de datos, permitiendo a cada usuario o empresa crear sus propios tableros adaptados a sus necesidades específicas.

La aplicación está diseñada para ser intuitiva y accesible, ofreciendo una experiencia de usuario fluida tanto en dispositivos de escritorio como en dispositivos móviles.

## Tipos de usuarios y permisos

La aplicación iPRA Dashboard cuenta con tres tipos de usuarios, cada uno con diferentes niveles de permisos:

### Administrador

- Puede crear, editar y eliminar usuarios
- Puede crear, editar y eliminar tableros
- Tiene acceso completo a todas las funcionalidades de la aplicación

### Editor de Tableros

- Puede crear, editar y eliminar tableros de su empresa
- No puede gestionar usuarios

### Visor de Tableros

- Puede ver los tableros de su empresa
- No puede crear, editar ni eliminar tableros
- Acceso de solo lectura

### Permisos especiales

La empresa con ID 1 (iPRA) tiene un estatus especial en la aplicación:

- Los administradores de iPRA son los únicos que pueden crear empresas
- Los administradores de iPRA pueden crear usuarios para cualquier empresa
- Los administradores de iPRA pueden crear tableros para cualquier empresa

Los administradores y editores de otras empresas solo pueden crear tableros para su propia empresa, no para otras empresas.

## Pantallas y funcionalidades

### Pantalla de inicio de sesión

- Permite a los usuarios acceder a la aplicación mediante sus credenciales
- Incluye un selector de tema para personalizar la apariencia de la aplicación
- El tema seleccionado se guarda en el navegador del usuario

### Menú principal

- Muestra las opciones disponibles según el tipo de usuario
- Todos los usuarios tienen acceso a la opción "Tablero"
- Los administradores tienen acceso adicional a "Empresas" (o "Empresa" si no son de iPRA) y "Personalizar"

### Pantalla de tableros

- Muestra el tablero activo con sus widgets
- Permite cambiar entre diferentes tableros
- Incluye opciones para crear, editar y eliminar tableros (según permisos)
- Permite añadir, editar y eliminar widgets (según permisos)

### Gestión de empresas

- Disponible solo para administradores
- Permite ver, crear, editar y eliminar empresas (solo administradores de iPRA pueden ver todas las empresas)
- Incluye acceso a la gestión de usuarios de cada empresa

### Gestión de usuarios

- Disponible solo para administradores
- Permite ver, crear, editar y eliminar usuarios
- Los administradores de iPRA pueden gestionar usuarios de cualquier empresa
- Los administradores de otras empresas solo pueden gestionar usuarios de su propia empresa
- Las contraseñas de los usuarios no son visibles ni siquiera para los administradores

### Configuración de usuario

- Disponible para todos los usuarios desde el menú principal
- Permite modificar datos personales:
  - Nombre
  - Correo electrónico
  - Contraseña
- Los cambios afectan únicamente al usuario que realiza la modificación
- La contraseña actual es requerida para confirmar cualquier cambio de seguridad

## Navegación entre pantallas

![Diagrama de navegación entre pantallas](navigation_diagram.svg)

El diagrama muestra el flujo de navegación entre las diferentes pantallas de la aplicación:

- Desde la pantalla de **inicio de sesión** se accede al **menú principal**
- Desde el **menú principal** se puede acceder a:
  - **Tableros**: Visualización y gestión de tableros
  - **Empresas**: Gestión de empresas
  - **Configuración de usuario**: Modificación de datos personales
- Desde la pantalla de **tableros** se puede:
  - Volver al **menú principal**
  - Acceder a **empresas**
  - Abrir la **configuración de tablero** para modificar sus propiedades
  - Abrir la **configuración de widgets** para personalizar elementos visuales
- Desde la pantalla de **empresas** se puede:
  - Acceder a la **gestión de usuarios** de una empresa específica
  - Ver los **tableros de una empresa** específica
  - Abrir la **ficha de empresa** para ver o editar sus detalles
- Desde la pantalla de **usuarios** se puede abrir la **ficha de usuario** para ver o editar sus detalles
- Desde cualquier pantalla se puede **cerrar sesión** y volver a la pantalla de inicio

## Temas de la aplicación

La aplicación iPRA Dashboard ofrece varios temas visuales que el usuario puede seleccionar según sus preferencias:

- **Menta Zen**: Tema por defecto con tonos verdes suaves
- **Tron**: Tema oscuro con acentos azules brillantes
- **Neumórfico**: Tema con efecto de relieve y sombras suaves
- **Azul Nuboso**: Tema con tonos azules suaves

Los temas son una preferencia personal de cada usuario y se almacenan en el navegador local. Esto significa que:

- Cada usuario puede tener su propio tema preferido
- El tema seleccionado se recordará la próxima vez que el usuario inicie sesión en el mismo navegador
- Si el usuario accede desde otro dispositivo o navegador, deberá seleccionar su tema preferido nuevamente

## Tableros

Los tableros son la parte central de la aplicación iPRA Dashboard. Cada tablero pertenece a una empresa específica y solo los usuarios de esa empresa pueden verlo.

### Configuración de tableros

Cada tablero puede personalizarse con las siguientes opciones:

- **Nombre**: Identificador descriptivo del tablero
- **Dimensiones**: Ancho y alto del tablero en píxeles
- **Color de fondo**: Color del fondo del tablero
- **Cuadrícula**: Activar/desactivar la cuadrícula y cambiar su color
- **Texto de widgets**: Color por defecto para el texto de los widgets
- **Fondo de widgets**: Color por defecto para el fondo de los widgets
- **Bordes de widgets**: Mostrar/ocultar los bordes de los widgets
- **Widgets transparentes**: Hacer transparentes los fondos de los widgets

Si al cambiar el tamaño de un tablero los widgets no caben en la nueva configuración, estos se reajustarán automáticamente para adaptarse al nuevo tamaño.

### Operaciones con tableros

Los tableros admiten diversas operaciones que permiten una gestión eficiente:

- **Cambio rápido**: Desde la pantalla de tableros, existe un botón de cambio rápido que permite alternar entre los diferentes tableros disponibles sin necesidad de volver al menú principal.
- **Crear nuevo tablero**: Permite crear un tablero vacío con la configuración predeterminada.
- **Clonar tablero**: Crea una copia exacta del tablero actual, incluyendo todos sus widgets y configuraciones.
- **Vaciar tablero**: Elimina todos los widgets del tablero actual manteniendo su configuración.
- **Eliminar tablero**: Elimina completamente el tablero seleccionado (requiere confirmación).

Estas operaciones están disponibles en el menú de opciones de la pantalla de tableros, y su disponibilidad depende de los permisos del usuario.

### Widgets

Los widgets son los elementos visuales que se colocan en los tableros para mostrar información. Cada widget tiene un propósito específico y puede configurarse de manera independiente.

#### Manipulación de widgets

Los widgets ofrecen una gran flexibilidad en su uso:

- **Mover**: Los widgets pueden arrastrarse libremente a cualquier posición dentro del tablero.
- **Redimensionar**: Cada widget puede redimensionarse desde sus esquinas o bordes para adaptarse a las necesidades de visualización.
- **Editar**: Al hacer doble clic sobre un widget, se abre un diálogo de configuración específico para ese tipo de widget.
- **Duplicar**: Es posible duplicar widgets existentes, lo que resulta útil para crear múltiples visualizaciones del mismo tipo de datos con diferentes configuraciones.

Desde la pantalla de configuración de un widget también es posible ajustar su tamaño mediante controles numéricos precisos, lo que permite un posicionamiento exacto.

#### Tipos de widgets

Los tableros pueden contener diferentes tipos de widgets para visualizar información:

- **Etiqueta de texto**: Muestra texto estático personalizable.
- **Etiqueta de valor**: Muestra un valor numérico con su unidad de medida.
- **Gauge**: Muestra un medidor tipo velocímetro con rangos configurables.
- **Gauge porcentual**: Muestra un medidor de porcentaje con indicador visual.
- **Gráfica**: Muestra datos históricos en diferentes formatos visuales.
- **Últimos**: Muestra los últimos N valores registrados en formato de lista.

Es posible tener múltiples widgets del mismo tipo en un tablero, por ejemplo, varias gráficas que muestren diferentes métricas o la misma métrica con diferentes configuraciones visuales.

#### Configuración de widgets

Cada tipo de widget tiene su propio conjunto de parámetros configurables:

##### Configuración visual común a todos los widgets
- **Color de texto**: Permite establecer un color de texto específico para el widget, diferente al color por defecto del tablero.
- **Color de fondo**: Permite establecer un color de fondo específico para el widget, diferente al color por defecto del tablero.
- **Color de borde**: Permite establecer un color de borde específico para el widget, diferente al configurado en el tablero.
- **Botón "Usar valores por defecto"**: Permite revertir cualquier configuración de color personalizada y volver a utilizar los valores por defecto definidos en el tablero.

Cuando se realizan cambios en la configuración de colores del tablero, estos se aplican instantáneamente a todos los widgets que estén utilizando la configuración por defecto.

##### Etiqueta de texto
- **Texto**: El contenido textual que se mostrará en el widget.

##### Etiqueta de valor
- **Valor a mostrar**: Selección del tipo de dato a mostrar (temperatura, presión, humedad, etc.).

##### Gauge
- **Tipo de gauge**: Selección del tipo de medición (temperatura, presión, velocidad del viento, humedad).
- **Valores mínimo y máximo**: Rango de valores para el medidor.
- **Unidad**: Unidad de medida que se mostrará junto al valor.

##### Gauge porcentual
- **Tipo de porcentaje**: Selección del tipo de medición porcentual (batería, memoria, CPU, disco).

##### Gráfica
- **Valor a mostrar**: Selección del tipo de dato a visualizar.
- **Tipo de gráfica**: Formato visual (línea, barras, circular, anillo, radar, área polar).
- **Número de puntos**: Cantidad de puntos de datos históricos a mostrar (5-50).
- **Mostrar leyenda**: Opción para mostrar u ocultar la leyenda de la gráfica.
- **Mostrar cuadrícula**: Opción para mostrar u ocultar la cuadrícula de fondo.

##### Últimos
- **Tipo de valor**: Selección del tipo de dato a mostrar en la lista.
- **Número de valores**: Cantidad de valores históricos a mostrar.
- **Incluir unidades**: Opción para mostrar u ocultar las unidades de medida.
- **Mostrar hora completa**: Opción para mostrar la fecha y hora completa o solo la hora.
- **Incluir minutos y segundos**: Opción para mostrar u ocultar los minutos y segundos en la marca de tiempo.

## Datos de la aplicación

iPRA Dashboard es una aplicación de demostración que utiliza datos aleatorios generados cada 5 segundos. Estos datos simulan información que en un entorno real provendría de una API REST.

Los datos generados incluyen:
- Temperatura
- Presión
- Velocidad del viento
- Dirección del viento
- Humedad
- Precipitación
- Niveles de batería
- Uso de memoria
- Uso de CPU
- Uso de disco

Estos datos se almacenan temporalmente en el navegador del usuario y se actualizan automáticamente, permitiendo ver cambios en tiempo real en los widgets.

## Funcionalidades genéricas

iPRA Dashboard incluye una serie de funcionalidades comunes a toda la aplicación que mejoran la experiencia de usuario y facilitan la interacción con el sistema.

### Atajos de teclado y navegación

- La tecla **Escape** cierra los diálogos y pantallas modales activas
- El botón **Atrás** del navegador:
  - Si hay un diálogo abierto, lo cierra sin cambiar de pantalla
  - Si no hay diálogos abiertos, vuelve a la pantalla anterior
- Se admite navegación por hash en la URL:
  - Permite acceder directamente a secciones específicas mediante URL
  - Verifica automáticamente los permisos del usuario
  - Comprueba que el usuario esté autenticado antes de mostrar cualquier contenido

### Persistencia de sesión

- La aplicación recuerda al usuario identificado entre sesiones
- No es necesario iniciar sesión cada vez que se accede a la aplicación
- La sesión se mantiene activa hasta que el usuario cierra sesión explícitamente
- Por seguridad, se verifica periódicamente la validez de la sesión

### Interacción con diálogos

- Todos los diálogos se pueden mover libremente por la pantalla
- Los diálogos mantienen su estado al cerrarlos y volverlos a abrir
- Los formularios dentro de diálogos validan los datos en tiempo real
- Los diálogos se adaptan automáticamente al tamaño de pantalla del dispositivo, manteniendo todas sus funcionalidades pero reorganizando sus elementos para una mejor experiencia de usuario
- En dispositivos móviles, los diálogos utilizan controles optimizados para pantallas táctiles
- En dispositivos de escritorio, los diálogos aprovechan el espacio adicional para mostrar más información y opciones de manera simultánea

### Gestión de listados

Los listados de entidades (empresas, usuarios, tableros) ofrecen funcionalidades avanzadas:

- **Exportación a CSV**: Permite exportar los datos mostrados a un archivo CSV
- **Configuración de columnas**:
  - Selección de columnas a mostrar/ocultar
  - Ajuste del ancho de cada columna
  - Reordenación de columnas mediante arrastrar y soltar
- **Ordenación**: Todos los listados permiten ordenar por cualquier columna visible
- **Opciones específicas**:
  - Opciones a nivel de cabecera que afectan a todo el listado
  - Opciones a nivel de fila que afectan solo al registro seleccionado

### Interacción gestual

- La aplicación soporta interacción gestual en dispositivos táctiles:
  - Deslizar horizontalmente para navegar entre registros
  - Pellizcar para hacer zoom en gráficos y tableros
  - Arrastrar para mover widgets en los tableros
- La interacción gestual también funciona con ratón en ordenadores:
  - Clic y arrastrar simula el deslizamiento táctil
  - La rueda del ratón permite hacer zoom
  - Arrastrar y soltar para mover elementos

## Compatibilidad con dispositivos

iPRA Dashboard está diseñada para funcionar en todo tipo de dispositivos:

- **Ordenadores de escritorio**: Experiencia completa con todas las funcionalidades
- **Tablets**: Interfaz adaptada al tamaño de pantalla
- **Smartphones**: Interfaz optimizada para pantallas pequeñas

En dispositivos móviles, si se carga un tablero diseñado para pantallas más grandes, la aplicación permite hacer scroll para ver todo el contenido.

La aplicación se adapta automáticamente al tamaño de la pantalla, reorganizando elementos y ajustando controles para ofrecer la mejor experiencia posible en cada dispositivo.

## Conclusiones

iPRA Dashboard representa una solución integral para la visualización y monitorización de datos en tiempo real, diseñada con un enfoque centrado en la experiencia del usuario. Cada aspecto de la aplicación ha sido cuidadosamente desarrollado para garantizar una interacción fluida, intuitiva y altamente personalizable.

La plataforma destaca por su agilidad y facilidad de uso, permitiendo a usuarios de todos los niveles técnicos configurar potentes visualizaciones de datos sin necesidad de conocimientos especializados. La flexibilidad de sus tableros personalizados, combinada con la amplia variedad de widgets disponibles, ofrece posibilidades ilimitadas para adaptar la visualización a las necesidades específicas de cada empresa.

Hemos implementado numerosas funcionalidades que anticipan las necesidades del usuario, creando una experiencia que parece "leer la mente" de quien utiliza la aplicación. Desde la persistencia automática de sesiones hasta la interacción gestual intuitiva, cada detalle ha sido diseñado para que el usuario pueda concentrarse en lo que realmente importa: sus datos.

iPRA Dashboard es, en definitiva, la herramienta perfecta para quienes buscan una solución completa, flexible y fácil de usar para la monitorización de datos en tiempo real, adaptándose a cualquier entorno empresarial y evolucionando con las necesidades de sus usuarios.
