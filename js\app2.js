/**
 * Extensión de la aplicación principal iPRA
 * Contiene funciones genéricas adicionales para la aplicación
 */

//Oculta todos los contenedores
function hideAllContainers() {
    document.querySelectorAll('.container').forEach(container => {
        container.classList.add('hidden');
    });
}

// Función para mostrar/ocultar contenedores principales
async function showContainer(container, manager = null) {
    // Mostrar el contenedor solicitado con animación
    const containerElement = typeof container === 'string' ? document.getElementById(container) : container;
    if (!containerElement) return;

    hideAllContainers();

    // Quitar clase hidden para que sea visible
    containerElement.classList.remove('hidden');

    // Añadir clase de animación de entrada
    containerElement.classList.add('container-entering');

    // Escuchar el evento de fin de animación
    const handleAnimationEnd = (e) => {
        // Solo procesar si es el evento del contenedor
        if (e.target !== containerElement) return;

        // Quitar la clase de animación
        containerElement.classList.remove('container-entering');

        // Eliminar el listener para evitar duplicados
        containerElement.removeEventListener('animationend', handleAnimationEnd);
    };

    // Añadir el listener para la animación
    containerElement.addEventListener('animationend', handleAnimationEnd);

    // Añadir a la pila UI con el manager responsable
    await pushToUIStack(containerElement, 'container', manager);
}

// Función para navegar entre pantallas
async function navigateTo(screen, params = {}) {
    console.log(`Navegando a: ${screen}`, JSON.stringify(params));

    // Si llegamos aquí, no encontramos la pantalla en la pila
    // Continuar con la navegación normal
    switch (screen) {
        case 'login':
            authManager.showLogin();
            break;
        case 'main-menu':
            // Cerrar el menú desplegable si está abierto
            closeMenuUniversal();
            emptyUIStack();
            await showContainer('main-menu-container');
            updateUrlHash('main-menu');

            // Restaurar el tema guardado en localStorage (preferencia del usuario)
            if (themeManager) {
                // Obtener el tema guardado en localStorage
                const savedTheme = localStorage.getItem('ipram_theme');
                if (savedTheme) {
                    // Verificar si el tema actual es diferente al guardado
                    if (!document.documentElement.classList.contains(`theme-${savedTheme}`)) {
                        // Aplicar el tema guardado
                        themeManager.changeTheme(savedTheme);
                        console.log(`Tema restaurado a la preferencia del usuario: ${savedTheme}`);
                    }
                }
            }

            // Actualizar el encabezado con el login del usuario actual
            if (window.mainMenuManager) {
                window.mainMenuManager.updateHeaderWithUserLogin();
            }
            break;
        case 'dashboard':
            updateUrlHash('dashboard');
            window.dashboardManager.useDashboard((params && params.dashboardId) || null);
            break;
        default:
            // Crear una nueva instancia de EntityManager
            const newEntityManager = new EntityManager();
            // Registrar los manejadores de entidades desde la instancia global
            if (entityManager && entityManager.entityHandlers) {
                Object.keys(entityManager.entityHandlers).forEach(type => {
                    newEntityManager.registerEntityHandler(type, entityManager.entityHandlers[type]);
                });
            }

            console.log(`navigateTo: Inicializando EntityManager con params:`, JSON.stringify(params));

            // Inicializar y esperar a que se carguen los datos antes de mostrar
            newEntityManager.init(params.entityType, params)  // Pasar todos los parámetros, no solo entityType
                .then(entityManagerInstance => {
                    // Una vez cargados los datos, mostrar la interfaz
                    console.log(`navigateTo: EntityManager inicializado con params:`,
                        JSON.stringify(entityManagerInstance.params));
                    entityManagerInstance.show();
                })
                .catch(error => {
                    console.error(`Error al inicializar el gestor de entidades:`, error);
                    // En caso de error, volver al menú principal
                    navigateTo('main-menu');
                });
            break;
    }
}

// La función normalizeString() ha sido eliminada.
// Usar normalizeText() de app.js en su lugar.

// Función para formatear fechas
function formatDate(date) {
    if (!date) return '';
    if (typeof date === 'string') {
        date = new Date(date);
    }
    return date.toLocaleDateString('es-ES', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
}

// Función para validar email
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(String(email).toLowerCase());
}

// Función para generar un ID único
function generateUniqueId() {
    return Date.now() + Math.floor(Math.random() * 1000);
}

// Función para mostrar mensajes de notificación
function showNotification(message, type = 'info', duration = 3000) {
    // Crear el elemento de notificación si no existe
    let notificationContainer = document.getElementById('notification-container');
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'notification-container';
        document.body.appendChild(notificationContainer);
    }

    // Crear la notificación
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    // Configurar eventos de animación
    notification.addEventListener('animationend', function handleAnimEnd(event) {
        if (event.animationName === 'notificationHide') {
            notification.remove();
            notification.removeEventListener('animationend', handleAnimEnd);
        }
    });

    // Añadir la notificación al contenedor
    notificationContainer.appendChild(notification);

    // Forzar un reflow para asegurar que la transición funcione
    notification.offsetHeight;

    // Mostrar la notificación
    notification.classList.add('show');

    // Configurar temporizador para ocultar la notificación
    const hideTimer = setTimeout(() => {
        notification.classList.remove('show');
        notification.classList.add('hide');
        clearTimeout(hideTimer);
    }, duration);
}

// Función para mostrar diálogo de confirmación
function showConfirmDialog(message, onConfirm, onCancel) {
    // Esta función será implementada en dialogs.js
    if (window.dialogManager) {
        dialogManager.showConfirmDialog(message, onConfirm, onCancel);
    } else {
        // Fallback si no está disponible dialogManager
        if (confirm(message)) {
            if (onConfirm) onConfirm();
        } else {
            if (onCancel) onCancel();
        }
    }
}