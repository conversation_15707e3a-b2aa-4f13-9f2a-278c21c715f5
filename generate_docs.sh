#!/bin/bash

# Script para generar documentación usando WeasyPrint

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Cargar configuración
if [ ! -f "config.conf" ]; then
    error "Archivo config.conf no encontrado. Ejecuta configure.sh primero."
fi

source config.conf

# Configurar entorno regional
if [ -n "$LC_NUMERIC" ]; then
    export LC_NUMERIC="$LC_NUMERIC"
    log "Configuración regional: LC_NUMERIC=$LC_NUMERIC"
fi

# Función para verificar WeasyPrint
check_weasyprint() {
    if [ ! -d "venv" ]; then
        error "Entorno virtual Python no encontrado. Ejecuta install.sh primero."
    fi
    
    source venv/bin/activate
    
    if ! python -c "import weasyprint" 2>/dev/null; then
        deactivate
        error "WeasyPrint no está instalado. Ejecuta install.sh primero."
    fi
    
    log "WeasyPrint verificado correctamente"
}

# Función para crear directorio de documentación
create_docs_dir() {
    mkdir -p "$DOCS_DIR"
    mkdir -p "$DOCS_DIR/html"
    mkdir -p "$DOCS_DIR/pdf"
    mkdir -p "$DOCS_DIR/assets"
    
    log "Directorios de documentación creados"
}

# Función para generar documentación del manual de usuario
generate_user_manual() {
    log "Generando manual de usuario..."
    
    local input_file="doc/manual_usuario.md"
    local html_file="$DOCS_DIR/html/manual_usuario.html"
    local pdf_file="$DOCS_DIR/pdf/manual_usuario.pdf"
    
    if [ ! -f "$input_file" ]; then
        warn "Manual de usuario no encontrado: $input_file"
        return 1
    fi
    
    # Activar entorno virtual
    source venv/bin/activate
    
    # Convertir Markdown a HTML
    if command -v pandoc &> /dev/null; then
        log "Convirtiendo Markdown a HTML con Pandoc..."
        pandoc "$input_file" -o "$html_file" \
            --standalone \
            --css="../assets/docs.css" \
            --metadata title="Manual de Usuario IPRA_I" \
            --toc \
            --toc-depth=3
    else
        # Crear HTML básico si no hay Pandoc
        log "Pandoc no disponible, creando HTML básico..."
        create_basic_html "$input_file" "$html_file" "Manual de Usuario IPRA_I"
    fi
    
    # Generar PDF con WeasyPrint
    log "Generando PDF con WeasyPrint..."
    python -c "
import weasyprint
import os

# Configurar base URL para recursos relativos
base_url = 'file://' + os.path.abspath('$DOCS_DIR/html/')

# Generar PDF
weasyprint.HTML(filename='$html_file', base_url=base_url).write_pdf('$pdf_file')
print('PDF generado: $pdf_file')
"
    
    deactivate
    
    log "Manual de usuario generado:"
    log "  HTML: $html_file"
    log "  PDF: $pdf_file"
}

# Función para generar documentación técnica
generate_technical_docs() {
    log "Generando documentación técnica..."
    
    local html_file="$DOCS_DIR/html/documentacion_tecnica.html"
    local pdf_file="$DOCS_DIR/pdf/documentacion_tecnica.pdf"
    
    # Crear documentación técnica combinando varios archivos
    create_technical_html "$html_file"
    
    # Activar entorno virtual
    source venv/bin/activate
    
    # Generar PDF
    log "Generando PDF técnico con WeasyPrint..."
    python -c "
import weasyprint
import os

base_url = 'file://' + os.path.abspath('$DOCS_DIR/html/')
weasyprint.HTML(filename='$html_file', base_url=base_url).write_pdf('$pdf_file')
print('PDF técnico generado: $pdf_file')
"
    
    deactivate
    
    log "Documentación técnica generada:"
    log "  HTML: $html_file"
    log "  PDF: $pdf_file"
}

# Función para crear HTML básico desde Markdown
create_basic_html() {
    local md_file="$1"
    local html_file="$2"
    local title="$3"
    
    cat > "$html_file" << EOF
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>$title</title>
    <link rel="stylesheet" href="../assets/docs.css">
</head>
<body>
    <div class="container">
        <h1>$title</h1>
        <div class="content">
EOF
    
    # Conversión básica de Markdown a HTML
    sed -e 's/^# \(.*\)/<h1>\1<\/h1>/' \
        -e 's/^## \(.*\)/<h2>\1<\/h2>/' \
        -e 's/^### \(.*\)/<h3>\1<\/h3>/' \
        -e 's/^\* \(.*\)/<li>\1<\/li>/' \
        -e 's/^$/<\/p><p>/' \
        "$md_file" >> "$html_file"
    
    cat >> "$html_file" << EOF
        </div>
    </div>
</body>
</html>
EOF
}

# Función para crear documentación técnica HTML
create_technical_html() {
    local html_file="$1"
    
    cat > "$html_file" << EOF
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentación Técnica IPRA_I</title>
    <link rel="stylesheet" href="../assets/docs.css">
</head>
<body>
    <div class="container">
        <h1>Documentación Técnica IPRA_I</h1>
        
        <div class="toc">
            <h2>Índice</h2>
            <ul>
                <li><a href="#arquitectura">Arquitectura del Sistema</a></li>
                <li><a href="#instalacion">Instalación</a></li>
                <li><a href="#configuracion">Configuración</a></li>
                <li><a href="#websockets">WebSockets y Colaboración</a></li>
                <li><a href="#build">Sistema de Build</a></li>
                <li><a href="#api">API del Servidor</a></li>
            </ul>
        </div>
        
        <div class="content">
            <section id="arquitectura">
                <h2>Arquitectura del Sistema</h2>
                <p>IPRA_I es una aplicación web para dashboards colaborativos que consta de:</p>
                <ul>
                    <li><strong>Frontend:</strong> Aplicación web SPA con JavaScript vanilla</li>
                    <li><strong>Backend:</strong> Servidor Node.js con WebSockets para colaboración en tiempo real</li>
                    <li><strong>Almacenamiento:</strong> IndexedDB en el navegador</li>
                    <li><strong>Build:</strong> Sistema de minificación y combinación de archivos</li>
                </ul>
                
                <h3>Componentes Principales</h3>
                <ul>
                    <li><strong>WidgetManager:</strong> Gestión de widgets en el dashboard</li>
                    <li><strong>DashboardManager:</strong> Gestión de dashboards y navegación</li>
                    <li><strong>CollaborationClient:</strong> Cliente WebSocket para colaboración</li>
                    <li><strong>DatabaseManager:</strong> Interfaz con IndexedDB</li>
                </ul>
            </section>
            
            <section id="instalacion">
                <h2>Instalación</h2>
                <p>El proceso de instalación se realiza mediante scripts automatizados:</p>
                <ol>
                    <li><code>./configure.sh</code> - Configurar puerto y opciones</li>
                    <li><code>./install.sh</code> - Instalar dependencias y configurar sistema</li>
                    <li><code>./start.sh</code> - Iniciar el servidor</li>
                </ol>
                
                <h3>Requisitos del Sistema</h3>
                <ul>
                    <li>Node.js 16+ y npm</li>
                    <li>Python 3.7+ para WeasyPrint</li>
                    <li>Navegador moderno con soporte para WebSockets e IndexedDB</li>
                </ul>
            </section>
            
            <section id="configuracion">
                <h2>Configuración</h2>
                <p>La configuración se centraliza en <code>config.conf</code>:</p>
                <ul>
                    <li><strong>SERVER_PORT:</strong> Puerto del servidor (por defecto: 3000)</li>
                    <li><strong>LC_NUMERIC:</strong> Configuración regional para números</li>
                    <li><strong>BUILD_DIR:</strong> Directorio de archivos minificados</li>
                    <li><strong>LOG_LEVEL:</strong> Nivel de logging (debug, info, warn, error)</li>
                </ul>
            </section>
            
            <section id="websockets">
                <h2>WebSockets y Colaboración</h2>
                <p>El sistema de colaboración permite que múltiples usuarios editen el mismo dashboard simultáneamente:</p>
                
                <h3>Características</h3>
                <ul>
                    <li>Reconexión automática con backoff exponencial</li>
                    <li>Sincronización de cambios en tiempo real</li>
                    <li>Indicadores visuales de usuarios conectados</li>
                    <li>Cola de mensajes durante desconexión</li>
                </ul>
                
                <h3>Mensajes WebSocket</h3>
                <ul>
                    <li><code>join_dashboard</code> - Unirse a un dashboard</li>
                    <li><code>widget_update</code> - Actualizar widget</li>
                    <li><code>widget_create</code> - Crear widget</li>
                    <li><code>widget_delete</code> - Eliminar widget</li>
                </ul>
            </section>
            
            <section id="build">
                <h2>Sistema de Build</h2>
                <p>El sistema de build combina y minifica archivos para producción:</p>
                
                <h3>Comandos</h3>
                <ul>
                    <li><code>npm run build</code> - Build completo</li>
                    <li><code>npm run build:js</code> - Solo JavaScript</li>
                    <li><code>npm run build:css</code> - Solo CSS</li>
                    <li><code>npm run watch</code> - Build automático al cambiar archivos</li>
                </ul>
                
                <h3>Herramientas</h3>
                <ul>
                    <li><strong>UglifyJS:</strong> Minificación de JavaScript</li>
                    <li><strong>clean-css:</strong> Minificación de CSS</li>
                    <li><strong>Chokidar:</strong> Vigilancia de archivos</li>
                </ul>
            </section>
            
            <section id="api">
                <h2>API del Servidor</h2>
                <p>El servidor expone los siguientes endpoints:</p>
                
                <h3>HTTP Endpoints</h3>
                <ul>
                    <li><code>GET /health</code> - Estado del servidor</li>
                </ul>
                
                <h3>WebSocket Events</h3>
                <p>Ver sección de WebSockets para detalles de los eventos disponibles.</p>
            </section>
        </div>
        
        <footer>
            <p>Generado el $(date) con configuración regional: $LC_NUMERIC</p>
        </footer>
    </div>
</body>
</html>
EOF
}

# Función para crear CSS para documentación
create_docs_css() {
    local css_file="$DOCS_DIR/assets/docs.css"
    
    cat > "$css_file" << EOF
/* Estilos para documentación IPRA_I */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: white;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

h1 {
    color: #2c3e50;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    margin-bottom: 30px;
}

h2 {
    color: #34495e;
    margin-top: 30px;
    margin-bottom: 15px;
    border-left: 4px solid #3498db;
    padding-left: 15px;
}

h3 {
    color: #7f8c8d;
    margin-top: 20px;
    margin-bottom: 10px;
}

p {
    margin-bottom: 15px;
    text-align: justify;
}

ul, ol {
    margin-left: 30px;
    margin-bottom: 15px;
}

li {
    margin-bottom: 5px;
}

code {
    background-color: #ecf0f1;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.toc {
    background-color: #ecf0f1;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 30px;
}

.toc h2 {
    margin-top: 0;
    border-left: none;
    padding-left: 0;
}

.toc ul {
    margin-left: 20px;
}

.toc a {
    color: #3498db;
    text-decoration: none;
}

.toc a:hover {
    text-decoration: underline;
}

section {
    margin-bottom: 40px;
}

footer {
    margin-top: 50px;
    padding-top: 20px;
    border-top: 1px solid #bdc3c7;
    text-align: center;
    color: #7f8c8d;
    font-size: 0.9em;
}

/* Estilos para impresión/PDF */
@media print {
    body {
        background-color: white;
    }
    
    .container {
        box-shadow: none;
        max-width: none;
    }
    
    h1, h2 {
        page-break-after: avoid;
    }
    
    section {
        page-break-inside: avoid;
    }
}

/* Configuración regional para números */
.number {
    font-family: monospace;
}

/* Español: usar coma decimal */
.es-number::after {
    content: " (formato: 1,23)";
    font-size: 0.8em;
    color: #7f8c8d;
}

/* Inglés: usar punto decimal */
.en-number::after {
    content: " (format: 1.23)";
    font-size: 0.8em;
    color: #7f8c8d;
}
EOF
    
    log "CSS de documentación creado: $css_file"
}

# Función para generar documentación de configuración
generate_config_docs() {
    log "Generando documentación de configuración..."
    
    local html_file="$DOCS_DIR/html/configuracion.html"
    local pdf_file="$DOCS_DIR/pdf/configuracion.pdf"
    
    cat > "$html_file" << EOF
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuración IPRA_I</title>
    <link rel="stylesheet" href="../assets/docs.css">
</head>
<body>
    <div class="container">
        <h1>Configuración IPRA_I</h1>
        
        <h2>Configuración Actual</h2>
        <p>Generado el: $(date)</p>
        
        <h3>Servidor</h3>
        <ul>
            <li><strong>Host:</strong> $SERVER_HOST</li>
            <li><strong>Puerto:</strong> $SERVER_PORT</li>
            <li><strong>URL:</strong> http://$SERVER_HOST:$SERVER_PORT</li>
        </ul>
        
        <h3>Regional</h3>
        <ul>
            <li><strong>LC_NUMERIC:</strong> $LC_NUMERIC</li>
            <li><strong>Formato de números:</strong> $([ "$LC_NUMERIC" = "es_ES" ] && echo "1,23 (coma decimal)" || echo "1.23 (punto decimal)")</li>
        </ul>
        
        <h3>Directorios</h3>
        <ul>
            <li><strong>Build:</strong> $BUILD_DIR</li>
            <li><strong>Documentación:</strong> $DOCS_DIR</li>
            <li><strong>Logs:</strong> $LOG_DIR</li>
        </ul>
        
        <h3>Archivos de Build</h3>
        <ul>
            <li><strong>JavaScript:</strong> $COMBINED_JS_FILE</li>
            <li><strong>CSS:</strong> $COMBINED_CSS_FILE</li>
        </ul>
        
        <h3>WebSockets</h3>
        <ul>
            <li><strong>Reconexión inicial:</strong> ${WEBSOCKET_RECONNECT_INITIAL}ms</li>
            <li><strong>Reconexión máxima:</strong> ${WEBSOCKET_RECONNECT_MAX}ms</li>
        </ul>
        
        <h3>Logging</h3>
        <ul>
            <li><strong>Nivel:</strong> $LOG_LEVEL</li>
        </ul>
        
        <h2>Comandos Disponibles</h2>
        <ul>
            <li><code>./configure.sh</code> - Configurar sistema</li>
            <li><code>./install.sh</code> - Instalar dependencias</li>
            <li><code>./start.sh</code> - Iniciar servidor</li>
            <li><code>./stop.sh</code> - Detener servidor</li>
            <li><code>./status.sh</code> - Ver estado</li>
            <li><code>./logs.sh</code> - Ver logs</li>
            <li><code>./generate_docs.sh</code> - Generar documentación</li>
        </ul>
    </div>
</body>
</html>
EOF
    
    # Activar entorno virtual y generar PDF
    source venv/bin/activate
    python -c "
import weasyprint
import os
base_url = 'file://' + os.path.abspath('$DOCS_DIR/html/')
weasyprint.HTML(filename='$html_file', base_url=base_url).write_pdf('$pdf_file')
print('PDF de configuración generado: $pdf_file')
"
    deactivate
    
    log "Documentación de configuración generada:"
    log "  HTML: $html_file"
    log "  PDF: $pdf_file"
}

# Función para mostrar ayuda
show_help() {
    echo "Uso: $0 [OPCIONES]"
    echo ""
    echo "Opciones:"
    echo "  -h, --help       Mostrar esta ayuda"
    echo "  -a, --all        Generar toda la documentación (por defecto)"
    echo "  -u, --user       Solo manual de usuario"
    echo "  -t, --technical  Solo documentación técnica"
    echo "  -c, --config     Solo documentación de configuración"
    echo "  --html-only      Solo generar HTML (no PDF)"
    echo "  --pdf-only       Solo generar PDF (requiere HTML existente)"
    echo ""
    echo "Ejemplos:"
    echo "  $0               # Generar toda la documentación"
    echo "  $0 -u            # Solo manual de usuario"
    echo "  $0 --html-only   # Solo archivos HTML"
    echo ""
}

# Función principal
main() {
    log "=== GENERADOR DE DOCUMENTACIÓN IPRA_I ==="
    
    check_weasyprint
    create_docs_dir
    create_docs_css
    
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        -u|--user)
            generate_user_manual
            ;;
        -t|--technical)
            generate_technical_docs
            ;;
        -c|--config)
            generate_config_docs
            ;;
        --html-only)
            warn "Función --html-only no implementada completamente"
            ;;
        --pdf-only)
            warn "Función --pdf-only no implementada completamente"
            ;;
        -a|--all|"")
            generate_user_manual
            generate_technical_docs
            generate_config_docs
            ;;
        *)
            error "Opción desconocida: $1. Usa -h para ayuda."
            ;;
    esac
    
    log "=== DOCUMENTACIÓN GENERADA ==="
    log "Directorio: $DOCS_DIR"
    log "Archivos HTML: $DOCS_DIR/html/"
    log "Archivos PDF: $DOCS_DIR/pdf/"
}

# Ejecutar función principal
main "$@"
