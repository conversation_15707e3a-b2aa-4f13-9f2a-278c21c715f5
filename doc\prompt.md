# Instrucciones para Desarrollo de Código en la Aplicación de Tableros

## 0. Estructura del Proyecto

Según los comentarios en dashboard_loader.js, la aplicación está estructurada de la siguiente manera:

### Archivos Core

- app.js: Punto de entrada principal de la aplicación
- app2.js: Gestiona la navegación y las rutas de la aplicación
- auth.js: Maneja la autenticación y permisos de usuario
- database.js: Proporciona acceso a la base de datos (simulada en localStorage)
- dialogs.js: Gestiona los diálogos y modales de la aplicación
- entity_manager.js: Maneja la gestión genérica de entidades (CRUD)
- user_profile.js: Gestiona el perfil del usuario actual

### Módulos de Tableros

- dashboard.js: Gestiona la visualización y comportamiento de los tableros
- widgets.js: Maneja la creación, edición y eliminación de widgets
- widget_fields.js: Define para cada widget los campos que tiene disponibles para su formulario de configuración.
- form_fields_accordion.js: Los campos del formulario de edición del widget se crean en forma de acordeón.
- dashboard_loader.js: Carga y gestiona los ficheros javascripts relacionados con tableros.

### Módulos de Entidades

- empresas.js: Gestiona las empresas en el sistema
- usuarios.js: Gestiona los usuarios del sistema
- tableros.js: Gestiona los tableros disponibles

### Utilidades

- html.js: Proporciona utilidades para crear elementos HTML de manera consistente.


## 1. Principios para Generar Código como Ingeniero Experimentado

- Simplicidad ante todo: Implementa la solución más simple que funcione correctamente. Evita complejidad innecesaria.
- No dupliques código: Utiliza funciones auxiliares existentes siempre que sea posible. 
- Al crear código, refactoriza el código para evitar duplicados y mira si puedes refactorizar con código existente.
- Consistencia: Mantén el mismo estilo y patrones que el código existente.
- Minimalismo: Usa el mínimo código necesario para lograr la funcionalidad requerida.
- Revisión rigurosa: Revisa el código al menos tres veces antes de entregarlo.
- Nombres descriptivos: Usa nombres de variables y funciones que describan claramente su propósito.
- Comentarios útiles: Documenta el propósito de las funciones, no lo obvio.
- Manejo de errores: Considera casos límite y maneja posibles errores.
- Evita anidamiento excesivo: Prefiere retornos tempranos y código plano.
- Prueba antes de entregar: Asegúrate de que el código funciona como se espera.
- No inventes archivos o componentes que no estén explícitamente mencionados en el código proporcionado.
- Limítate estrictamente a la información que puedes verificar en el código compartido.
- Si no estás seguro de algo, indica claramente que es una suposición o pregunta, no lo presentes como un hecho.
- Si no estás seguro al 95%, te paras y preguntas.

## 2. Conocimiento Adquirido sobre la Aplicación

- La aplicación es un sistema de tableros (dashboards) con widgets configurables.
- Utiliza un patrón de diseño modular con JavaScript.
- Los widgets pueden ser de diferentes tipos y configurarse según necesidades específicas.
- Los tableros están asociados a empresas específicas.
- Existe un sistema de permisos basado en roles (el rol en la app está en la propiedad 'tipo' del usuario) de usuario.
- La aplicación usa Material Design Icons para los elementos visuales.
- Los formularios siguen una estructura específica con clases CSS como form-group y checkbox-group.
- Existe un sistema de gestión de diálogos para interacciones con el usuario.
- Los datos de widgets se almacenan y recuperan mediante un sistema de persistencia en localStorage.
- Los usuarios de tipo (rol) 'admin' de la empresa 1 pueden acceder a todas las empresas, sus usuarios y tableros.
- Los usuarios del resto de empresas sólo pueden acceder a sus propios tableros y ver los datos sólo de su empresa.
- En app.js se define un sistema de delegación de eventos mediante la función on(elemento,'delegado',handler).
- El sistema de delegación de eventos se usa en html poniendo en los nodos atributos data-accion y data-accion2.
  + data-accion indica la rutina a ejecutar cuando se hace clic sobre el nodo.
  + data-accion indica la rutina a ejecutar cuando se hace doble-clic sobre el nodo.
- dialogs.js tiene una pila uiStack donde las pantallas se añaden según se van motrando.
- La pantalla actual es app.pant_act. Los data-accion y data-accion2 se refieren a rutinas app.acciones['nombre rutina']
  o a rutinas app.pant_act['nombre rutina']. 
- Las clases gestoras se instalan en app.nombreGestor.
- Los data-accion y data-accion2 si tienen un "." se refieren a app.nombreGestor['nombre_rutina'] donde "nombreGestor" es lo que va delante del punto.


## 3. Uso Correcto de html.js
- Propósito: html.js centraliza la creación de elementos HTML para mantener consistencia en toda la aplicación.
- Función base: uhtml.crearElemento(tipo, config, contenedor) es la función principal que todas las demás utilizan.
- Funciones auxiliares:
    + crearBoton(config, contenedor): Crea botones.
    + crearIcono(nombre, config, contenedor): Crea iconos de Material Design.
    + crearGroupLabel(config, contenedor): Crea etiquetas para campos de formulario.
    + crearInputText(config, contenedor): Crea inputs de texto.
    + crearInputNumber(config, contenedor): Crea inputs numéricos.
    + crearCheckbox(config, contenedor): Crea checkboxes.
    + crearSelect(config, opciones, contenedor): Crea selects con opciones.
    + crearGrupoCampo(config, contenedor): Crea grupos de campos de formulario.
- Estructura correcta de formularios:
    + Campos normales: Usar clase form-group, etiqueta primero (con dos puntos), luego input.
    + Checkboxes: Usar clase checkbox-group, input primero, luego etiqueta (sin dos puntos).
- Implementación eficiente: Las funciones auxiliares deben ser concisas (2-3 líneas máximo).
- Ejemplo de uso correcto:
```javascript
// Crear un campo de texto
uhtml.crearGrupoCampo({
    id: 'nombre',
    label: 'Nombre',
    type: 'text',
    attrs: {
        required: 'required',
        'data-param-name': 'nombre'
    }
}, contenedor);

// Crear un checkbox
uhtml.crearGrupoCampo({
    id: 'activo',
    label: 'Activo',
    type: 'checkbox',
    attrs: {
        checked: true
    }
}, contenedor);
```
## 4. Sistema de CSS en la Aplicación
- Temas múltiples: La aplicación soporta varios temas de estilo (claro, oscuro, etc.).
- Variables CSS: Los estilos utilizan variables CSS definidas al inicio de cada archivo CSS para facilitar la personalización y mantener consistencia.

```css
:root {
  --color-primary: #3f51b5;
  --color-secondary: #f50057;
  --color-background: #ffffff;
  --color-text: #333333;
  --spacing-unit: 8px;
  --border-radius: 4px;
}
```

- Clases específicas para componentes:
    .form-group: Para campos de formulario estándar
    .checkbox-group: Para campos de tipo checkbox
    .accordion-section: Para secciones de acordeón
    .accordion-header: Para encabezados de acordeón
    .accordion-content: Para contenido de acordeón

- Responsive design: Los estilos están diseñados para adaptarse a diferentes tamaños de pantalla.
- Nomenclatura de clases: Se sigue un patrón descriptivo para las clases CSS, evitando abreviaciones confusas.
- Especificidad controlada: Se evita el uso excesivo de selectores anidados para mantener una especificidad manejable.
- CSS por defecto: Existe un conjunto de estilos base que se aplican a todos los temas.
- Modificadores de tema: Cada tema extiende o sobrescribe las variables CSS base según sea necesario.
