/* Tema Tron - Estilo futurista inspirado en la película Tron */
/* Importar fuentes para el tema Tron */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700&family=Rajdhani:wght@300;400;500;600;700&display=swap');

/* Variables del tema */
:root.theme-tron {
    --primary-color: #00a2ff;
    --primary-color-rgb: 0, 162, 255;
    /* RGB para efectos de transparencia */
    --secondary-color: #0066cc;
    --accent-color: #00ffff;
    --tron-accent-bright: #00ffff;
    --background-color: #0a1a2a;
    --surface-color: #0c2038;
    --tron-surface: #0c2038;
    --text-color: #ffffff;
    --tron-text: #ffffff;
    --tron-surface-dark: #0a1a2a;
    --text-secondary-color: #0088cc;
    --tron-accent: #0088cc;
    /* Color más oscuro para mejor legibilidad */
    --gauge-value-color: #0099cc;
    /* Color más oscuro para valores de gauges */
    --border-color: #00a2ff;
    --success-color: #00ff9f;
    --warning-color: #ffcc00;
    --error-color: #ff3366;
    --error-color-rgb: 255, 51, 102;
    --shadow-color: rgba(0, 162, 255, 0.5);
    --glow-effect: 0 0 10px rgba(0, 162, 255, 0.7), 0 0 20px rgba(0, 162, 255, 0.4);
    --tron-accent-glow: 0 0 10px rgba(0, 162, 255, 0.7), 0 0 20px rgba(0, 162, 255, 0.4);
    --neon-effect: 0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.4);
    --grid-lines: rgba(0, 162, 255, 0.2);

    /* Fuentes */
    --font-family: 'Rajdhani', 'Orbitron', sans-serif;
    --font-weight-normal: 400;
    --font-weight-bold: 600;
}

/* Para seleccionar widgets*/
:root.theme-tron {
    --selection-overlay: rgba(0, 162, 255, 0.15);
    --selection-border: var(--accent-color);
    --selection-check: var(--tron-accent-bright);
    --selection-check-background: var(--primary-color);
}

.theme-tron .widget.selected::before {
    box-shadow: var(--tron-accent-glow);
}

.theme-tron .widget.selected::after {
    box-shadow: var(--neon-effect);
}

.theme-tron .widget .widget-checkbox-container input[type="checkbox"] {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 3px;
    background-color: rgba(0, 0, 0, 0.3);
    box-shadow: var(--neon-effect);
    cursor: pointer;
}

.theme-tron .widget .widget-checkbox-container input[type="checkbox"]:checked {
    background-color: var(--primary-color);
    border-color: var(--accent-color);
    box-shadow: var(--tron-accent-glow);
}

:root.theme-tron .widget-pasted {
    box-shadow: 0 0 20px var(--accent-color);
}

:root.theme-tron .widget-pasted {
    animation: widget-paste-flash-tron 1s ease-out;
}

@keyframes widget-paste-flash-tron {
    0% {
        box-shadow: 0 0 30px var(--accent-color), 0 0 10px var(--primary-color);
        transform: scale(0.95);
    }

    50% {
        box-shadow: 0 0 20px var(--accent-color), 0 0 15px var(--primary-color);
        transform: scale(1.02);
    }

    100% {
        box-shadow: var(--glow-effect);
        transform: scale(1);
    }
}




/* Estilos generales */
.theme-tron body {
    background-color: var(--background-color);
    color: var(--text-color);
    background-image:
        linear-gradient(0deg, var(--grid-lines) 1px, transparent 1px),
        linear-gradient(90deg, var(--grid-lines) 1px, transparent 1px);
    background-size: 20px 20px;
    font-family: var(--font-family);
}

/* Contenedores */
.theme-tron .container {
    background-color: rgba(10, 26, 42, 0.7);
    border: 1px solid var(--border-color);
    box-shadow: var(--glow-effect);
}

/* Login */
.theme-tron #login-container h1 {
    color: var(--accent-color);
    text-shadow: var(--neon-effect);
    letter-spacing: 2px;
    font-size: 3rem;
    margin-bottom: 2rem;
}

.theme-tron .login-form {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    box-shadow: var(--glow-effect);
    border-radius: 0;
    padding: 2rem;
}

.theme-tron .login-form h2 {
    color: var(--accent-color);
    text-transform: uppercase;
    letter-spacing: 1px;
    text-align: center;
    margin-bottom: 1.5rem;
}

.theme-tron .form-group label {
    color: var(--text-secondary-color);
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 1px;
}

.theme-tron .form-group input:not([type="checkbox"]),
.theme-tron .form-group select,
.theme-tron .form-group textarea {
    background-color: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 0.7rem;
    border-radius: 0;
    transition: all 0.3s ease;
}

.theme-tron .form-group input:focus,
.theme-tron .form-group select:focus,
.theme-tron .form-group textarea:focus {
    box-shadow: var(--neon-effect);
    outline: none;
}

.theme-tron button {
    background-color: var(--primary-color);
    color: var(--text-color);
    border: 1px solid var(--accent-color);
    border-radius: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 0.7rem 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.theme-tron button:hover {
    background-color: var(--secondary-color);
    box-shadow: var(--glow-effect);
}

.theme-tron button:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
            transparent,
            rgba(0, 255, 255, 0.2),
            transparent);
    transition: 0.5s;
}

.theme-tron button:hover:before {
    left: 100%;
}

.theme-tron button:disabled {
    background-color: rgba(0, 162, 255, 0.5);
    border-color: rgba(0, 255, 255, 0.3);
    box-shadow: none;
    cursor: not-allowed;
    opacity: 0.7;
}

.theme-tron button:disabled:before {
    display: none;
}

.theme-tron .error-message {
    color: var(--error-color);
    text-shadow: 0 0 5px rgba(255, 51, 102, 0.7);
}

/* Header */
.theme-tron header {
    border-bottom: 1px solid var(--border-color);
    /*padding: 1rem 0;*/
    padding: 0;
    padding-bottom: 5px;
    margin-bottom: 1.5rem;
}

.theme-tron header h1 {
    color: var(--accent-color);
    text-shadow: var(--neon-effect);
    text-transform: uppercase;
    letter-spacing: 2px;
}

.theme-tron .header-title-container {
    position: relative;
}

.theme-tron .dashboard-quick-actions {
    margin-top: 5px;
}

.theme-tron .icon-button {
    background-color: rgba(0, 0, 0, 0.3);
    color: var(--accent-color);
    border: 1px solid var(--border-color);
    box-shadow: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.theme-tron .icon-button:hover {
    background-color: rgba(0, 162, 255, 0.2);
    box-shadow: var(--glow-effect);
    transform: translateY(-2px);
}

.theme-tron .icon-button:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.theme-tron .icon-button:hover:before {
    left: 100%;
}

.theme-tron .dropdown-menu {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    box-shadow: var(--glow-effect);
    border-radius: 0;
}

.theme-tron .dropdown-menu button {
    background-color: transparent;
    color: var(--text-color);
    border: none;
    border-bottom: 1px solid rgba(0, 162, 255, 0.3);
    border-radius: 0;
    text-align: left;
}

.theme-tron .dropdown-menu button:hover {
    background-color: rgba(0, 162, 255, 0.2);
    box-shadow: none;
}

/* Dashboard */
.theme-tron .dashboard {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    box-shadow: var(--glow-effect);
    border-radius: 0;
    background-image: none;
    /* Sin cuadrícula por defecto */
}

/* Dashboard con cuadrícula */
.theme-tron .dashboard.show-grid {
    background-image:
        linear-gradient(0deg, var(--grid-lines) 1px, transparent 1px),
        linear-gradient(90deg, var(--grid-lines) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Widgets */
.theme-tron .widget {
    background-color: rgba(200, 210, 220, 0.15);
    border: 0;
    /* Sin borde por defecto */
    box-shadow: none;
    /* Sin sombra por defecto */
    border-radius: 0;
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
    position: relative;
    /* Para posicionar el resize handle */
}

/* Manejador de redimensionamiento */
.theme-tron .widget .resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 15px;
    height: 15px;
    cursor: nwse-resize;
    background: transparent;
    z-index: 10;
    display: none;
    /* Oculto por defecto */
}

.theme-tron .widget .resize-handle::before {
    content: '';
    position: absolute;
    bottom: 3px;
    right: 3px;
    width: 8px;
    height: 8px;
    border-right: 2px solid var(--border-color);
    border-bottom: 2px solid var(--border-color);
    opacity: 0.7;
}

.theme-tron .edit-mode .widget .resize-handle {
    display: block;
    /* Visible en modo edición */
}

/* Aplicar bordes y sombra solo cuando está activada la opción */
.theme-tron .show-widget-borders .widget {
    border: 1px solid var(--border-color);
    box-shadow: var(--glow-effect);
}

/* Efecto hover solo cuando los bordes están activados */
.theme-tron .show-widget-borders .widget:hover {
    box-shadow: var(--neon-effect);
}

.theme-tron .widget-header {
    border-bottom: 1px solid var(--border-color);
}

.theme-tron .widget-title {
    color: var(--accent-color);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.8rem;
}

.theme-tron .text-widget .widget-content {
    color: var(--text-secondary-color);
    font-weight: var(--font-weight-bold);
    text-shadow: 0 0 5px rgba(0, 136, 204, 0.3);
    /* Sombra más sutil */
}

.theme-tron .value-widget .widget-content {
    color: var(--gauge-value-color);
    text-shadow: 0 0 5px rgba(0, 153, 204, 0.7);
    font-size: 1.8rem;
    font-weight: var(--font-weight-bold);
}

/* Gauges */
.theme-tron .gauge-arc.gauge-background {
    stroke: rgba(0, 162, 255, 0.2);
}

.theme-tron .gauge-arc.gauge-foreground {
    stroke: var(--accent-color);
    filter: drop-shadow(0 0 3px var(--accent-color));
}

.theme-tron .gauge-value {
    color: var(--gauge-value-color);
    text-shadow: 0 0 5px rgba(0, 153, 204, 0.7);
    font-size: 1.2rem;
    font-weight: var(--font-weight-bold);
}

.theme-tron .percentage-title {
    color: var(--text-secondary-color);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
    font-weight: var(--font-weight-bold);
}

.theme-tron .percentage-value {
    color: var(--gauge-value-color);
    text-shadow: 0 0 5px rgba(0, 153, 204, 0.7);
    font-size: 1.8rem;
    font-weight: var(--font-weight-bold);
}

.theme-tron .percentage-gauge {
    background-color: rgba(0, 162, 255, 0.2);
    border-radius: 0;
    height: 20px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.5);
    width: 80%;
    /* Más ancho según preferencia del usuario */
}

.theme-tron .percentage-gauge-fill {
    background-color: var(--accent-color);
    background-image: linear-gradient(45deg,
            rgba(255, 255, 255, 0.15) 25%,
            transparent 25%,
            transparent 50%,
            rgba(255, 255, 255, 0.15) 50%,
            rgba(255, 255, 255, 0.15) 75%,
            transparent 75%,
            transparent);
    background-size: 20px 20px;
    border-radius: 0;
    box-shadow: 0 0 10px var(--accent-color);
    animation: tron-pulse 1.5s infinite alternate;
}

/* Modales */
.theme-tron .modal {
    backdrop-filter: blur(5px);
}

.theme-tron .modal-content {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    box-shadow: var(--glow-effect);
    border-radius: 0;
}

.theme-tron .close-btn {
    color: var(--accent-color);
}

/* Diálogo de confirmación de eliminación */
.theme-tron .delete-confirm-dialog .dialog-content {
    border: 2px solid var(--error-color);
    background-color: rgba(var(--error-color-rgb), 0.05);
    box-shadow: 0 0 15px rgba(var(--error-color-rgb), 0.3), 0 0 30px rgba(var(--error-color-rgb), 0.1);
}

.theme-tron .delete-confirm-dialog .dialog-header {
    background-color: rgba(var(--error-color-rgb), 0.1);
    border-bottom: 1px solid rgba(var(--error-color-rgb), 0.2);
}

.theme-tron .delete-confirm-dialog .dialog-header h3 {
    color: var(--error-color);
    text-shadow: 0 0 5px rgba(var(--error-color-rgb), 0.5);
}

/* Botón de eliminar en el diálogo de confirmación y en modales */
.theme-tron .delete-confirm-dialog .danger-btn,
.theme-tron .danger-btn {
    background-color: rgba(var(--error-color-rgb), 0.8);
    color: white;
    border: 1px solid var(--error-color);
    box-shadow: 0 0 10px rgba(var(--error-color-rgb), 0.5);
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

.theme-tron .delete-confirm-dialog .danger-btn:hover,
.theme-tron .danger-btn:hover {
    background-color: var(--error-color);
    box-shadow: 0 0 15px rgba(var(--error-color-rgb), 0.7), 0 0 30px rgba(var(--error-color-rgb), 0.4);
}

.theme-tron .delete-confirm-dialog .danger-btn:before,
.theme-tron .danger-btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.theme-tron .delete-confirm-dialog .danger-btn:hover:before,
.theme-tron .danger-btn:hover:before {
    left: 100%;
}

/* Popup de opciones */
.theme-tron .popup-menu {
    backdrop-filter: blur(5px);
}

.theme-tron .popup-content {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    box-shadow: var(--glow-effect);
    border-radius: 0;
}

.theme-tron .popup-content h3 {
    color: var(--accent-color);
    text-transform: uppercase;
    letter-spacing: 1px;
    border-bottom: 1px solid var(--border-color);
}

.theme-tron .popup-option {
    background-color: rgba(0, 0, 0, 0.3);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    /* Para asegurar que los efectos se apliquen correctamente */
    z-index: 1;
    /* Para evitar problemas de superposición */
    margin-bottom: 8px;
    /* Espacio entre opciones */
}

.theme-tron .popup-option:first-child {
    border-top: 1px solid var(--border-color);
    /* Asegurar que el primer elemento tenga borde superior */
}

.theme-tron .popup-option:hover {
    background-color: rgba(0, 162, 255, 0.2);
    box-shadow: var(--glow-effect);
    border-color: var(--accent-color);
    /* Borde más visible al hacer hover */
}

.theme-tron .popup-option.danger-option {
    color: var(--error-color);
    border-color: var(--error-color);
}

.theme-tron .popup-option.danger-option:hover {
    background-color: rgba(255, 51, 102, 0.2);
    box-shadow: 0 0 10px rgba(255, 51, 102, 0.7);
}

/* Estilos para el popup de datos de período en el tema Tron */
.theme-tron .period-data-popup {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    box-shadow: var(--glow-effect);
    border-radius: 0;
    backdrop-filter: blur(5px);
}

.theme-tron .period-data-popup .popup-header {
    background-color: rgba(0, 162, 255, 0.2);
    border-bottom: 1px solid var(--border-color);
}

.theme-tron .period-data-popup .popup-header h3 {
    color: var(--accent-color);
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 0 5px rgba(0, 162, 255, 0.5);
}

.theme-tron .period-data-popup .close-btn {
    color: var(--accent-color);
}

.theme-tron .period-data-popup .close-btn:hover {
    color: var(--error-color);
    text-shadow: 0 0 5px rgba(255, 51, 102, 0.7);
}

.theme-tron .period-data-popup p {
    color: var(--text-color);
}

.theme-tron .period-data-table {
    border-collapse: collapse;
}

.theme-tron .period-data-table th {
    background-color: rgba(0, 162, 255, 0.2);
    color: var(--accent-color);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
    border: 1px solid var(--border-color);
}

.theme-tron .period-data-table td {
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.theme-tron .period-points-table {
    border: 1px solid var(--border-color);
    box-shadow: var(--glow-effect);
}

.theme-tron .period-points-table th {
    background-color: rgba(0, 162, 255, 0.2);
    color: var(--accent-color);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.8rem;
    border: 1px solid var(--border-color);
}

.theme-tron .period-points-table td {
    color: var(--text-color);
    border: 1px solid var(--border-color);
    font-size: 0.9rem;
}

.theme-tron .popup-option.selected {
    background-color: rgba(0, 162, 255, 0.5);
    border: 1px solid var(--accent-color);
    /* Borde completo en lugar de solo izquierdo */
    border-left: 4px solid var(--accent-color);
    /* Mantener el borde izquierdo más grueso */
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.7);
    text-shadow: 0 0 5px var(--accent-color);
    font-weight: var(--font-weight-bold);
    color: var(--accent-color);
}

.theme-tron .popup-option.selected:hover {
    background-color: rgba(0, 162, 255, 0.6);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.9);
}

/* Modo edición */
.theme-tron .edit-mode .widget:hover {
    border: 2px dashed var(--accent-color);
    box-shadow: 0 0 15px var(--accent-color);
}

.theme-tron .delete-mode .widget:hover {
    border: 2px dashed var(--error-color);
    box-shadow: 0 0 15px var(--error-color);
}

/* Estilo para el widget durante el arrastre */
.theme-tron .widget.dragging {
    opacity: 0.8;
    z-index: 100;
}

/* Checkbox personalizado */
.theme-tron .checkbox-group {
    display: flex;
    align-items: center;
}

.theme-tron .checkbox-group input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    background-color: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    position: relative;
    margin-right: 10px;
    cursor: pointer;
}

.theme-tron .checkbox-group input[type="checkbox"]:checked {
    background-color: var(--primary-color);
}

.theme-tron .checkbox-group input[type="checkbox"]:checked::before {
    content: '✓';
    position: absolute;
    color: var(--text-color);
    font-size: 14px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.theme-tron .checkbox-group label {
    color: var(--text-secondary-color);
}

/* Animaciones */
@keyframes tron-pulse {
    from {
        box-shadow: 0 0 5px var(--accent-color);
    }

    to {
        box-shadow: 0 0 15px var(--accent-color);
    }
}

/* Scrollbar personalizada */
.theme-tron ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.theme-tron ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
}

.theme-tron ::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 0;
}

.theme-tron ::-webkit-scrollbar-thumb:hover {
    background: var(--accent-color);
}

/* Transparencia de widgets */
.theme-tron .transparent-widgets .widget {
    background-color: transparent;
    backdrop-filter: blur(0);
}