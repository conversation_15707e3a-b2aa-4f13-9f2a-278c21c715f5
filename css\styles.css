/* Estilos base - Independientes del tema */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Clase para ocultar elementos */
.hidden {
    display: none !important;
}

[data-accion] {
    cursor: pointer;
}

/** En los encabezados, dealnte del título, la flecha para ir atrás*/
.tit_ico_atras {
    padding: 0px 6px;
    transition: all 0.4s ease;
    cursor: pointer;
    background-color: transparent;
    color: var(--accent-color);
    position: relative;
    overflow: hidden;

    border: 1px dotted var(--border-color);
    border-radius: 8px;
}

.tit_ico_atras:hover {
    background-color: rgba(0, 255, 255, 0.1);
    box-shadow: var(--glow-effect);
    border-radius: 50%;
}

/* Estructura básica */
.container {
    max-width: calc(100vw - 4em);
    max-height: calc(100vh - 2em);
    margin: auto;
    padding: 20px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    overflow: hidden;
}

/* Excepción para el contenedor de entidades */
#entity-management-container {
    z-index: 1;
    /* Asegurar que esté por debajo de los botones de navegación */
    display: flex;
    flex-direction: column;
    height: calc(100vh - 2em);
}

/* Sistema de temas */
:root {
    /* Variables base que serán sobrescritas por cada tema */
    --primary-color: #4CAF50;
    --secondary-color: #45a049;
    --accent-color: #4CAF50;
    --background-color: #f5f5f5;
    --surface-color: #ffffff;
    --text-color: #333333;
    --text-secondary-color: #555555;
    --border-color: #dddddd;
    --success-color: #4CAF50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --font-family: Arial, sans-serif;
    --widget-text-color: #333333;
    --widget-bg-color: #ffffff;
}

/* Por defecto, aplicamos el tema default */
:root {
    font-family: var(--font-family);
}

/* Evitar selección de texto excepto en áreas permitidas */
body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
img,
button,
label,
div,
span,
a,
ul,
ol,
li,
header,
footer,
nav,
aside,
section,
article,
figure,
figcaption,
blockquote {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

input[type="text"],
input[type="password"],
textarea,
td {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* Estilos para el login */
#login-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 60vh;
}

.login-form {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 350px;
}

.login-form h2 {
    margin-bottom: 20px;
    text-align: center;
}

.form-group {
    margin-bottom: 12px;
    /* Reducido de 15px a 12px */
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input:not([type="checkbox"]),
.form-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.form-group input[readonly] {
    background-color: var(--background-color) !important;
    color: gray !important;
}

/* Estilos para la fila de cabecera del widget con acciones */
.widget-header-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    /* Reducido de 10px a 6px */
    flex-wrap: nowrap;
}

.widget-type-label {
    margin-bottom: 0 !important;
    white-space: nowrap;
    flex: 1;
}

.widget-actions-container {
    display: flex;
    gap: 8px;
    margin-left: 10px;
}

/* Estilos para la fila del selector de tipo de widget */
.widget-type-row {
    margin-bottom: 10px;
    /* Reducido de 15px a 10px */
    padding-left: 0;
}

.widget-type-select {
    width: 100%;
}

.icon-action-btn {
    width: 28px;
    height: 28px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    font-size: 14px;
}

.primary-icon-btn {
    background-color: #4CAF50;
    color: white;
}

.secondary-icon-btn {
    background-color: #2196F3;
    color: white;
}

.icon-action-btn.danger-icon-btn {
    color: var(--error-color);
    border-color: var(--error-color);
    background-color: var(----background-color);
}

.icon-action-btn.danger-icon-btn:hover {
    background-color: rgba(255, 51, 102, 0.2);
    box-shadow: 0 0 10px rgba(255, 51, 102, 0.7);
}

.action-icon {
    font-size: 16px;
}

.danger-icon-btn:hover {
    background-color: #f44336;
    color: white;
}

.checkbox-group {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 10px;
}

.checkbox-group label {
    font-weight: normal;
    margin-bottom: 0;
}

button {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

button:hover {
    background-color: #45a049;
}

.error-message {
    color: red;
    margin-top: 10px;
    text-align: center;
}

/* Estilos para el header */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #ddd;
    margin-bottom: 10px;
}

/* Ajuste específico para la cabecera de entidades */
#entity-management-container header {
    margin-bottom: 5px;
    padding-top: 5px;
    /* Reducir el padding superior */
}

#entity-management-container .header-title-container {
    margin-top: -5px;
    /* Subir el título */
}

.header-title-container {
    display: flex;
    flex-direction: column;
    margin-left: 20px;
}

header h1 {
    padding-left: 10px;
    cursor: default;
    margin-bottom: 5px;
}

.dashboard-quick-actions {
    display: flex;
    gap: 10px;
    padding-left: 10px;
    margin-top: 2px;
}

.icon-button {
    background: none;
    border: none;
    font-size: 16px;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-color: #f0f0f0;
    color: #333;
    transition: all 0.2s ease;
    padding: 0;
    z-index: 5;
    /* Asegurar que esté por encima de otros elementos */
}

.icon-button:hover {
    background-color: #e0e0e0;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.icon-button span {
    display: inline-block;
    line-height: 1;
}

.icon-switch {
    font-weight: bold;
}

.icon-new {
    font-size: 18px;
    font-weight: bold;
}

.icon-add-widget {
    font-size: 14px;
}

.menu-container {
    position: relative;
}

/* Estilos para el botón de menú móvil */
.mobile-menu-btn {
    display: none;
    /* Por defecto oculto, se mostrará en móviles */
    width: 40px;
    height: 30px;
    /* Altura reducida a 30px */
    background-color: #4CAF50;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    color: white;
    /* Color del icono */
    font-size: 24px;
    /* Tamaño del icono */
    justify-content: center;
    align-items: center;
    line-height: 1;
    /* Ajustar línea base para centrar verticalmente */
    padding: 0;
    /* Eliminar padding para mejor control */
    position: relative;
    /* Para posicionar el contenido */
}

/* Animación para el icono de hamburguesa */
.hamburger-icon {
    display: inline-block;
    transform: rotate(0deg);
    /* Posición inicial */
    transition: transform 0.3s ease;
    /* Transición suave para la rotación */
}

/* Clase para la animación de apertura del menú */
.hamburger-icon.menu-opening {
    transform: rotate(90deg);
    /* Rotación cuando el menú se está abriendo */
}

/* Clase para la animación de cierre del menú */
.hamburger-icon.menu-closing {
    transform: rotate(0deg);
    /* Volver a la posición original */
}

.dropdown-menu {
    position: absolute;
    right: 0;
    top: 40px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
    width: fit-content;
    /* Ancho automático en escritorio */
    min-width: 200px;
    /* Ancho mínimo para asegurar legibilidad */
    transform-origin: top right;
    transition: transform 0.3s ease, opacity 0.3s ease;
    opacity: 1;
    transform: translateX(0);
}

/* Animación para mostrar el menú */
.dropdown-menu.menu-entering {
    animation: slideIn 0.3s ease forwards;
}

/* Animación para ocultar el menú */
.dropdown-menu.menu-exiting {
    animation: slideOut 0.3s ease forwards;
}

/* Menú oculto */
.dropdown-menu.hidden {
    display: none;
}

/* Keyframes para la animación de entrada */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Keyframes para la animación de salida */
@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }

    to {
        opacity: 0;
        transform: translateX(20px);
    }
}

.dropdown-menu button {
    display: block;
    width: 100%;
    text-align: left;
    padding: 10px 15px;
    background-color: transparent;
    color: #333;
    border: none;
    border-bottom: 1px solid #eee;
}

.dropdown-menu button:last-child {
    border-bottom: none;
}

.dropdown-menu button:hover {
    background-color: #f5f5f5;
}

.dropdown-menu .danger-option {
    color: #f44336;
    font-weight: bold;
}

.dropdown-menu .danger-option:hover {
    background-color: #ffebee;
}

/* Estilos para el menú principal */
.main-menu-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 70vh;
    padding: 20px;
}

.main-menu {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
    width: 100%;
    max-width: 600px;
}

.menu-button {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 15px 20px;
    font-size: 18px;
    border-radius: 8px;
    background-color: var(--surface-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    cursor: pointer;
    /* Estilos adicionales para los divs */
    box-sizing: border-box;
    user-select: none;
    outline: none;
}

.menu-button .icon {
    margin-right: 15px;
    font-size: 24px;
}

.logout-btn {
    background-color: transparent;
    color: var(--text-color);
    border: none;
    padding: 8px 15px;
    cursor: pointer;
    font-size: 14px;
}

.back-btn {
    background-color: transparent;
    color: var(--text-color);
    border: none;
    padding: 8px 15px;
    cursor: pointer;
    font-size: 14px;
}

/* Estilos para la gestión de entidades */
.entity-container {
    padding: 20px;
    max-width: 100%;
}

.entity-count {
    font-size: 14px;
    color: var(--text-secondary-color);
    margin-left: 10px;
}

.entity-filter-container {
    display: flex;
    margin-bottom: 20px;
    position: relative;
}

.entity-filter {
    flex: 1;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 16px;
}

.clear-filter-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    font-size: 20px;
    color: var(--text-secondary-color);
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.entity-table-container {
    overflow-x: auto;
    width: 100%;
}

.entity-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.entity-table th,
.entity-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.entity-table th {
    background-color: var(--surface-color);
    font-weight: bold;
    position: relative;
    cursor: pointer;
}

.entity-table th:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.entity-table th.sortable::after {
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 5px;
}

.entity-table th.sort-asc::after {
    content: '▲';
    font-size: 10px;
}

.entity-table th.sort-desc::after {
    content: '▼';
    font-size: 10px;
}

.entity-table tr:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.entity-table .checkbox-cell {
    width: 40px;
    text-align: center;
    padding-left: 0;
    padding-right: 0;
}

.entity-table .action-cell {
    width: 40px;
    text-align: center;
    vertical-align: middle;
    padding-left: 0;
    padding-right: 0;
}

.entity-table .action-button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 18px;
    padding: 5px;
    color: var(--text-color);
}

.entity-action-menu {
    position: absolute;
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    box-shadow: 0 2px 10px var(--shadow-color);
    z-index: 1100;
    /* Aumentado para asegurar que esté por encima de todo, incluyendo modales */
    min-width: 150px;
}

.entity-action-menu button {
    display: block;
    width: 100%;
    text-align: left;
    padding: 10px 15px;
    background: none;
    border: none;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
    cursor: pointer;
}

.entity-action-menu button:last-child {
    border-bottom: none;
}

.entity-action-menu button:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.entity-action-menu .danger-option {
    color: var(--error-color);
}

/* Estilos para el dashboard */
.dashboard {
    width: 800px;
    height: 600px;
    background-color: white;
    border: 1px solid #ddd;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

/* Contenedor del dashboard */
.dashboard-container {
    position: relative;
    margin: 0 auto;
    max-width: 100%;
    overflow-x: auto;
    /* Permitir scroll horizontal */
    overflow-y: visible;
    -webkit-overflow-scrolling: touch;
    /* Scroll suave en iOS */
}

/* Estilos para la cuadrícula */
:root {
    --grid-color: #dddddd;
}

/* Cuadrícula para el tema por defecto */
.dashboard.show-grid {
    background-image: linear-gradient(var(--grid-color) 1px, transparent 1px),
        linear-gradient(90deg, var(--grid-color) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Ocultar cuadrícula cuando no está activada */
.dashboard:not(.show-grid) {
    background-image: none !important;
}

/* Estilos para los widgets */
.widget {
    position: absolute !important;
    /* Forzar posición absoluta */
    background-color: var(--widget-bg-color, #f9f9f9);
    color: var(--widget-text-color, #333333);
    border: 0;
    border-radius: 4px;
    padding: 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: background-color 0.3s ease, border 0.3s ease, color 0.3s ease;
}

.show-widget-borders .widget {
    border: 1px solid #ddd;
}

.transparent-widgets .widget:not(.force-border) {
    background-color: transparent;
    box-shadow: none;
}

/* Asegurar que los widgets con borde forzado siempre muestren su borde */
.widget.force-border {
    border-width: 1px !important;
    border-style: solid !important;
}

/* Asegurar que los widgets con borde forzado no sean afectados por la transparencia */
.transparent-widgets .widget.force-border {
    opacity: 1 !important;
}

.widget-content {
    height: 100%;
    /* Ocupa todo el alto */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Estilos específicos para cada tipo de widget */
.text-widget .widget-content {
    font-size: 16px;
    text-align: center;
    color: var(--widget-text-color, #333333);
}

.value-widget .widget-content {
    font-size: 24px;
    font-weight: bold;
    color: var(--widget-text-color, #333333);
}

.gauge-widget .widget-content,
.percentage-gauge-widget .widget-content {
    position: relative;
}

.gauge-container {
    width: 100%;
    height: 100%;
    position: relative;
}

/* Estilos para los modales */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1203;
    /* Aumentado para asegurar que esté por encima de todo */
    overflow: hidden;
    /* Evitar scroll cuando el modal se arrastra */
}

.modal-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    width: 500px;
    max-width: 90%;
    max-height: 95vh;
    /* Aumentado de 90vh a 95vh */
    overflow-y: auto;
    position: fixed;
    /* Cambiado a fixed para mejor comportamiento al arrastrar */
    cursor: default;
    z-index: 1001;
    /* Asegurar que esté por encima del fondo del modal */
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    /* Sombra más pronunciada para destacar */
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    /* Reducido de 15px a 10px */
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
    /* Reducido de 10px a 8px */
}

/* Estilo para el encabezado de los modales para que sea reconocible como "agarradera" */
.modal-content h2,
.modal-header h2 {
    cursor: move;
    user-select: none;
    padding-right: 10px;
    margin-bottom: 0;
    margin-top: 0;
    /* Asegurar que no haya espacio extra arriba */
}

/* Estilo para elementos durante el arrastre */
.dragging {
    opacity: 0.95;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    transition: none;
    /* Desactivar transiciones durante el arrastre */
}

.close-btn {
    float: right;
    font-size: 24px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    border-radius: 50%;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.close-btn:hover {
    background-color: #f44336;
    color: white;
}

.widget-fields {
    margin-top: 15px;
}

/* Estilos para el modo de edición */
.edit-mode .widget {
    cursor: move;
}

.edit-mode .widget:hover {
    border: 2px dashed #4CAF50;
}

/* Manejador de redimensionamiento */
.widget .resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 20px;
    height: 20px;
    cursor: nwse-resize;
    background: transparent;
    z-index: 10;
    display: none;
    /* Oculto por defecto */
    touch-action: none;
    /* Prevenir scroll en dispositivos táctiles */
}

.widget .resize-handle::before {
    content: '';
    position: absolute;
    bottom: 3px;
    right: 3px;
    width: 10px;
    height: 10px;
    border-right: 2px solid #aaa;
    border-bottom: 2px solid #aaa;
    opacity: 0.7;
}

.edit-mode .widget .resize-handle {
    display: block;
    /* Visible en modo edición */
}

/* Ajustes para dispositivos táctiles */
.touch-device .edit-mode .widget .resize-handle {
    width: 30px;
    height: 30px;
}

.touch-device .edit-mode .widget .resize-handle::before {
    width: 15px;
    height: 15px;
    border-width: 3px;
}

/* Estilos para el modo de edición de widget */
.edit-widget-mode .widget {
    cursor: pointer;
}

.edit-widget-mode .widget:hover {
    border: 2px dashed #2196F3;
}

/* Estilos para los selectores de color */
.color-picker-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.reset-color-btn {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.reset-color-btn:hover {
    background-color: #e0e0e0;
}

/* Estilos para los botones en el diálogo de edición */
.button-group {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    align-items: center;
    /* Asegurar alineación vertical */
}

/* Estilos para los botones del diálogo de configurar tablero */
/* Excepción para los botones del diálogo de configurar tablero - FORZAR UNA ÚNICA FILA */
.dashboard-config-buttons {
    flex-direction: row !important;
    justify-content: space-between !important;
    gap: 5px !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}

.dashboard-config-buttons button {
    flex: 1 !important;
    padding: 0 !important;
    min-width: 0 !important;
    width: 32% !important;
    max-width: 32% !important;
    border-radius: 4px !important;
    height: 36px !important;
    margin: 0 !important;
}

/* Mostrar solo iconos en móvil */
.dashboard-config-buttons .btn-text {
    display: none !important;
}

.dashboard-config-buttons .btn-icon {
    display: block !important;
    font-size: 20px !important;
    line-height: 1 !important;
}

/* Asegurar que los botones tengan el contenido centrado */
.dashboard-config-buttons button {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

#dashboard-config-modal .form-group {
    margin-bottom: 12px;
}

#dashboard-config-modal .form-group {
    margin-bottom: 12px;
}

#dashboard-config-modal .form-group label {
    display: inline-block;
    margin-bottom: 5px;
    width: 45%;
}

#dashboard-config-modal .form-group input,
#dashboard-config-modal .form-group select {
    width: 49%;
}


/* Estilos para los iconos y texto en los botones */
.btn-text {
    display: inline-block;
}

.btn-icon {
    display: none;
    /* Oculto por defecto, visible solo en móvil */
}

/* Estilo para el botón de cancelar */
.cancel-btn {
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    flex: 1;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cancel-btn:hover {
    background-color: #e0e0e0;
}

.primary-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    flex: 1;
    margin-right: 10px;
    height: 40px;
    /* Altura fija para todos los botones */
    display: flex;
    align-items: center;
    justify-content: center;
}

.primary-btn:disabled {
    background-color: #a5d6a7;
    cursor: not-allowed;
    opacity: 0.7;
}

.secondary-btn {
    background-color: #2196F3;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    flex: 1;
    margin-right: 10px;
    height: 40px;
    /* Altura fija para todos los botones */
    display: flex;
    align-items: center;
    justify-content: center;
}

.danger-btn {
    background-color: #f44336;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    flex: 1;
    height: 40px;
    /* Altura fija para todos los botones */
    display: flex;
    align-items: center;
    justify-content: center;
}

.primary-btn:hover {
    background-color: #45a049;
}

.secondary-btn:hover {
    background-color: #0b7dda;
}

.danger-btn:hover {
    background-color: #d32f2f;
}

/* Estilos para el popup de opciones de tablero */
.popup-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 200;
    /* Mayor que los modales normales */
}

.popup-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    width: 300px;
    max-width: 90%;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
}

.popup-header h3 {
    margin: 0;
    padding: 0;
    text-align: center;
    flex: 1;
}

.popup-content h3:not(.popup-header h3) {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    text-align: center;
}

.popup-option {
    display: block;
    width: 100%;
    text-align: left;
    padding: 12px 15px;
    margin-bottom: 8px;
    background-color: #f5f5f5;
    color: #333;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.popup-option:hover {
    background-color: #e0e0e0;
    /* transform: translateY(-2px);*/
}

.popup-option.danger-option {
    color: #f44336;
}

.popup-option.danger-option:hover {
    background-color: #ffebee;
}

.popup-option.selected {
    background-color: #bbdefb;
    border-left: 4px solid #2196F3;
    /*transform: translateY(-2px);*/
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(33, 150, 243, 0.3);
}

.popup-option.selected:hover {
    background-color: #90caf9;
    box-shadow: 0 3px 8px rgba(33, 150, 243, 0.4);
}

.delete-mode .widget:hover {
    border: 2px dashed red;
}

/* Estilos para el widget durante el arrastre */
.dragging {
    opacity: 0.8;
    cursor: move;
}

/* Estilos para los gauges */
.gauge {
    width: 100%;
    height: 100%;
    position: relative;
}

.gauge-value {
    position: absolute;
    bottom: 10px;
    width: 100%;
    text-align: center;
    font-weight: bold;
}

.gauge-arc {
    fill: none;
    stroke-width: 20;
}

.gauge-background {
    stroke: #eee;
}

.gauge-foreground {
    stroke: #4CAF50;
}

.percentage-gauge-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
}

.percentage-title {
    font-size: 16px;
    color: #555;
    margin-bottom: 5px;
}

.percentage-value {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 15px;
}

.percentage-gauge {
    width: 100%;
    height: 30px;
    background-color: #eee;
    border-radius: 15px;
    overflow: hidden;
    margin-top: 5px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.percentage-gauge-fill {
    height: 100%;
    background-color: #4CAF50;
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-size: 40px 40px;
    border-radius: 15px;
    transition: width 0.5s ease;
}

/* Estilos para el widget de últimos por períodos */
.latest-by-periods-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    text-align: center;
}

.latest-by-periods-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.latest-by-periods-table th,
.latest-by-periods-table td {
    padding: 5px;
    text-align: center;
    border-bottom: 1px solid #ddd;
}

.latest-by-periods-table th {
    background-color: rgba(0, 0, 0, 0.05);
    font-weight: bold;
    position: relative;
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;
}

.latest-by-periods-table th:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.latest-by-periods-table th::after {
    content: "";
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    opacity: 0.5;
}

.latest-by-periods-table tr:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

/* Estilos para la lista de tableros */
.dashboard-list {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 20px;
}

.dashboard-item {
    display: block;
    width: 100%;
    text-align: left;
    padding: 12px 15px;
    margin-bottom: 8px;
    background-color: #f5f5f5;
    color: #333;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dashboard-item:hover {
    background-color: #e0e0e0;
    transform: translateY(-2px);
}

.dashboard-item.current {
    background-color: #ffebee;
    color: #f44336;
    cursor: not-allowed;
}

.dashboard-item.current:hover {
    background-color: #ffebee;
    transform: none;
}

/* Estilos para el nombre de usuario en la lista de tableros */
.dashboard-name {
    display: block;
    font-weight: bold;
}

.dashboard-user {
    display: block;
    font-size: 0.85em;
    color: #666;
    margin-top: 2px;
}

/* Estilos para el botón de crear nuevo tablero */
.btn-primary {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.btn-primary:hover {
    background-color: #0069d9;
}

.btn-primary:active {
    background-color: #0062cc;
}

/* Media queries para dispositivos móviles */
@media (max-width: 768px) {
    .header-title-container {
        margin-left: 10px;
    }

    header h1 {
        font-size: 1.5rem;
        margin-bottom: 2px;
    }

    .dashboard-quick-actions {
        margin-top: 0;
    }

    .icon-button {
        width: 24px;
        height: 24px;
        font-size: 14px;
    }

    .icon-new {
        font-size: 16px;
    }

    .icon-add-widget {
        font-size: 12px;
    }
}


#dashboard-options-popup {
    z-index: 1200;
}

/* Estilos para el popup de datos de período */
.period-data-popup {
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.period-data-popup .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--background-color);
}

.period-data-popup .popup-header h3 {
    margin: 0;
    font-size: 16px;
    color: var(--text-color);
}

.period-data-popup .close-btn {
    cursor: pointer;
    font-size: 18px;
    color: var(--text-secondary-color);
}

.period-data-popup .close-btn:hover {
    color: var(--error-color);
}

.period-data-popup .popup-content {
    padding: 15px;
}

.period-data-popup p {
    margin: 0 0 10px 0;
    color: var(--text-color);
}

.period-data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.period-data-table th,
.period-data-table td {
    padding: 8px;
    text-align: center;
    border: 1px solid var(--border-color);
}

.period-data-table th {
    background-color: var(--background-color);
    color: var(--text-color);
    font-weight: bold;
}

.period-data-table td {
    color: var(--text-color);
}

/* Estilos para la tabla de puntos de datos */
.period-points-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 5px;
    font-size: 0.9em;
}

.period-points-table th,
.period-points-table td {
    padding: 6px;
    text-align: center;
    border: 1px solid var(--border-color);
}

.period-points-table th {
    background-color: var(--background-color);
    color: var(--text-color);
    font-weight: bold;
}

.period-points-table td {
    color: var(--text-color);
}

/* Estilos para las pestañas del popup */
.popup-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 15px;
}

.popup-tab {
    padding: 8px 15px;
    background-color: transparent;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    font-weight: normal;
    color: var(--text-secondary-color);
    transition: all 0.2s ease;
}

.popup-tab.active {
    border-bottom: 2px solid var(--accent-color);
    font-weight: bold;
    color: var(--text-color);
}

.popup-tab:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.period-selector {
    margin-bottom: 10px;
}

.period-selector select {
    padding: 5px;
    border-radius: 3px;
    border: 1px solid var(--border-color);
    background-color: var(--surface-color);
    color: var(--text-color);
}

.no-data-message {
    font-style: italic;
    color: var(--text-secondary-color);
    text-align: center;
    padding: 20px;
}

.icono-boton {
    max-width: min-content;
}

/* Estilo específico para grupos de formulario con inputs de tipo color */
.form-group.color-field-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
}

/* Estilo para la etiqueta del campo de color - ocupa el 50% del espacio */
.form-group.color-field-container label {
    flex: 0 0 50%;
    margin: 0;
    padding-right: 10px;
}

/* Contenedor para el color picker y el botón reset - ocupa el otro 50% */
.form-group.color-field-container .color-controls {
    display: flex;
    flex: 0 0 50%;
    align-items: center;
}

/* Estilo para el input de tipo color (selector de color) - se adapta al espacio disponible */
.form-group.color-field-container .color-controls input[type="color"] {
    flex: 1;
    min-width: 30px;
    height: 30px;
    padding: 0;
    border: 1px solid #ccc;
    cursor: pointer;
    background: none;
    vertical-align: middle;
    -webkit-appearance: none;
    appearance: none;
    margin-right: 10px;
}

/* Estilo para que se muestre correctamente en distintos navegadores */
.form-group.color-field-container .color-controls input[type="color"]::-webkit-color-swatch-wrapper {
    padding: 0;
}

.form-group.color-field-container .color-controls input[type="color"]::-webkit-color-swatch {
    border: none;
}

.form-group.color-field-container .color-controls input[type="color"]::-moz-color-swatch {
    border: none;
}

/* Estilo para el botón de reset */
.form-group.color-field-container .color-controls .reset-button {
    padding: 4px 8px;
    font-size: 12px;
    height: auto;
    cursor: pointer;
    white-space: nowrap;
}

/* Ajustes para temas específicos */
.theme-neumorphic .form-group.color-field-container .color-controls input[type="color"] {
    border-radius: 10px;
    box-shadow: var(--neumorphic-shadow);
}

.theme-tron .form-group.color-field-container .color-controls input[type="color"] {
    border: 1px solid var(--tron-border-color);
    box-shadow: 0 0 5px var(--tron-glow-color);
}