/**
 * Módulo de navegación para iPRA
 * Implementa la navegación entre pantallas utilizando delegación de eventos
 */
function setupNavigation() {
    // Manejar el botón de menú principal en el dashboard
    on(document, 'click', '#main-menu-btn,#entity-back-menu-btn', function () {
        // Navegar al menú principal
        navigateTo('main-menu');
    });

    // Añadir la función "back" a app.acciones para volver a la pantalla anterior
    app.acciones.back = function () {
        console.log('Acción back: Volviendo a la pantalla anterior');

        // Verificar si hay historia en el navegador
        if (window.history.length > 1) {
            // Si hay historia, usar el botón atrás del navegador
            // Esto disparará handlePopState que se encargará de la navegación
            window.history.back();
        } else {
            // Si no hay historia (por ejemplo, se recargó la aplicación), ir al menú principal
            console.log('Acción back: No hay historia, navegando al menú principal');
            navigateTo('main-menu');
        }
    };

    // Ir al menú principal
    app.acciones.inicio = function () {
        navigateTo('main-menu');
    }
}