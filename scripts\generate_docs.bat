@echo off
REM Script para generar documentación usando WeasyPrint (Windows)

setlocal enabledelayedexpansion

REM Colores para Windows
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "NC=[0m"

REM Función para logging
:log
echo %GREEN%[%date% %time%] %~1%NC%
goto :eof

:warn
echo %YELLOW%[%date% %time%] WARNING: %~1%NC%
goto :eof

:error
echo %RED%[%date% %time%] ERROR: %~1%NC%
exit /b 1

REM Cargar configuración
set "CONFIG_FILE=%~dp0config.conf"
if not exist "%CONFIG_FILE%" (
    call :error "Archivo config.conf no encontrado. Ejecuta scripts\configure.bat primero."
    exit /b 1
)

REM Leer configuración
for /f "tokens=1,2 delims==" %%a in ('type "%CONFIG_FILE%" ^| findstr /v "^#" ^| findstr "="') do (
    set "%%a=%%b"
)

REM Configurar entorno regional
if not "%LC_NUMERIC%"=="" (
    set "LC_NUMERIC=%LC_NUMERIC%"
    call :log "Configuración regional: LC_NUMERIC=%LC_NUMERIC%"
)

call :log "=== GENERADOR DE DOCUMENTACIÓN IPRA_I ==="

REM Verificar WeasyPrint
if not exist "venv" (
    call :error "Entorno virtual Python no encontrado. Ejecuta scripts\install.bat primero."
    exit /b 1
)

call venv\Scripts\activate.bat
python -c "import weasyprint" >nul 2>&1
if errorlevel 1 (
    call venv\Scripts\deactivate.bat
    call :error "WeasyPrint no está instalado. Ejecuta scripts\install.bat primero."
    exit /b 1
)
call venv\Scripts\deactivate.bat

call :log "WeasyPrint verificado correctamente"

REM Crear directorios de documentación
call :create_docs_dir
call :create_docs_css

REM Procesar argumentos
if "%~1"=="-h" goto :show_help
if "%~1"=="--help" goto :show_help
if "%~1"=="-u" (
    call :generate_user_manual
    goto :eof
)
if "%~1"=="--user" (
    call :generate_user_manual
    goto :eof
)
if "%~1"=="-t" (
    call :generate_technical_docs
    goto :eof
)
if "%~1"=="--technical" (
    call :generate_technical_docs
    goto :eof
)
if "%~1"=="-c" (
    call :generate_config_docs
    goto :eof
)
if "%~1"=="--config" (
    call :generate_config_docs
    goto :eof
)

REM Por defecto, generar toda la documentación
call :generate_user_manual
call :generate_technical_docs
call :generate_config_docs

call :log "=== DOCUMENTACIÓN GENERADA ==="
call :log "Directorio: %DOCS_DIR%"
call :log "Archivos HTML: %DOCS_DIR%\html\"
call :log "Archivos PDF: %DOCS_DIR%\pdf\"

goto :eof

:create_docs_dir
call :log "Creando directorios de documentación..."

if not exist "%DOCS_DIR%" mkdir "%DOCS_DIR%"
if not exist "%DOCS_DIR%\html" mkdir "%DOCS_DIR%\html"
if not exist "%DOCS_DIR%\pdf" mkdir "%DOCS_DIR%\pdf"
if not exist "%DOCS_DIR%\assets" mkdir "%DOCS_DIR%\assets"

call :log "Directorios de documentación creados"
goto :eof

:create_docs_css
set "CSS_FILE=%DOCS_DIR%\assets\docs.css"

(
echo /* Estilos para documentación IPRA_I */
echo.
echo * {
echo     margin: 0;
echo     padding: 0;
echo     box-sizing: border-box;
echo }
echo.
echo body {
echo     font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
echo     line-height: 1.6;
echo     color: #333;
echo     background-color: #f5f5f5;
echo }
echo.
echo .container {
echo     max-width: 800px;
echo     margin: 0 auto;
echo     padding: 20px;
echo     background-color: white;
echo     box-shadow: 0 0 10px rgba(0,0,0,0.1);
echo }
echo.
echo h1 {
echo     color: #2c3e50;
echo     border-bottom: 3px solid #3498db;
echo     padding-bottom: 10px;
echo     margin-bottom: 30px;
echo }
echo.
echo h2 {
echo     color: #34495e;
echo     margin-top: 30px;
echo     margin-bottom: 15px;
echo     border-left: 4px solid #3498db;
echo     padding-left: 15px;
echo }
echo.
echo h3 {
echo     color: #7f8c8d;
echo     margin-top: 20px;
echo     margin-bottom: 10px;
echo }
echo.
echo p {
echo     margin-bottom: 15px;
echo     text-align: justify;
echo }
echo.
echo ul, ol {
echo     margin-left: 30px;
echo     margin-bottom: 15px;
echo }
echo.
echo li {
echo     margin-bottom: 5px;
echo }
echo.
echo code {
echo     background-color: #ecf0f1;
echo     padding: 2px 6px;
echo     border-radius: 3px;
echo     font-family: 'Courier New', monospace;
echo     font-size: 0.9em;
echo }
) > "%CSS_FILE%"

call :log "CSS de documentación creado: %CSS_FILE%"
goto :eof

:generate_user_manual
call :log "Generando manual de usuario..."

set "INPUT_FILE=doc\manual_usuario.md"
set "HTML_FILE=%DOCS_DIR%\html\manual_usuario.html"
set "PDF_FILE=%DOCS_DIR%\pdf\manual_usuario.pdf"

if not exist "%INPUT_FILE%" (
    call :warn "Manual de usuario no encontrado: %INPUT_FILE%"
    goto :eof
)

REM Crear HTML básico
call :create_basic_html "%INPUT_FILE%" "%HTML_FILE%" "Manual de Usuario IPRA_I"

REM Generar PDF con WeasyPrint
call :log "Generando PDF con WeasyPrint..."
call venv\Scripts\activate.bat

python -c "
import weasyprint
import os

# Configurar base URL para recursos relativos
base_url = 'file:///' + os.path.abspath('%DOCS_DIR%/html/').replace('\\', '/')

# Generar PDF
weasyprint.HTML(filename='%HTML_FILE%', base_url=base_url).write_pdf('%PDF_FILE%')
print('PDF generado: %PDF_FILE%')
"

call venv\Scripts\deactivate.bat

call :log "Manual de usuario generado:"
call :log "  HTML: %HTML_FILE%"
call :log "  PDF: %PDF_FILE%"

goto :eof

:generate_technical_docs
call :log "Generando documentación técnica..."

set "HTML_FILE=%DOCS_DIR%\html\documentacion_tecnica.html"
set "PDF_FILE=%DOCS_DIR%\pdf\documentacion_tecnica.pdf"

REM Crear documentación técnica
call :create_technical_html "%HTML_FILE%"

REM Generar PDF
call :log "Generando PDF técnico con WeasyPrint..."
call venv\Scripts\activate.bat

python -c "
import weasyprint
import os

base_url = 'file:///' + os.path.abspath('%DOCS_DIR%/html/').replace('\\', '/')
weasyprint.HTML(filename='%HTML_FILE%', base_url=base_url).write_pdf('%PDF_FILE%')
print('PDF técnico generado: %PDF_FILE%')
"

call venv\Scripts\deactivate.bat

call :log "Documentación técnica generada:"
call :log "  HTML: %HTML_FILE%"
call :log "  PDF: %PDF_FILE%"

goto :eof

:generate_config_docs
call :log "Generando documentación de configuración..."

set "HTML_FILE=%DOCS_DIR%\html\configuracion.html"
set "PDF_FILE=%DOCS_DIR%\pdf\configuracion.pdf"

(
echo ^<!DOCTYPE html^>
echo ^<html lang="es"^>
echo ^<head^>
echo     ^<meta charset="UTF-8"^>
echo     ^<meta name="viewport" content="width=device-width, initial-scale=1.0"^>
echo     ^<title^>Configuración IPRA_I^</title^>
echo     ^<link rel="stylesheet" href="../assets/docs.css"^>
echo ^</head^>
echo ^<body^>
echo     ^<div class="container"^>
echo         ^<h1^>Configuración IPRA_I^</h1^>
echo         
echo         ^<h2^>Configuración Actual^</h2^>
echo         ^<p^>Generado el: %date% %time%^</p^>
echo         
echo         ^<h3^>Servidor^</h3^>
echo         ^<ul^>
echo             ^<li^>^<strong^>Host:^</strong^> %SERVER_HOST%^</li^>
echo             ^<li^>^<strong^>Puerto:^</strong^> %SERVER_PORT%^</li^>
echo             ^<li^>^<strong^>URL:^</strong^> http://%SERVER_HOST%:%SERVER_PORT%^</li^>
echo         ^</ul^>
echo         
echo         ^<h3^>Regional^</h3^>
echo         ^<ul^>
echo             ^<li^>^<strong^>LC_NUMERIC:^</strong^> %LC_NUMERIC%^</li^>
echo         ^</ul^>
echo         
echo         ^<h3^>Directorios^</h3^>
echo         ^<ul^>
echo             ^<li^>^<strong^>Build:^</strong^> %BUILD_DIR%^</li^>
echo             ^<li^>^<strong^>Documentación:^</strong^> %DOCS_DIR%^</li^>
echo             ^<li^>^<strong^>Logs:^</strong^> %LOG_DIR%^</li^>
echo         ^</ul^>
echo         
echo         ^<h2^>Comandos Disponibles^</h2^>
echo         ^<ul^>
echo             ^<li^>^<code^>scripts\configure.bat^</code^> - Configurar sistema^</li^>
echo             ^<li^>^<code^>scripts\install.bat^</code^> - Instalar dependencias^</li^>
echo             ^<li^>^<code^>scripts\start.bat^</code^> - Iniciar servidor^</li^>
echo             ^<li^>^<code^>scripts\stop.bat^</code^> - Detener servidor^</li^>
echo             ^<li^>^<code^>scripts\status.bat^</code^> - Ver estado^</li^>
echo             ^<li^>^<code^>scripts\logs.bat^</code^> - Ver logs^</li^>
echo             ^<li^>^<code^>scripts\generate_docs.bat^</code^> - Generar documentación^</li^>
echo         ^</ul^>
echo     ^</div^>
echo ^</body^>
echo ^</html^>
) > "%HTML_FILE%"

REM Generar PDF
call venv\Scripts\activate.bat
python -c "
import weasyprint
import os
base_url = 'file:///' + os.path.abspath('%DOCS_DIR%/html/').replace('\\', '/')
weasyprint.HTML(filename='%HTML_FILE%', base_url=base_url).write_pdf('%PDF_FILE%')
print('PDF de configuración generado: %PDF_FILE%')
"
call venv\Scripts\deactivate.bat

call :log "Documentación de configuración generada:"
call :log "  HTML: %HTML_FILE%"
call :log "  PDF: %PDF_FILE%"

goto :eof

:create_basic_html
set "MD_FILE=%~1"
set "HTML_FILE=%~2"
set "TITLE=%~3"

(
echo ^<!DOCTYPE html^>
echo ^<html lang="es"^>
echo ^<head^>
echo     ^<meta charset="UTF-8"^>
echo     ^<meta name="viewport" content="width=device-width, initial-scale=1.0"^>
echo     ^<title^>%TITLE%^</title^>
echo     ^<link rel="stylesheet" href="../assets/docs.css"^>
echo ^</head^>
echo ^<body^>
echo     ^<div class="container"^>
echo         ^<h1^>%TITLE%^</h1^>
echo         ^<div class="content"^>
) > "%HTML_FILE%"

REM Conversión básica de Markdown a HTML (muy simple)
for /f "usebackq delims=" %%i in ("%MD_FILE%") do (
    set "LINE=%%i"
    if "!LINE:~0,2!"=="# " (
        set "LINE=!LINE:~2!"
        echo             ^<h1^>!LINE!^</h1^> >> "%HTML_FILE%"
    ) else if "!LINE:~0,3!"=="## " (
        set "LINE=!LINE:~3!"
        echo             ^<h2^>!LINE!^</h2^> >> "%HTML_FILE%"
    ) else if "!LINE:~0,4!"=="### " (
        set "LINE=!LINE:~4!"
        echo             ^<h3^>!LINE!^</h3^> >> "%HTML_FILE%"
    ) else if "!LINE:~0,2!"=="* " (
        set "LINE=!LINE:~2!"
        echo             ^<li^>!LINE!^</li^> >> "%HTML_FILE%"
    ) else if "!LINE!"=="" (
        echo             ^<p^>^</p^> >> "%HTML_FILE%"
    ) else (
        echo             ^<p^>!LINE!^</p^> >> "%HTML_FILE%"
    )
)

(
echo         ^</div^>
echo     ^</div^>
echo ^</body^>
echo ^</html^>
) >> "%HTML_FILE%"

goto :eof

:create_technical_html
set "HTML_FILE=%~1"

(
echo ^<!DOCTYPE html^>
echo ^<html lang="es"^>
echo ^<head^>
echo     ^<meta charset="UTF-8"^>
echo     ^<meta name="viewport" content="width=device-width, initial-scale=1.0"^>
echo     ^<title^>Documentación Técnica IPRA_I^</title^>
echo     ^<link rel="stylesheet" href="../assets/docs.css"^>
echo ^</head^>
echo ^<body^>
echo     ^<div class="container"^>
echo         ^<h1^>Documentación Técnica IPRA_I^</h1^>
echo         
echo         ^<h2^>Arquitectura del Sistema^</h2^>
echo         ^<p^>IPRA_I es una aplicación web para dashboards colaborativos que consta de:^</p^>
echo         ^<ul^>
echo             ^<li^>^<strong^>Frontend:^</strong^> Aplicación web SPA con JavaScript vanilla^</li^>
echo             ^<li^>^<strong^>Backend:^</strong^> Servidor Node.js con WebSockets para colaboración en tiempo real^</li^>
echo             ^<li^>^<strong^>Almacenamiento:^</strong^> IndexedDB en el navegador^</li^>
echo             ^<li^>^<strong^>Build:^</strong^> Sistema de minificación y combinación de archivos^</li^>
echo         ^</ul^>
echo         
echo         ^<h2^>Instalación^</h2^>
echo         ^<p^>El proceso de instalación se realiza mediante scripts automatizados:^</p^>
echo         ^<ol^>
echo             ^<li^>^<code^>scripts\configure.bat^</code^> - Configurar puerto y opciones^</li^>
echo             ^<li^>^<code^>scripts\install.bat^</code^> - Instalar dependencias y configurar sistema^</li^>
echo             ^<li^>^<code^>scripts\start.bat^</code^> - Iniciar el servidor^</li^>
echo         ^</ol^>
echo         
echo         ^<h2^>WebSockets y Colaboración^</h2^>
echo         ^<p^>El sistema de colaboración permite que múltiples usuarios editen el mismo dashboard simultáneamente:^</p^>
echo         ^<ul^>
echo             ^<li^>Reconexión automática con backoff exponencial^</li^>
echo             ^<li^>Sincronización de cambios en tiempo real^</li^>
echo             ^<li^>Indicadores visuales de usuarios conectados^</li^>
echo             ^<li^>Cola de mensajes durante desconexión^</li^>
echo         ^</ul^>
echo         
echo         ^<footer^>
echo             ^<p^>Generado el %date% %time% con configuración regional: %LC_NUMERIC%^</p^>
echo         ^</footer^>
echo     ^</div^>
echo ^</body^>
echo ^</html^>
) > "%HTML_FILE%"

goto :eof

:show_help
echo Uso: %~nx0 [OPCIONES]
echo.
echo Opciones:
echo   -h, --help       Mostrar esta ayuda
echo   -a, --all        Generar toda la documentación (por defecto)
echo   -u, --user       Solo manual de usuario
echo   -t, --technical  Solo documentación técnica
echo   -c, --config     Solo documentación de configuración
echo.
echo Ejemplos:
echo   %~nx0               # Generar toda la documentación
echo   %~nx0 -u            # Solo manual de usuario
echo.
goto :eof

endlocal
