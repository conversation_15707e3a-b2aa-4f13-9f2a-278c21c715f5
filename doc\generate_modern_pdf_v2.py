#!/usr/bin/env python3
"""
Script para generar un PDF moderno a partir del archivo HTML usando WeasyPrint
"""

import os
import sys
import subprocess
from bs4 import BeautifulSoup
import re

def check_weasyprint():
    """Verifica si WeasyPrint está instalado"""
    try:
        subprocess.run(['weasyprint', '--version'], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def extract_content_from_html(html_file):
    """Extrae el contenido del archivo HTML original"""
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
        soup = BeautifulSoup(content, 'html.parser')

    # Extraer el título
    title = soup.title.string if soup.title else "iPRA - Prototipo"

    # Extraer solo el contenido del body, eliminando los estilos y el índice original
    body = soup.body

    # Eliminar el índice original para evitar duplicación
    if body:
        index_section = body.find('h2', id='índice') or body.find('h2', string=re.compile('Índice', re.IGNORECASE))
        if index_section:
            # Encontrar la sección completa del índice (h2 + ol)
            next_sibling = index_section.find_next_sibling()
            if next_sibling and next_sibling.name == 'ol':
                next_sibling.decompose()  # Eliminar la lista del índice
            index_section.decompose()  # Eliminar el encabezado del índice

    # Extraer el contenido del body sin los estilos
    body_content = body.decode_contents() if body else ""

    return title, body_content, content

def create_modern_html(template_file, html_file, output_file):
    """Crea un nuevo archivo HTML con la plantilla moderna y el contenido original"""
    title, body_content, original_content = extract_content_from_html(html_file)

    with open(template_file, 'r', encoding='utf-8') as f:
        template = f.read()

    # Crear la portada solo con el texto "iPRA"
    cover_html = f"""
    <div class="cover">
        <h1>iPRA</h1>
    </div>
    """

    # Crear la página de índice
    toc_html = """
    <div class="toc-page">
        <h1 class="toc-title">Índice</h1>
        <div class="toc">
    """

    # Extraer el índice del contenido original
    soup = BeautifulSoup(original_content, 'html.parser')

    # Primero, asignar IDs a todos los encabezados para asegurar que los enlaces funcionen
    for header in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
        if not header.get('id'):
            header_text = header.get_text().strip().lower()
            header_id = re.sub(r'[^a-z0-9]+', '-', header_text)
            header['id'] = header_id

    # Buscar la sección de índice
    index_section = soup.find('h2', id='índice') or soup.find('h2', string=re.compile('Índice', re.IGNORECASE))

    if index_section and index_section.find_next('ol'):
        # Obtener el índice y asegurarse de que los enlaces apunten a IDs existentes
        index_ol = index_section.find_next('ol')

        # Corregir los enlaces en el índice
        for a in index_ol.find_all('a'):
            href = a.get('href', '')
            if href.startswith('#'):
                target_id = href[1:]  # Eliminar el # inicial
                # Buscar el encabezado correspondiente
                target = soup.find(id=target_id)
                if not target:
                    # Si no existe, crear un ID basado en el texto del enlace
                    new_id = re.sub(r'[^a-z0-9]+', '-', a.get_text().strip().lower())
                    a['href'] = f'#{new_id}'

        toc_html += str(index_ol)
    else:
        # Buscar todos los encabezados h2 para crear un índice automático
        h2_headers = soup.find_all('h2')
        toc_items = []

        for h2 in h2_headers:
            h2_id = h2.get('id', '')
            h2_text = h2.get_text().strip()
            if h2_id and h2_text and not h2_text.lower().startswith('índice'):
                toc_items.append(f'<li><a href="#{h2_id}">{h2_text}</a></li>')

        if toc_items:
            toc_html += "<ol>\n            " + "\n            ".join(toc_items) + "\n        </ol>"
        else:
            # Índice de respaldo si no se encuentran encabezados h2
            toc_html += """
        <ol>
            <li><a href="#introduccion">Introducción</a></li>
            <li><a href="#tipos-de-usuarios">Tipos de usuarios y permisos</a></li>
            <li><a href="#pantallas">Pantallas y funcionalidades</a></li>
            <li><a href="#navegacion">Navegación entre pantallas</a></li>
            <li><a href="#temas">Temas de la aplicación</a></li>
            <li><a href="#tableros">Tableros</a></li>
            <li><a href="#funcionalidades-genericas">Funcionalidades genéricas</a></li>
            <li><a href="#datos">Datos de la aplicación</a></li>
            <li><a href="#compatibilidad">Compatibilidad con dispositivos</a></li>
            <li><a href="#conclusiones">Conclusiones</a></li>
        </ol>
        """

    toc_html += """
        </div>
    </div>
    """

    # Preparar el contenido principal
    # Asegurarse de que todos los elementos tengan IDs correctos
    content_soup = BeautifulSoup(body_content, 'html.parser')

    # Crear un diccionario de mapeo para IDs específicos que sabemos que están causando problemas
    id_mapping = {
        'editor-tableros': 'editor-de-tableros',
        'visor-tableros': 'visor-de-tableros',
        'pantalla-login': 'pantalla-de-inicio-de-sesión',
        'menu-principal': 'menú-principal',
        'pantalla-tableros': 'pantalla-de-tableros',
        'gestion-empresas': 'gestión-de-empresas',
        'gestion-usuarios': 'gestión-de-usuarios'
    }

    # Asignar IDs a los encabezados si no los tienen
    for i, header in enumerate(content_soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])):
        if not header.get('id'):
            # Crear un ID basado en el texto del encabezado
            header_text = header.get_text().strip().lower()
            header_id = re.sub(r'[^a-z0-9]+', '-', header_text)
            header['id'] = header_id
        # Si el encabezado tiene un ID que está en nuestro mapeo, añadir un ID adicional
        elif header.get('id') in id_mapping:
            # Añadir un ID alternativo como atributo de datos
            header['data-alt-id'] = id_mapping[header.get('id')]
            # También crear un span con el ID alternativo dentro del encabezado
            alt_id_span = content_soup.new_tag('span')
            alt_id_span['id'] = id_mapping[header.get('id')]
            header.append(alt_id_span)

    # Añadir spans con IDs específicos para los enlaces problemáticos
    # Crear un div contenedor para los IDs problemáticos
    id_container = content_soup.new_tag('div')
    id_container['style'] = 'display:none;'

    for problem_id in id_mapping.keys():
        span = content_soup.new_tag('span')
        span['id'] = problem_id
        id_container.append(span)

    # Añadir el contenedor al final del contenido
    content_soup.append(id_container)

    content_html = f"""
    <div class="content-wrapper">
        {str(content_soup)}
    </div>
    """

    # Reemplazar la sección del body en la plantilla
    modern_html = template.replace('<!-- Esta sección será reemplazada con el contenido real -->',
                                  cover_html + toc_html + content_html)

    # Guardar el nuevo archivo HTML
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(modern_html)

    return output_file

def main():
    """Función principal"""
    # Verificar si estamos en el directorio correcto
    if not os.path.exists('manual_usuario.html'):
        print("Error: No se encuentra el archivo manual_usuario.html")
        print("Asegúrate de ejecutar este script desde el directorio 'doc'")
        sys.exit(1)

    # Verificar que la imagen de portada existe
    if not os.path.exists('dashboard1.jpg'):
        print("Error: No se encuentra la imagen de portada (dashboard1.jpg)")
        print("Asegúrate de que la imagen esté en el directorio 'doc'")
        sys.exit(1)

    # Verificar que WeasyPrint está instalado
    if not check_weasyprint():
        print("Error: WeasyPrint no está instalado o no se puede ejecutar")
        print("Por favor, instala WeasyPrint con: pip install weasyprint")
        sys.exit(1)

    # Crear el archivo HTML moderno
    template_file = 'modern_template_v2.html'
    html_file = 'manual_usuario.html'
    modern_html = 'manual_usuario_modern.html'

    print("Creando HTML con plantilla moderna...")
    create_modern_html(template_file, html_file, modern_html)

    # Generar el PDF con WeasyPrint
    print("Generando PDF con WeasyPrint...")
    output_pdf = 'manual_usuario.pdf'
    subprocess.run(['weasyprint', modern_html, output_pdf], check=True)

    print(f"PDF generado correctamente: {output_pdf}")

    # Limpiar archivos temporales
    os.remove(modern_html)
    print("Proceso completado con éxito.")

if __name__ == "__main__":
    main()
