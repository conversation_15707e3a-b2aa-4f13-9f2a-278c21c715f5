/**
 * Animación del borde para el menú principal
 * Crea un borde que se mueve entre las opciones del menú principal
 * según la posición del ratón
 */


// Función para inicializar el borde del menú
function initMenuBorder() {
    const mainMenu = document.getElementById('main-menu');
    if (!mainMenu) return;

    // Eliminar borde anterior si existe
    const oldBorder = document.querySelector('.menu-border');
    if (oldBorder) {
        oldBorder.remove();
    }

    // Crear el elemento del borde
    const borderElement = document.createElement('div');
    borderElement.className = 'menu-border';

    // Añadir el borde al menú
    mainMenu.appendChild(borderElement);

    // Obtener todos los elementos del menú
    const menuItems = mainMenu.querySelectorAll('.menu-button');
    if (!menuItems.length) return;

    // Posicionar inicialmente el borde sobre el primer elemento
    const firstItem = menuItems[0];
    positionBorderOnItem(borderElement, firstItem);

    // Configurar eventos para cada elemento del menú
    menuItems.forEach(item => {
        item.addEventListener('mouseenter', () => {
            positionBorderOnItem(borderElement, item);
        });
    });
}

// Función para posicionar el borde sobre un elemento del menú
function positionBorderOnItem(border, item) {
    if (!border || !item) return;

    // Obtener la posición del elemento relativa al contenedor
    const itemRect = item.getBoundingClientRect();
    const menuRect = item.parentElement.getBoundingClientRect();

    // Calcular la posición relativa
    const top = itemRect.top - menuRect.top;

    // Posicionar el borde
    border.style.top = `${top}px`;
    border.style.height = `${itemRect.height}px`;
}

// Exponer la función para reiniciar la animación
window.resetMenuBorder = initMenuBorder;