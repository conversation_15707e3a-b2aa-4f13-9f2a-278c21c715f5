@echo off
REM Script para verificar el estado del servidor IPRA_I (Windows) - MOVIDO A AUX

setlocal enabledelayedexpansion

REM Cargar configuración
set "CONFIG_FILE=%~dp0..\config.conf"
if not exist "%CONFIG_FILE%" (
    echo ERROR: Archivo config.conf no encontrado. Edita scripts/config.conf primero.
    pause
    exit /b 1
)

REM Leer configuración
for /f "tokens=1,2 delims==" %%a in ('type "%CONFIG_FILE%" ^| findstr /v "^#" ^| findstr "="') do (
    set "%%a=%%b"
)

echo === ESTADO IPRA_I ===
echo Puerto: %SERVER_PORT%
echo Host: %SERVER_HOST%

REM Verificar si está ejecutándose
netstat -an | findstr ":%SERVER_PORT% " >nul 2>&1
if not errorlevel 1 (
    echo Estado: EJECUTÁNDOSE
) else (
    echo Estado: DETENIDO
)

echo URL: http://%SERVER_HOST%:%SERVER_PORT%
echo.
pause

endlocal
