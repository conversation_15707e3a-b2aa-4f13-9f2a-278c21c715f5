@echo off
REM Script para detener el servidor IPRA_I (Windows)

setlocal enabledelayedexpansion

REM Colores para Windows
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "NC=[0m"

REM Función para logging
:log
echo %GREEN%[%date% %time%] %~1%NC%
goto :eof

:warn
echo %YELLOW%[%date% %time%] WARNING: %~1%NC%
goto :eof

:error
echo %RED%[%date% %time%] ERROR: %~1%NC%
exit /b 1

REM Cargar configuración
set "CONFIG_FILE=%~dp0config.conf"
if not exist "%CONFIG_FILE%" (
    call :error "Archivo config.conf no encontrado. Ejecuta scripts\configure.bat primero."
    exit /b 1
)

REM Leer configuración
for /f "tokens=1,2 delims==" %%a in ('type "%CONFIG_FILE%" ^| findstr /v "^#" ^| findstr "="') do (
    set "%%a=%%b"
)

call :log "=== DETENIENDO SERVIDOR IPRA_I ==="

REM Procesar argumentos
set "STOP_MODE=all"
if "%~1"=="-p" set "STOP_MODE=pid"
if "%~1"=="--pid" set "STOP_MODE=pid"
if "%~1"=="-P" set "STOP_MODE=port"
if "%~1"=="--port" set "STOP_MODE=port"
if "%~1"=="-f" set "STOP_MODE=force"
if "%~1"=="--force" set "STOP_MODE=force"
if "%~1"=="-h" goto :show_help
if "%~1"=="--help" goto :show_help

if "%STOP_MODE%"=="pid" (
    call :stop_by_pid
) else if "%STOP_MODE%"=="port" (
    call :stop_by_port
) else if "%STOP_MODE%"=="force" (
    call :force_stop
) else (
    call :stop_all
)

goto :eof

:stop_by_pid
call :log "Deteniendo por archivo PID..."

set "PID_FILE=%LOG_DIR%\ipra-i.pid"
if not exist "%PID_FILE%" (
    call :warn "Archivo PID no encontrado: %PID_FILE%"
    exit /b 1
)

set /p "SERVER_PID=" < "%PID_FILE%"
if "%SERVER_PID%"=="" (
    call :warn "PID vacío en archivo: %PID_FILE%"
    del "%PID_FILE%" 2>nul
    exit /b 1
)

call :log "Deteniendo proceso PID: %SERVER_PID%"

REM Verificar si el proceso existe
tasklist /FI "PID eq %SERVER_PID%" 2>nul | findstr "%SERVER_PID%" >nul
if errorlevel 1 (
    call :warn "Proceso PID %SERVER_PID% no existe"
    del "%PID_FILE%" 2>nul
    exit /b 1
)

REM Intentar detener el proceso
taskkill /PID %SERVER_PID% /T >nul 2>&1
if errorlevel 1 (
    call :warn "No se pudo detener el proceso gracefully, forzando..."
    taskkill /PID %SERVER_PID% /T /F >nul 2>&1
    if errorlevel 1 (
        call :error "No se pudo detener el proceso PID %SERVER_PID%"
        exit /b 1
    )
)

REM Verificar que se detuvo
timeout /t 2 /nobreak >nul
tasklist /FI "PID eq %SERVER_PID%" 2>nul | findstr "%SERVER_PID%" >nul
if errorlevel 1 (
    call :log "Proceso detenido correctamente"
    del "%PID_FILE%" 2>nul
) else (
    call :error "El proceso aún está ejecutándose"
    exit /b 1
)

goto :eof

:stop_by_port
call :log "Buscando procesos en puerto %SERVER_PORT%..."

REM Buscar procesos usando el puerto
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":%SERVER_PORT% "') do (
    set "PID=%%a"
    if not "!PID!"=="" (
        call :log "Encontrado proceso PID: !PID! en puerto %SERVER_PORT%"
        
        REM Verificar si es un proceso Node.js
        for /f "tokens=1" %%b in ('tasklist /FI "PID eq !PID!" /FO CSV ^| findstr "node.exe"') do (
            call :log "Deteniendo proceso Node.js PID: !PID!"
            taskkill /PID !PID! /T >nul 2>&1
            if errorlevel 1 (
                call :warn "Forzando detención del proceso !PID!"
                taskkill /PID !PID! /T /F >nul 2>&1
            )
        )
    )
)

call :log "Búsqueda por puerto completada"
goto :eof

:force_stop
call :log "Forzando detención del servidor..."

REM Buscar todos los procesos Node.js que puedan ser nuestro servidor
for /f "tokens=2" %%a in ('tasklist /FI "IMAGENAME eq node.exe" /FO CSV ^| findstr "node.exe"') do (
    set "PID=%%~a"
    if not "!PID!"=="" (
        REM Verificar si el proceso está usando nuestro puerto o es server.js
        netstat -ano | findstr "!PID!" | findstr ":%SERVER_PORT%" >nul 2>&1
        if not errorlevel 1 (
            call :log "Forzando detención de proceso Node.js PID: !PID!"
            taskkill /PID !PID! /T /F >nul 2>&1
        )
    )
)

REM Limpiar archivo PID
if exist "%LOG_DIR%\ipra-i.pid" (
    del "%LOG_DIR%\ipra-i.pid" 2>nul
    call :log "Archivo PID eliminado"
)

call :log "Detención forzada completada"
goto :eof

:stop_all
call :log "Intentando detener servidor por todos los métodos..."

set "STOPPED=false"

REM Intentar por PID
call :stop_by_pid >nul 2>&1
if not errorlevel 1 set "STOPPED=true"

REM Intentar por puerto
call :stop_by_port >nul 2>&1
if not errorlevel 1 set "STOPPED=true"

if "%STOPPED%"=="true" (
    call :log "Servidor detenido correctamente"
) else (
    call :warn "No se encontraron procesos del servidor ejecutándose"
)

REM Limpiar archivos temporales
call :cleanup

goto :eof

:cleanup
call :log "Limpiando archivos temporales..."

REM Limpiar archivo PID
if exist "%LOG_DIR%\ipra-i.pid" (
    del "%LOG_DIR%\ipra-i.pid" 2>nul
    call :log "Archivo PID eliminado"
)

REM Limpiar archivos de lock si existen
if exist "%LOG_DIR%" (
    del "%LOG_DIR%\*.lock" 2>nul
)

call :log "Limpieza completada"
goto :eof

:verify_stopped
call :log "Verificando que el servidor esté detenido..."

REM Verificar puerto
netstat -an | findstr ":%SERVER_PORT% " >nul 2>&1
if not errorlevel 1 (
    call :warn "El puerto %SERVER_PORT% todavía está en uso"
    exit /b 1
)

call :log "Servidor detenido completamente"
goto :eof

:show_help
echo Uso: %~nx0 [OPCIONES]
echo.
echo Opciones:
echo   -h, --help       Mostrar esta ayuda
echo   -p, --pid        Detener solo por archivo PID
echo   -P, --port       Detener solo por puerto
echo   -a, --all        Detener por todos los métodos (por defecto)
echo   -f, --force      Forzar detención (SIGKILL)
echo.
echo Ejemplos:
echo   %~nx0               # Detener por todos los métodos
echo   %~nx0 -p            # Detener solo por PID
echo   %~nx0 -f            # Forzar detención
echo.
goto :eof

endlocal
