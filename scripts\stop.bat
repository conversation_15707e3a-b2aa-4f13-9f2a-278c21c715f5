@echo off
REM Script para detener el servidor IPRA_I (Windows)

setlocal enabledelayedexpansion

echo === DETENIENDO SERVIDOR IPRA_I ===

REM Cargar configuración
set "CONFIG_FILE=%~dp0config.conf"
if not exist "%CONFIG_FILE%" (
    echo ERROR: Archivo scripts/config.conf no encontrado.
    pause
    exit /b 1
)

REM Leer configuración
for /f "tokens=1,2 delims==" %%a in ('type "%CONFIG_FILE%" ^| findstr /v "^#" ^| findstr "="') do (
    set "%%a=%%b"
)

echo Puerto: %SERVER_PORT%
echo.

REM Buscar procesos Node.js en el puerto
echo Buscando procesos en puerto %SERVER_PORT%...

set "FOUND=false"
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":%SERVER_PORT% "') do (
    set "PID=%%a"
    if not "!PID!"=="" (
        echo Encontrado proceso PID: !PID!

        REM Verificar si es Node.js
        tasklist /FI "PID eq !PID!" /FO CSV | findstr "node.exe" >nul
        if not errorlevel 1 (
            echo Deteniendo proceso Node.js PID: !PID!
            taskkill /PID !PID! /T /F >nul 2>&1
            set "FOUND=true"
        )
    )
)

if "%FOUND%"=="false" (
    echo No se encontraron procesos Node.js en puerto %SERVER_PORT%
) else (
    echo Servidor detenido
)

REM Limpiar archivos temporales
if exist "%LOG_DIR%\ipra-i.pid" del "%LOG_DIR%\ipra-i.pid" 2>nul
if exist "%LOG_DIR%\*.lock" del "%LOG_DIR%\*.lock" 2>nul

echo.
pause

endlocal
