/**
 * Gestor del menú principal
 * Muestra diferentes opciones según el tipo de usuario
 */
class MainMenuManager {
    constructor() {
        this.menuOptions = [];
        app.mainMenuManager = this;
    }

    /**
     * Inicializa el gestor del menú principal
     */
    init() {
        // Configurar eventos
        this.setupEvents();
        // Actualizar el encabezado con el login del usuario
        this.updateHeaderWithUserLogin();
    }

    isLoaded() {
        return this.menuOptions.length > 0;
    }

    reset() {
        // Limpiar las opciones de menú
        this.menuOptions = [];

        // Limpiar también el contenedor del menú para evitar duplicación
        const menuContainer = document.getElementById('main-menu');
        if (menuContainer) {
            menuContainer.innerHTML = '';
            console.log("Contenedor del menú principal limpiado en reset()");
        }
    }

    /**
     * Configura los eventos del menú principal
     */
    setupEvents() {
        //por si la aplicación quiere poner algo aquí
    }

    /**
     * Carga el menú principal según el usuario actual
     */
    loadMenu() {
        if (this.isLoaded()) return;
        // Primero, resetear el estado del menú para evitar duplicación
        this.reset();

        console.log("Iniciando carga del menú principal");
        const menuContainer = document.getElementById('main-menu');
        if (!menuContainer) {
            console.error("No se encontró el contenedor del menú principal");
            return;
        }

        // Obtener opciones de menú según el usuario
        this.getMenuOptions()
            .then(options => {
                console.log(`Opciones de menú obtenidas: ${options.length} opciones`);
                this.menuOptions = options;

                // Crear opciones del menú como divs en lugar de botones
                options.forEach((option, index) => {
                    console.log(`Creando opción para: ${option.id} - ${option.label}`);
                    const menuItem = document.createElement('div');
                    menuItem.className = 'menu-button'; // Mantenemos la misma clase para no romper estilos
                    menuItem.dataset.option = option.id;
                    menuItem.tabIndex = 0; // Hacerlo focusable para navegación por teclado

                    // Añadir icono si está definido
                    if (option.icon) {
                        const icon = document.createElement('span');
                        icon.className = 'icon';
                        icon.innerHTML = option.icon;
                        menuItem.appendChild(icon);
                    }

                    // Añadir texto
                    const text = document.createElement('span');
                    text.textContent = option.label;
                    menuItem.appendChild(text);

                    // Añadir evento de clic
                    menuItem.addEventListener('click', () => {
                        console.log(`Opción seleccionada: ${option.id}`);
                        this.handleMenuOption(option);
                    });

                    // Añadir evento de teclado para Enter y Espacio
                    menuItem.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            this.handleMenuOption(option);
                        }
                    });

                    // Añadir al contenedor
                    menuContainer.appendChild(menuItem);

                    // Dar foco al primer elemento
                    if (index === 0) {
                        requestAnimationFrame(() => {
                            if (window.resetMenuBorder) {
                                window.resetMenuBorder();
                            }
                            menuItem.focus();
                        });
                    }
                });

                // Configurar navegación con teclado
                this.setupKeyboardNavigation();

                // Actualizar el encabezado con el login del usuario
                this.updateHeaderWithUserLogin();

                console.log("Menú principal cargado correctamente");
            })
            .catch(error => {
                console.error('Error al cargar el menú principal:', error);
                showNotification('Error al cargar el menú principal', 'error');
            });
    }

    /**
     * Obtiene las opciones de menú según el usuario actual
     * @returns {Promise<Array>} - Promesa que se resuelve con las opciones de menú
     */
    getMenuOptions() {
        return new Promise((resolve) => {
            const user = authManager.user;
            const companyId = authManager.companyId;
            const options = [];

            // Opción de tablero siempre disponible
            options.push({
                id: 'dashboard',
                label: 'Tablero',
                icon: '&#9776;', // Icono plano de tablero
                action: () => navigateTo('dashboard')
            });

            // Opciones para todos los administradores (de cualquier empresa)
            if (user && user.tipo === 'admin') {
                // Todos los administradores pueden ver el botón de Empresas/Empresa
                options.push({
                    id: 'companies',
                    // Si el usuario es de la empresa 1, mostrar "Empresas" (plural), si no, mostrar "Empresa" (singular)
                    label: companyId === 1 ? 'Empresas' : 'Empresa',
                    icon: '&#9635;', // Icono plano de empresa
                    action: () => navigateTo('entity-management', {
                        entityType: 'companies'
                    })
                });

                options.push({
                    id: 'personalizar',
                    label: 'Personalizar',
                    icon: '&#9673;', // Icono plano de usuario
                    action: () => userProfileManager.showUserProfileForm()
                });
            }

            // Añadir más opciones según sea necesario...

            resolve(options);
        });
    }

    /**
     * Maneja la selección de una opción del menú
     * @param {Object} option - Opción seleccionada
     */
    handleMenuOption(option) {
        if (option && option.action) {
            // Ejecutar la acción
            option.action();
        }
    }

    /**
     * Configura la navegación por teclado en el menú
     */
    setupKeyboardNavigation() {
        const menuItems = document.querySelectorAll('.menu-button');
        if (!menuItems.length) return;

        menuItems.forEach((item, index) => {
            item.addEventListener('keydown', (e) => {
                let nextIndex;

                switch (e.key) {
                    case 'ArrowDown':
                        e.preventDefault();
                        nextIndex = (index + 1) % menuItems.length;
                        menuItems[nextIndex].focus();
                        break;
                    case 'ArrowUp':
                        e.preventDefault();
                        nextIndex = (index - 1 + menuItems.length) % menuItems.length;
                        menuItems[nextIndex].focus();
                        break;
                        // No necesitamos manejar Enter y Espacio aquí, ya lo hacemos en cada elemento
                }
            });
        });
    }

    /**
     * Actualiza el encabezado del menú principal con el login del usuario actual
     */
    updateHeaderWithUserLogin() {
        // Obtener el usuario actual
        const user = authManager.user;
        if (!user) return;

        // Obtener el elemento h1 del encabezado del menú principal
        const headerTitle = document.querySelector('#main-menu-container header h1');
        if (headerTitle) {
            // Actualizar el texto con el login del usuario
            headerTitle.textContent = user.login;
            console.log(`Encabezado actualizado con el login del usuario: ${user.login}`);
        }
    }

}

// Crear instancia del gestor de menú principal y hacerla global
//window.mainMenuManager = new MainMenuManager();