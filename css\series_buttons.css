/* Estilos para los botones de series */

/* Contenedor para el selector de series y el botón de añadir */
.series-selector-container {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 10px;
}

/* Selector de series */
.series-selector-container select {
    flex: 1;
    margin-right: 10px;
}

/* Botón de añadir serie */
.series-selector-container .add-series-btn {
    flex-shrink: 0;
}

/* Estilos para la lista de series */
.series-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

/* Cada elemento de la lista de series */
.series-list li {
    display: flex;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

.series-list li:last-child {
    border-bottom: none;
}

/* Color de la serie */
.series-color {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 8px;
}

/* Nombre de la serie */
.series-name {
    flex: 1;
    font-size: 14px;
}

/* Botón de eliminar serie */
.series-list li .remove-series-item-btn {
    width: 24px;
    height: 24px;
    padding: 0;
    margin-left: 10px;
    border-radius: 0;
    position: relative;
    z-index: 5;
    cursor: pointer;
    border: none;
    color: white;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    line-height: 1;
    font-family: Arial, sans-serif;
    background-color: var(--error-color, #F44336);
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.series-list li .remove-series-item-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}
