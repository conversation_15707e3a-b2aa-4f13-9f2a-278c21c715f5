<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Database</title>
</head>
<body>
    <h1>Debug Database</h1>
    <button onclick="debugDatabase()">Debug Completo</button>
    <div id="output"></div>
    
    <script src="js/database_innerdb.js"></script>
    <script>
        const output = document.getElementById('output');
        
        function log(message) {
            console.log(message);
            output.innerHTML += '<p>' + message + '</p>';
        }
        
        async function debugDatabase() {
            try {
                log('=== INICIANDO DEBUG COMPLETO ===');
                
                log('1. Inicializando base de datos...');
                await dbManager.init();
                log('✅ Base de datos inicializada');
                
                log('2. Verificando empresas existentes...');
                const existingCompanies = await dbManager.getAllCompanies();
                log(`Empresas existentes: ${existingCompanies.length}`);
                
                log('3. Inicializando datos de prueba...');
                await dbManager.initializetestdata();
                log('✅ Datos de prueba procesados');
                
                log('4. Verificando empresas después de inicialización...');
                const companies = await dbManager.getAllCompanies();
                log(`Total empresas: ${companies.length}`);
                
                if (companies.length > 0) {
                    log('Primeras 5 empresas:');
                    for (let i = 0; i < Math.min(5, companies.length); i++) {
                        log(`  ${i+1}. ID: ${companies[i].id}, Nombre: ${companies[i].nombre}, Activo: ${companies[i].activo}`);
                    }
                }
                
                log('5. Verificando usuarios...');
                const users = await dbManager.getAllUsers();
                log(`Total usuarios: ${users.length}`);
                
                if (users.length > 0) {
                    log('Todos los usuarios:');
                    for (let i = 0; i < users.length; i++) {
                        const user = users[i];
                        log(`  ${i+1}. ID: ${user.id}, Login: "${user.login}", Clave: "${user.clave}", Empresa: ${user.empresaId}, Tipo: ${user.tipo}`);
                    }
                }
                
                log('6. Probando búsqueda específica de admin...');
                const adminUser = await dbManager.getUserByLogin('admin');
                if (adminUser) {
                    log(`✅ Usuario admin encontrado: ${JSON.stringify(adminUser, null, 2)}`);
                } else {
                    log('❌ Usuario admin NO encontrado');
                }
                
                log('7. Verificando índices de la base de datos...');
                const transaction = dbManager.db.transaction(['users'], 'readonly');
                const store = transaction.objectStore('users');
                const indexNames = Array.from(store.indexNames);
                log(`Índices en users: ${indexNames.join(', ')}`);
                
                log('8. Probando búsqueda manual por índice...');
                const index = store.index('login');
                const request = index.get('admin');
                request.onsuccess = () => {
                    if (request.result) {
                        log(`✅ Búsqueda manual exitosa: ${JSON.stringify(request.result, null, 2)}`);
                    } else {
                        log('❌ Búsqueda manual falló');
                    }
                };
                
                log('=== DEBUG COMPLETADO ===');
                
            } catch (error) {
                log('❌ Error: ' + error.message);
                console.error('Error completo:', error);
            }
        }
        
        // Auto-ejecutar al cargar
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(debugDatabase, 1000);
        });
    </script>
</body>
</html>
