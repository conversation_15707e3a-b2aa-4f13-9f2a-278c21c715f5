@echo off
REM Script de instalación desatendido para IPRA_I (Windows)
REM Ejecutar como Administrador

setlocal enabledelayedexpansion

echo === INSTALACIÓN IPRA_I ===

REM Cargar configuración
set "CONFIG_FILE=%~dp0config.conf"
for /f "tokens=1,2 delims==" %%a in ('type "%CONFIG_FILE%" 2^>nul ^| findstr /v "^#" ^| findstr "="') do set "%%a=%%b"

echo Puerto: %SERVER_PORT%

REM Verificar Node.js
node --version >nul 2>&1
if not errorlevel 1 (
    echo Node.js ya instalado
    goto :deps
)

REM Node.js no está instalado - necesita admin
net session >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js no está instalado
    echo Ejecuta como Administrador para instalarlo automáticamente
    echo O instala Node.js manualmente desde: https://nodejs.org/
    exit /b 1
)

REM Directorio de instalación
set "INSTALL_DIR=%~dp0install\windows"
mkdir "%INSTALL_DIR%" 2>nul

REM Descargar Node.js
set "NODE_FILE=%INSTALL_DIR%\node-v20.10.0-x64.msi"
if not exist "%NODE_FILE%" (
    echo Descargando Node.js v20.10.0...
    powershell -Command "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi' -OutFile '%NODE_FILE%'"
    if errorlevel 1 (
        echo ERROR: No se pudo descargar Node.js
        echo Verifica conexión a Internet
        exit /b 1
    )
    echo Descarga completada
)

REM Instalar Node.js y esperar a que termine
echo Instalando Node.js (esto puede tardar 2-3 minutos)...
msiexec /i "%NODE_FILE%" /quiet /norestart /l*v "%INSTALL_DIR%\install.log"
if errorlevel 1 (
    echo ERROR: msiexec falló
    echo Ver log: %INSTALL_DIR%\install.log
    exit /b 1
)

echo Instalación de Node.js completada, actualizando PATH...

REM Actualizar PATH del sistema y de la sesión
setx PATH "%PATH%;C:\Program Files\nodejs" /M >nul 2>&1
set "PATH=%PATH%;C:\Program Files\nodejs"

REM Verificar instalación con ruta completa
echo Verificando instalación...
"C:\Program Files\nodejs\node.exe" --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js no se instaló correctamente
    echo Reinicia el sistema e intenta de nuevo
    exit /b 1
)

for /f %%i in ('"C:\Program Files\nodejs\node.exe" --version') do echo Node.js instalado: %%i

:deps
REM Crear directorios
mkdir build 2>nul
mkdir logs 2>nul
mkdir docs_generated 2>nul
mkdir backend\logs 2>nul

REM Instalar dependencias frontend
echo Instalando dependencias frontend...
"C:\Program Files\nodejs\npm.cmd" ci 2>nul
if errorlevel 1 (
    echo npm ci falló, usando npm install...
    "C:\Program Files\nodejs\npm.cmd" install
    if errorlevel 1 (
        echo ERROR: No se pudieron instalar dependencias frontend
        exit /b 1
    )
)

REM Instalar dependencias backend
echo Instalando dependencias backend...
cd backend
"C:\Program Files\nodejs\npm.cmd" ci 2>nul
if errorlevel 1 (
    echo npm ci falló, usando npm install...
    "C:\Program Files\nodejs\npm.cmd" install
    if errorlevel 1 (
        echo ERROR: No se pudieron instalar dependencias backend
        exit /b 1
    )
)
cd ..

REM Firewall
echo Configurando firewall...
netsh advfirewall firewall delete rule name="IPRA_I" >nul 2>&1
netsh advfirewall firewall add rule name="IPRA_I" dir=in action=allow protocol=TCP localport=%SERVER_PORT% >nul 2>&1
if errorlevel 1 (
    echo AVISO: Error configurando firewall
) else (
    echo Firewall configurado para puerto %SERVER_PORT%
)

REM Build
echo Realizando build...
"C:\Program Files\nodejs\npm.cmd" run build
if errorlevel 1 (
    echo ERROR: Build falló
    echo Verifica que existe build.js en el directorio raíz
    exit /b 1
)

echo.
echo INSTALACIÓN COMPLETADA
echo URL: http://localhost:%SERVER_PORT%
echo.
echo Verificando archivos generados:
if exist build\aplicacion.js (echo - aplicacion.js: OK) else (echo - aplicacion.js: FALTA)
if exist build\aplicacion.css (echo - aplicacion.css: OK) else (echo - aplicacion.css: FALTA)

endlocal
