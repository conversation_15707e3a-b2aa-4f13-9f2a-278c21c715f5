@echo off
REM Script de instalación REAL para IPRA_I (Windows)
REM Descarga e instala automáticamente Node.js y todo lo necesario

setlocal enabledelayedexpansion

echo === INSTALACIÓN AUTOMÁTICA IPRA_I ===

REM Cargar configuración
set "CONFIG_FILE=%~dp0config.conf"
if not exist "%CONFIG_FILE%" (
    echo ERROR: Archivo scripts/config.conf no encontrado.
    echo Edita scripts/config.conf con tu configuración antes de ejecutar install.bat
    exit /b 1
)

REM Leer configuración
for /f "tokens=1,2 delims==" %%a in ('type "%CONFIG_FILE%" ^| findstr /v "^#" ^| findstr "="') do (
    set "%%a=%%b"
)

echo Puerto configurado: %SERVER_PORT%
echo.

REM Crear directorio de instalación
set "INSTALL_DIR=%~dp0install\windows"
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM URLs de descarga
set "NODE_URL=https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi"
set "NODE_FILE=%INSTALL_DIR%\node-v20.10.0-x64.msi"

REM Verificar si Node.js ya está instalado
node --version >nul 2>&1
if not errorlevel 1 (
    for /f %%i in ('node --version') do echo Node.js ya instalado: %%i
    goto :install_deps
)

REM Descargar Node.js si no existe
if not exist "%NODE_FILE%" (
    echo Descargando Node.js...
    powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri '%NODE_URL%' -OutFile '%NODE_FILE%'}"
    if errorlevel 1 (
        echo ERROR: No se pudo descargar Node.js
        exit /b 1
    )
    echo Node.js descargado correctamente
)

REM Instalar Node.js silenciosamente
echo Instalando Node.js...
msiexec /i "%NODE_FILE%" /quiet /norestart
if errorlevel 1 (
    echo ERROR: No se pudo instalar Node.js
    exit /b 1
)

echo Node.js instalado correctamente

REM Actualizar PATH para esta sesión
set "PATH=%PATH%;%ProgramFiles%\nodejs"

REM Verificar instalación
timeout /t 5 /nobreak >nul
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js no se instaló correctamente
    echo Reinicia el sistema y ejecuta este script nuevamente
    exit /b 1
)

for /f %%i in ('node --version') do echo Node.js verificado: %%i

:install_deps

REM Crear directorios necesarios
echo Creando directorios...
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"
if not exist "%DOCS_DIR%" mkdir "%DOCS_DIR%"
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"
if not exist "backend\logs" mkdir "backend\logs"

REM Instalar dependencias del frontend
echo Instalando dependencias del frontend...
npm install
if errorlevel 1 (
    echo ERROR: Error al instalar dependencias del frontend
    exit /b 1
)

REM Instalar dependencias del backend
echo Instalando dependencias del backend...
cd backend
npm install
if errorlevel 1 (
    echo ERROR: Error al instalar dependencias del backend
    exit /b 1
)
cd ..

REM Configurar firewall de Windows
echo Configurando firewall...
netsh advfirewall firewall delete rule name="IPRA_I Server" >nul 2>&1
netsh advfirewall firewall add rule name="IPRA_I Server" dir=in action=allow protocol=TCP localport=%SERVER_PORT% >nul 2>&1
if not errorlevel 1 (
    echo Firewall configurado para puerto %SERVER_PORT%
) else (
    echo AVISO: No se pudo configurar el firewall automáticamente
)

REM Build inicial
echo Realizando build inicial...
npm run build >nul 2>&1

echo.
echo === INSTALACIÓN COMPLETADA ===
echo Puerto: %SERVER_PORT%
echo URL: http://localhost:%SERVER_PORT%
echo.
echo Para iniciar: scripts\start.bat

endlocal
