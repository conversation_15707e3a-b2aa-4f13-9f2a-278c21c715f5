@echo off
REM Script de instalación para IPRA_I (Windows)
REM Instala Node.js, dependen<PERSON><PERSON>, y configura el sistema

setlocal enabledelayedexpansion

REM Colores para Windows
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "NC=[0m"

REM Función para logging
:log
echo %GREEN%[%date% %time%] %~1%NC%
goto :eof

:warn
echo %YELLOW%[%date% %time%] WARNING: %~1%NC%
goto :eof

:error
echo %RED%[%date% %time%] ERROR: %~1%NC%
exit /b 1

REM Cargar configuración
set "CONFIG_FILE=%~dp0config.conf"
if not exist "%CONFIG_FILE%" (
    call :error "Archivo config.conf no encontrado. Ejecuta scripts\configure.bat primero."
    exit /b 1
)

REM Leer configuración
for /f "tokens=1,2 delims==" %%a in ('type "%CONFIG_FILE%" ^| findstr /v "^#" ^| findstr "="') do (
    set "%%a=%%b"
)

call :log "=== INSTALACIÓN IPRA_I ==="
call :log "Puerto configurado: %SERVER_PORT%"
call :log "Configuración regional: %LC_NUMERIC%"

REM Verificar si Node.js está instalado
call :log "Verificando instalación de Node.js..."
node --version >nul 2>&1
if errorlevel 1 (
    call :warn "Node.js no está instalado"
    call :log "Descarga Node.js desde: https://nodejs.org/"
    call :log "Instala Node.js LTS y ejecuta este script nuevamente"
    pause
    exit /b 1
) else (
    for /f %%i in ('node --version') do set "NODE_VERSION=%%i"
    call :log "Node.js instalado: !NODE_VERSION!"
)

REM Verificar npm
npm --version >nul 2>&1
if errorlevel 1 (
    call :error "npm no está disponible"
    exit /b 1
) else (
    for /f %%i in ('npm --version') do set "NPM_VERSION=%%i"
    call :log "npm disponible: !NPM_VERSION!"
)

REM Verificar Python para WeasyPrint
call :log "Verificando Python para WeasyPrint..."
python --version >nul 2>&1
if errorlevel 1 (
    call :warn "Python no está instalado"
    call :log "Descarga Python desde: https://www.python.org/"
    call :log "WeasyPrint no estará disponible sin Python"
) else (
    for /f "tokens=2" %%i in ('python --version') do set "PYTHON_VERSION=%%i"
    call :log "Python instalado: !PYTHON_VERSION!"
    
    REM Crear entorno virtual para WeasyPrint
    if not exist "venv" (
        call :log "Creando entorno virtual Python..."
        python -m venv venv
    )
    
    call :log "Instalando WeasyPrint en entorno virtual..."
    call venv\Scripts\activate.bat
    pip install --upgrade pip
    pip install weasyprint
    call venv\Scripts\deactivate.bat
    call :log "WeasyPrint instalado correctamente"
)

REM Crear directorios necesarios
call :log "Creando directorios necesarios..."
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"
if not exist "%DOCS_DIR%" mkdir "%DOCS_DIR%"
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"
if not exist "backend\logs" mkdir "backend\logs"
call :log "Directorios creados: %BUILD_DIR%, %DOCS_DIR%, %LOG_DIR%"

REM Instalar dependencias del frontend
call :log "Instalando dependencias del frontend..."
if not exist "package.json" (
    call :error "package.json no encontrado en el directorio raíz"
    exit /b 1
)
npm install
if errorlevel 1 (
    call :error "Error al instalar dependencias del frontend"
    exit /b 1
)

REM Instalar dependencias del backend
call :log "Instalando dependencias del backend..."
if not exist "backend\package.json" (
    call :error "backend\package.json no encontrado"
    exit /b 1
)
cd backend
npm install
if errorlevel 1 (
    call :error "Error al instalar dependencias del backend"
    exit /b 1
)
cd ..

call :log "Dependencias instaladas correctamente"

REM Configurar firewall de Windows (requiere permisos de administrador)
call :log "Configurando firewall de Windows..."
netsh advfirewall firewall show rule name="IPRA_I Server" >nul 2>&1
if errorlevel 1 (
    call :log "Agregando regla de firewall para puerto %SERVER_PORT%..."
    netsh advfirewall firewall add rule name="IPRA_I Server" dir=in action=allow protocol=TCP localport=%SERVER_PORT% >nul 2>&1
    if errorlevel 1 (
        call :warn "No se pudo configurar el firewall automáticamente"
        call :warn "Configura manualmente el puerto %SERVER_PORT% en el firewall de Windows"
    ) else (
        call :log "Regla de firewall agregada correctamente"
    )
) else (
    call :log "Regla de firewall ya existe"
)

REM Build inicial
call :log "Realizando build inicial..."
npm run build
if errorlevel 1 (
    call :warn "Error en build inicial, pero continuando..."
)

REM Verificar instalación
call :log "Verificando instalación..."

REM Verificar dependencias
if not exist "node_modules" (
    call :error "Dependencias del frontend no instaladas"
    exit /b 1
)

if not exist "backend\node_modules" (
    call :error "Dependencias del backend no instaladas"
    exit /b 1
)

REM Verificar build
if not exist "%BUILD_DIR%\%COMBINED_JS_FILE%" (
    call :warn "Build de JavaScript no encontrado"
)

if not exist "%BUILD_DIR%\%COMBINED_CSS_FILE%" (
    call :warn "Build de CSS no encontrado"
)

call :log "Verificación completada"

call :log "=== INSTALACIÓN COMPLETADA ==="
echo.
call :log "Próximos pasos:"
call :log "1. Iniciar servidor: scripts\start.bat"
call :log "2. Verificar estado: scripts\status.bat"
call :log "3. Ver logs: scripts\logs.bat"
call :log "4. Parar servidor: scripts\stop.bat"
echo.
call :log "El servidor estará disponible en: http://localhost:%SERVER_PORT%"
echo.
call :log "Para documentación: scripts\generate_docs.bat"
call :log "Para rebuild: npm run build"

endlocal
pause
