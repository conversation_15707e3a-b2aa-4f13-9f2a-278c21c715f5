@echo off
REM Script de instalación desatendido para IPRA_I (Windows)
REM Ejecutar como Administrador

setlocal enabledelayedexpansion

echo === INSTALACIÓN IPRA_I ===

REM Verificar permisos de administrador
net session >nul 2>&1
if errorlevel 1 (
    echo ERROR: Ejecutar como Administrador
    exit /b 1
)

REM Cargar configuración
set "CONFIG_FILE=%~dp0config.conf"
for /f "tokens=1,2 delims==" %%a in ('type "%CONFIG_FILE%" 2^>nul ^| findstr /v "^#" ^| findstr "="') do set "%%a=%%b"

echo Puerto: %SERVER_PORT%

REM Directorio de instalación
set "INSTALL_DIR=%~dp0install\windows"
mkdir "%INSTALL_DIR%" 2>nul

REM Verificar Node.js
node --version >nul 2>&1
if not errorlevel 1 goto :deps

REM Descargar Node.js
set "NODE_FILE=%INSTALL_DIR%\node-v20.10.0-x64.msi"
if not exist "%NODE_FILE%" (
    echo Descargando Node.js...
    powershell -Command "Invoke-WebRequest -Uri 'https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi' -OutFile '%NODE_FILE%'"
)

REM Instalar Node.js
echo Instalando Node.js...
msiexec /i "%NODE_FILE%" /quiet /norestart
timeout /t 10 /nobreak >nul

:deps
REM Instalar dependencias
echo Instalando dependencias...
npm ci 2>nul || npm install
cd backend
npm ci 2>nul || npm install
cd ..

REM Firewall
netsh advfirewall firewall add rule name="IPRA_I" dir=in action=allow protocol=TCP localport=%SERVER_PORT% >nul 2>&1

REM Build
npm run build >nul 2>&1

echo.
echo INSTALACIÓN COMPLETADA
echo URL: http://localhost:%SERVER_PORT%

endlocal
