@echo off
REM Script de instalación desatendido para IPRA_I (Windows)
REM Ejecutar como Administrador

setlocal enabledelayedexpansion

echo === INSTALACIÓN AUTOMÁTICA IPRA_I ===

REM Cargar configuración
set "CONFIG_FILE=%~dp0config.conf"
for /f "tokens=1,2 delims==" %%a in ('type "%CONFIG_FILE%" 2^>nul ^| findstr /v "^#" ^| findstr "="') do set "%%a=%%b"

echo Puerto: %SERVER_PORT%

REM Verificar Node.js
"C:\Program Files\nodejs\node.exe" --version >nul 2>&1
if not errorlevel 1 (
    echo Node.js ya instalado
    goto :python
)

REM Node.js no está instalado - necesita admin
net session >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js no está instalado
    echo Ejecuta como Administrador para instalarlo automáticamente
    exit /b 1
)

REM Directorio de instalación
set "INSTALL_DIR=%~dp0install\windows"
mkdir "%INSTALL_DIR%" 2>nul

REM Descargar Node.js
set "NODE_FILE=%INSTALL_DIR%\node-v20.10.0-x64.msi"
if not exist "%NODE_FILE%" (
    echo Descargando Node.js v20.10.0...
    powershell -Command "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi' -OutFile '%NODE_FILE%'"
    if errorlevel 1 (
        echo ERROR: No se pudo descargar Node.js
        exit /b 1
    )
)

REM Instalar Node.js
echo Instalando Node.js...
msiexec /i "%NODE_FILE%" /quiet /norestart
if errorlevel 1 (
    echo ERROR: Error instalando Node.js
    exit /b 1
)

echo Node.js instalado correctamente

:python
REM Verificar Python
python --version >nul 2>&1
if not errorlevel 1 (
    echo Python ya instalado
    goto :deps
)

REM Descargar Python
set "PYTHON_FILE=%INSTALL_DIR%\python-3.11.7-amd64.exe"
if not exist "%PYTHON_FILE%" (
    echo Descargando Python 3.11.7...
    powershell -Command "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe' -OutFile '%PYTHON_FILE%'"
    if errorlevel 1 (
        echo ERROR: No se pudo descargar Python
        exit /b 1
    )
)

REM Instalar Python
echo Instalando Python...
"%PYTHON_FILE%" /quiet InstallAllUsers=1 PrependPath=1 Include_test=0
if errorlevel 1 (
    echo ERROR: Error instalando Python
    exit /b 1
)

echo Python instalado correctamente

:deps
REM Crear directorios
mkdir build 2>nul
mkdir logs 2>nul
mkdir docs_generated 2>nul
mkdir backend\logs 2>nul

REM Limpiar package.json (quitar weasyprint que es de Python)
echo Limpiando package.json...
powershell -Command "(Get-Content package.json) -replace '  \"dependencies\": \{[^}]*\},' -replace ',\s*\"dependencies\": \{[^}]*\}' | Set-Content package.json"

REM Instalar dependencias frontend
echo Instalando dependencias frontend...
"C:\Program Files\nodejs\npm.cmd" install
if errorlevel 1 (
    echo ERROR: No se pudieron instalar dependencias frontend
    exit /b 1
)

REM Instalar dependencias backend
echo Instalando dependencias backend...
cd backend
"C:\Program Files\nodejs\npm.cmd" install
if errorlevel 1 (
    echo ERROR: No se pudieron instalar dependencias backend
    exit /b 1
)
cd ..

REM Instalar WeasyPrint en entorno virtual Python
echo Instalando WeasyPrint...
python -m venv venv
call venv\Scripts\activate.bat
python -m pip install --upgrade pip
python -m pip install weasyprint
call venv\Scripts\deactivate.bat
echo WeasyPrint instalado en entorno virtual

REM Firewall
echo Configurando firewall...
netsh advfirewall firewall delete rule name="IPRA_I" >nul 2>&1
netsh advfirewall firewall add rule name="IPRA_I" dir=in action=allow protocol=TCP localport=%SERVER_PORT% >nul 2>&1
if errorlevel 1 (
    echo AVISO: Error configurando firewall
) else (
    echo Firewall configurado para puerto %SERVER_PORT%
)

REM Build
echo Realizando build...
"C:\Program Files\nodejs\node.exe" build.js
if errorlevel 1 (
    echo ERROR: Build falló
    exit /b 1
)

echo.
echo INSTALACIÓN COMPLETADA
echo URL: http://localhost:%SERVER_PORT%
echo.
echo Verificando archivos generados:
if exist build\aplicacion.js (echo - aplicacion.js: OK) else (echo - aplicacion.js: FALTA)
if exist build\aplicacion.css (echo - aplicacion.css: OK) else (echo - aplicacion.css: FALTA)

endlocal
