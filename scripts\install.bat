@echo off
REM Script de instalación para IPRA_I (Windows)
REM Edita scripts/config.conf antes de ejecutar

setlocal enabledelayedexpansion

echo === INSTALACIÓN IPRA_I ===

REM Cargar configuración
set "CONFIG_FILE=%~dp0config.conf"
if not exist "%CONFIG_FILE%" (
    echo ERROR: Archivo scripts/config.conf no encontrado.
    echo Edita scripts/config.conf con tu configuración antes de ejecutar install.bat
    pause
    exit /b 1
)

REM Leer configuración
for /f "tokens=1,2 delims==" %%a in ('type "%CONFIG_FILE%" ^| findstr /v "^#" ^| findstr "="') do (
    set "%%a=%%b"
)

echo Puerto configurado: %SERVER_PORT%
echo Configuración regional: %LC_NUMERIC%
echo.

REM Verificar si Node.js está instalado
echo Verificando Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js no está instalado
    echo Descarga Node.js desde: https://nodejs.org/
    echo Instala Node.js LTS y ejecuta este script nuevamente
    pause
    exit /b 1
) else (
    for /f %%i in ('node --version') do echo Node.js instalado: %%i
)

REM Crear directorios necesarios
echo Creando directorios...
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"
if not exist "%DOCS_DIR%" mkdir "%DOCS_DIR%"
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"
if not exist "backend\logs" mkdir "backend\logs"

REM Instalar dependencias del frontend
echo Instalando dependencias del frontend...
npm install
if errorlevel 1 (
    echo ERROR: Error al instalar dependencias del frontend
    pause
    exit /b 1
)

REM Instalar dependencias del backend
echo Instalando dependencias del backend...
cd backend
npm install
if errorlevel 1 (
    echo ERROR: Error al instalar dependencias del backend
    pause
    exit /b 1
)
cd ..

REM Configurar firewall de Windows
echo Configurando firewall...
netsh advfirewall firewall add rule name="IPRA_I Server" dir=in action=allow protocol=TCP localport=%SERVER_PORT% >nul 2>&1

REM Build inicial
echo Realizando build inicial...
npm run build >nul 2>&1

echo.
echo === INSTALACIÓN COMPLETADA ===
echo Puerto: %SERVER_PORT%
echo URL: http://localhost:%SERVER_PORT%
echo.
echo Próximos pasos:
echo 1. scripts\start.bat
echo 2. scripts\stop.bat
echo.
pause

endlocal
