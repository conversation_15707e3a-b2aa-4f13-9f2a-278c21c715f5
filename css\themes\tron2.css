/* Estilos del acordeón para tema Tron */
.theme-tron .accordion-section {
    border: 1px solid var(--border-color);
    box-shadow: var(--glow-effect);
    background-color: var(--surface-color);
}

.theme-tron .accordion-header {
    background-color: rgba(0, 162, 255, 0.2);
    border-bottom: 1px solid var(--border-color);
}

.theme-tron .accordion-header:hover {
    background-color: rgba(0, 162, 255, 0.3);
}

.theme-tron .accordion-header.active {
    background-color: rgba(0, 162, 255, 0.4);
}

.theme-tron .accordion-content {
    background-color: var(--surface-color);
}
