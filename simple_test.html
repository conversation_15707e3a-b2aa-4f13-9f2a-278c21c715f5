<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple IndexedDB Test</title>
</head>
<body>
    <h1>Simple IndexedDB Test</h1>
    <div id="output"></div>
    
    <script>
        const output = document.getElementById('output');
        
        function log(message) {
            console.log(message);
            output.innerHTML += '<p>' + message + '</p>';
        }
        
        async function simpleTest() {
            try {
                log('Probando IndexedDB básico...');
                
                // Abrir base de datos
                const request = indexedDB.open('test_db', 1);
                
                request.onupgradeneeded = (event) => {
                    const db = event.target.result;
                    if (!db.objectStoreNames.contains('test_store')) {
                        db.createObjectStore('test_store', { keyPath: 'id' });
                    }
                };
                
                request.onsuccess = async (event) => {
                    const db = event.target.result;
                    log('✅ Base de datos abierta');
                    
                    // Guardar un objeto simple
                    const transaction = db.transaction(['test_store'], 'readwrite');
                    const store = transaction.objectStore('test_store');
                    
                    const testObject = {
                        id: 1,
                        name: 'Test Object',
                        data: { value: 123 }
                    };
                    
                    const putRequest = store.put(testObject);
                    
                    putRequest.onsuccess = () => {
                        log('✅ Objeto guardado');
                        
                        // Leer el objeto
                        const getRequest = store.get(1);
                        getRequest.onsuccess = () => {
                            const result = getRequest.result;
                            log(`✅ Objeto leído: ${result.name}`);
                            log('🎉 Prueba básica completada');
                        };
                    };
                    
                    putRequest.onerror = () => {
                        log('❌ Error al guardar objeto: ' + putRequest.error);
                    };
                };
                
                request.onerror = () => {
                    log('❌ Error al abrir base de datos: ' + request.error);
                };
                
            } catch (error) {
                log('❌ Error: ' + error.message);
                console.error('Error completo:', error);
            }
        }
        
        // Ejecutar pruebas cuando la página esté cargada
        document.addEventListener('DOMContentLoaded', simpleTest);
    </script>
</body>
</html>
