/*****
 * Ú<PERSON>s
 */


/**
 * Versión personalizada de JSON.stringify que ignora propiedades 'chart' y funciones
 * @param {Object} obj - Objeto a convertir a string
 * @param {Function|Array} replacer - Función o array para filtrar propiedades (opcional)
 * @param {string|number} space - Espaciado para formatear el resultado (opcional)
 * @returns {string} - Representación en string del objeto
 */
app.stringify = function (obj, replacer, space) {
    // Función replacer personalizada que ignora propiedades 'chart' y funciones
    const customReplacer = (key, value) => {
        // Ignorar propiedades 'chart'
        if (key === 'chart') return undefined;

        // Ignorar funciones
        if (typeof value === 'function') return undefined;

        // Si hay un replacer personalizado, aplicarlo
        if (typeof replacer === 'function') {
            return replacer(key, value);
        }

        // Si hay un array de propiedades permitidas, verificar
        if (Array.isArray(replacer) && key !== '' && !replacer.includes(key)) {
            return undefined;
        }

        return value;
    };

    // Usar JSON.stringify con nuestro replacer personalizado
    try {
        return JSON.stringify(obj, customReplacer, space);
    } catch (error) {
        console.error('Error en app.stringify:', error);
        // En caso de error, intentar una versión simplificada
        return JSON.stringify({
            error: 'Error al serializar objeto'
        });
    }
};

/**
 Sistema de eventos por delegación. Para evitar preocuparnos crear y destruir los eventos evitando al mismo tiempo
 el código boilerplte que siempre es el mismo pero no sirve para nada.

 on(elemento,nombre_evento,selector,handler)

 Ejemplo uso: on(document,'click','.button-cancel',close_popup);
 Ésto hace que al hacer clic en cualquier sitio del documento, si se hace clic sobre un elemento
 de clase .button-cancel, se invoque a la rutnia close_popup.

 En la rutina "this" es app.pant_act que es la última pantalla abierta.

 En app.acciones[] se van añadiendo accioens genéricas para toda la aplicación que no dependen de la
 pantalla actual. Por ejemplo, app.acciones.inicio es una función para vovler  al menú principal.
 */
function on(elemento, nombre_evento, selector, handler) {
    elemento.addEventListener(nombre_evento, function (event) {
        var t = event.target;
        while (t && t !== this) {
            if (t.matches(selector)) {
                event.stopPropagation();
                event.preventDefault();
                handler.call(t, event);
                return;
            }
            t = t.parentNode;
        }
    });
}

/** Función para gestionar eventos con data-accion (clic en elementos)  y data-accion2 (se activa con doble clic)*/
function func_proc_accion(e) {
    var accion = this.dataset.accion || this.dataset.accion2;
    var func_accion;
    e.stopPropagation();
    e.preventDefault();

    // Verificar si la acción contiene un punto (formato: "manager.metodo")
    if (accion.includes('.')) {
        // Dividir la acción en nombre del manager y método
        var partes = accion.split('.');
        var managerName = partes[0];
        var methodName = partes[1];

        // Obtener la instancia del manager desde app
        var manager = app[managerName];
        // Asignar el contexto de la instancia al método
        func_accion = manager[methodName];
        func_accion = func_accion.bind(manager);
    } else {
        // Si no hay punto, buscar la acción en app.acciones
        func_accion = app.acciones[accion];
        //Si no hay acción mirar en la pantalla actual
        if (!func_accion) {
            if (app.pant_act && app.pant_act[accion]) {
                func_accion = app.pant_act[accion];
                func_accion = func_accion.bind(app.pant_act);
            }
        }
    }

    if (func_accion) {
        let a_cerrar = e.target.closest('.remove_on_click')
        func_accion(e); // Ejecutar la acción si se encontró
        if (a_cerrar) a_cerrar.remove();
        else if (e.target.parentElement.classList.contains('dropdown-menu')) closePopup(e.target.parentElement);
    }
}


//Los atributos data-accion tienen nombres de acciones a ejecutar
on(document, 'click', '[data-accion]', func_proc_accion); //para clic
on(document, 'dblclick', '[data-accion2]', func_proc_accion); //para doble clic
app.on = on;

/**
 * Inicializa la aplicación
 */
function initApp() {
    // Configurar eventos de la interfaz
    setupUIEvents();

    // Configurar manejador para la tecla Escape
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            // Obtener el último elemento de la pila UI sin eliminarlo
            const lastElement = peekUIStack();
            if (!lastElement) return;

            // Si es un diálogo, verificar si tiene callback onCancel o onClose
            if (lastElement.type === 'dialog') {
                if (lastElement.params && lastElement.params.onCancel) {
                    lastElement.params.onCancel();
                } else if (lastElement.params && lastElement.params.onClose) {
                    lastElement.params.onClose();
                }
                // No es necesario hacer nada más, el callback se encargará de cerrar el diálogo
            } else {
                // Para otros tipos de elementos, usar la función estándar
                closeLastUIElement();
            }

            // Detener la propagación para evitar que otros manejadores de Escape se activen
            e.stopPropagation();
        }
    });

    console.log('Aplicación inicializada con estructura app:', app);

    setupNavigation();
    initHashNavigation();
    app['themeManager'].setupThemeEvents();
    app['themeManager'].loadSavedTheme();

    // Inicializar autenticación (usando el objeto app).
    //Si el usuario está identificado se llama a handleHashChange() y si no, a la pantalla de login
    app.authManager.init();

    // Inicializar el gestor de perfil de usuario
    if (app.userProfileManager) {
        app.userProfileManager.init();
    }
}

/**
 * Configura los eventos de la interfaz de usuario
 */
function setupUIEvents() {
    // Botón de opciones y botón de menú móvil
    const optionsBtn = document.getElementById('options-btn');
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const optionsMenu = document.getElementById('options-menu');

    // Función para mostrar el menú con animación del icono de hamburguesa
    const openDashboardMenuWithIconAnimation = () => {
        // Si el menú ya está visible, no hacer nada
        if (!optionsMenu.classList.contains('hidden')) {
            return;
        }

        // Obtener el icono de hamburguesa
        const hamburgerIcon = mobileMenuBtn.querySelector('.hamburger-icon');

        // Añadir evento para detectar el fin de la animación del icono
        const handleIconAnimationEnd = () => {
            // Quitar la clase hidden para que el menú sea visible en el DOM
            optionsMenu.classList.remove('hidden');

            // Añadir la clase de animación de entrada al menú
            optionsMenu.classList.add('menu-entering');

            // Añadir evento para quitar la clase de animación cuando termine
            const handleMenuAnimationEnd = (e) => {
                // Solo procesar el evento si viene del menú
                if (e.target !== optionsMenu) return;

                // Quitar la clase de animación
                optionsMenu.classList.remove('menu-entering');

                // Eliminar el evento para no acumular listeners
                optionsMenu.removeEventListener('animationend', handleMenuAnimationEnd);
            };

            // Escuchar el evento de fin de animación del menú
            optionsMenu.addEventListener('animationend', handleMenuAnimationEnd);

            // Eliminar el evento para no acumular listeners
            hamburgerIcon.removeEventListener('transitionend', handleIconAnimationEnd);
        };

        // Escuchar el evento de fin de transición del icono
        hamburgerIcon.addEventListener('transitionend', handleIconAnimationEnd);

        // Añadir la clase de animación de apertura al icono
        hamburgerIcon.classList.add('menu-opening');
        hamburgerIcon.classList.remove('menu-closing');
    };

    // Función para cerrar el menú con animación del icono de hamburguesa
    const closeDashboardMenuWithIconAnimation = () => {
        // Si el menú ya está oculto, no hacer nada
        if (optionsMenu.classList.contains('hidden')) {
            return;
        }

        // Obtener el icono de hamburguesa
        const hamburgerIcon = mobileMenuBtn.querySelector('.hamburger-icon');

        // Añadir la clase de animación de cierre al icono
        hamburgerIcon.classList.remove('menu-opening');
        hamburgerIcon.classList.add('menu-closing');

        // Añadir la clase de animación de salida al menú
        optionsMenu.classList.remove('menu-entering');
        optionsMenu.classList.add('menu-exiting');

        // Añadir evento para detectar el fin de la animación del menú
        const handleMenuAnimationEnd = (e) => {
            // Solo procesar el evento si viene del menú (no del icono)
            if (e.target !== optionsMenu) return;

            // Ocultar el menú cuando termine la animación
            optionsMenu.classList.add('hidden');
            optionsMenu.classList.remove('menu-exiting');

            // Eliminar el evento para no acumular listeners
            optionsMenu.removeEventListener('animationend', handleMenuAnimationEnd);
        };

        // Escuchar el evento de fin de animación del menú
        optionsMenu.addEventListener('animationend', handleMenuAnimationEnd);
    };

    // Función para mostrar el menú sin animación del icono (para el botón de texto)
    const openDashboardMenu = () => {
        // Si el menú ya está visible, no hacer nada
        if (!optionsMenu.classList.contains('hidden')) {
            return;
        }

        // Quitar la clase hidden para que el menú sea visible en el DOM
        optionsMenu.classList.remove('hidden');

        // Añadir la clase de animación de entrada al menú
        optionsMenu.classList.add('menu-entering');

        // Añadir evento para quitar la clase de animación cuando termine
        const handleMenuAnimationEnd = (e) => {
            // Solo procesar el evento si viene del menú
            if (e.target !== optionsMenu) return;

            // Quitar la clase de animación
            optionsMenu.classList.remove('menu-entering');

            // Eliminar el evento para no acumular listeners
            optionsMenu.removeEventListener('animationend', handleMenuAnimationEnd);
        };

        // Escuchar el evento de fin de animación del menú
        optionsMenu.addEventListener('animationend', handleMenuAnimationEnd);
    };

    // Función para cerrar el menú sin animación del icono (para el botón de texto)
    const closeDashboardMenu = () => {
        // Si el menú ya está oculto, no hacer nada
        if (optionsMenu.classList.contains('hidden')) {
            return;
        }

        // Añadir la clase de animación de salida al menú
        optionsMenu.classList.remove('menu-entering');
        optionsMenu.classList.add('menu-exiting');

        // Añadir evento para detectar el fin de la animación del menú
        const handleMenuAnimationEnd = (e) => {
            // Solo procesar el evento si viene del menú
            if (e.target !== optionsMenu) return;

            // Ocultar el menú cuando termine la animación
            optionsMenu.classList.add('hidden');
            optionsMenu.classList.remove('menu-exiting');

            // Eliminar el evento para no acumular listeners
            optionsMenu.removeEventListener('animationend', handleMenuAnimationEnd);
        };

        // Escuchar el evento de fin de animación del menú
        optionsMenu.addEventListener('animationend', handleMenuAnimationEnd);
    };

    // Función para alternar el menú con animación del icono (para el botón de hamburguesa)
    const toggleMenuWithIconAnimation = () => {
        if (optionsMenu.classList.contains('hidden')) {
            openDashboardMenuWithIconAnimation();
        } else {
            closeDashboardMenuWithIconAnimation();
        }
    };

    // Función para alternar el menú sin animación del icono (para el botón de texto)
    const toggleMenu = () => {
        if (optionsMenu.classList.contains('hidden')) {
            openDashboardMenu();
        } else {
            closeDashboardMenu();
        }
    };

    // Asignar eventos a los botones
    optionsBtn.addEventListener('click', toggleMenu);
    if (mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', toggleMenuWithIconAnimation);
    }

    // Función auxiliar para cerrar el menú (funciona con ambos tipos de botones)
    // La hacemos global para que pueda ser llamada desde otros archivos
    window.closeMenuUniversal = () => {
        // Si el menú ya está oculto, no hacer nada
        if (optionsMenu.classList.contains('hidden')) {
            return;
        }

        // Si el icono de hamburguesa está presente, restaurarlo
        const hamburgerIcon = mobileMenuBtn ? mobileMenuBtn.querySelector('.hamburger-icon') : null;
        if (hamburgerIcon) {
            hamburgerIcon.classList.remove('menu-opening');
            hamburgerIcon.classList.add('menu-closing');
        }

        // Añadir la clase de animación de salida al menú
        optionsMenu.classList.remove('menu-entering');
        optionsMenu.classList.add('menu-exiting');

        // Añadir evento para detectar el fin de la animación del menú
        const handleMenuAnimationEnd = (e) => {
            // Solo procesar el evento si viene del menú
            if (e.target !== optionsMenu) return;

            // Ocultar el menú cuando termine la animación
            optionsMenu.classList.add('hidden');
            optionsMenu.classList.remove('menu-exiting');

            // Eliminar el evento para no acumular listeners
            optionsMenu.removeEventListener('animationend', handleMenuAnimationEnd);
        };

        // Escuchar el evento de fin de animación del menú
        optionsMenu.addEventListener('animationend', handleMenuAnimationEnd);
    };

    // El cierre al hacer clic fuera ahora se maneja en dialogs.js
    // Configurar el botón de opciones para usar el nuevo sistema
    optionsBtn.dataset.toggleElement = 'options-menu';
    if (mobileMenuBtn) {
        mobileMenuBtn.dataset.toggleElement = 'options-menu';
    }

    // Añadir un manejador de eventos global para cerrar el menú al hacer clic fuera
    document.addEventListener('click', (e) => {
        // Si el menú está visible y el clic no fue dentro del menú ni en los botones que lo abren
        if (!optionsMenu.classList.contains('hidden') &&
            !optionsMenu.contains(e.target) &&
            e.target !== optionsBtn &&
            e.target !== mobileMenuBtn &&
            !optionsBtn.contains(e.target) &&
            (mobileMenuBtn ? !mobileMenuBtn.contains(e.target) : true)) {

            // Cerrar el menú
            closeMenuUniversal();
        }
    });

    // Botones del menú de opciones
    const configDashboardBtn = document.getElementById('config-dashboard-btn');
    const addWidgetBtn = document.getElementById('add-widget-btn');
    // Estos botones pueden no existir si están comentados en el HTML
    const deleteWidgetBtn = document.getElementById('delete-widget-btn');
    const moveWidgetBtn = document.getElementById('move-widget-btn');
    // La variable editWidgetBtn se declara más adelante

    // Modal de configuración del tablero
    const dashboardConfigModal = document.getElementById('dashboard-config-modal');
    const saveDashboardConfigBtn = document.getElementById('save-dashboard-config');
    const dashboardOptionsBtn = document.getElementById('dashboard-options-btn');
    const cancelDashboardConfigBtn = document.getElementById('cancel-dashboard-config');
    const dashboardOptionsPopup = document.getElementById('dashboard-options-popup');

    configDashboardBtn.addEventListener('click', () => {
        // Cargar configuración actual en el formulario
        const dashboardMgr = app.dashboardManager || window.dashboardManager; // Usar app o fallback a window
        const config = dashboardMgr.getDashboardConfig();
        document.getElementById('dashboard-name').value = config.name;
        document.getElementById('dashboard-width').value = config.width;
        document.getElementById('dashboard-height').value = config.height;
        document.getElementById('dashboard-bg-color').value = config.backgroundColor;
        document.getElementById('dashboard-grid-color').value = config.gridColor || '#dddddd';
        document.getElementById('dashboard-widget-text-color').value = config.widgetTextColor || '#000000';
        document.getElementById('dashboard-widget-bg-color').value = config.widgetBgColor || '#ffffff';
        document.getElementById('dashboard-show-borders').checked = config.showWidgetBorders;
        document.getElementById('dashboard-transparent-widgets').checked = config.transparentWidgets;
        document.getElementById('dashboard-show-grid').checked = config.showGrid || false;

        dashboardMgr.deactivateEditMode();

        // Mostrar modal
        openModal('dashboard-config-modal');

        // Cerrar el menú
        closeMenuUniversal();
    });

    // Mostrar/ocultar popup de opciones de tablero
    dashboardOptionsBtn.addEventListener('click', (e) => {
        if (dashboardOptionsPopup.classList.contains('hidden')) {
            // Abrir el popup
            openPopup(dashboardOptionsPopup);
        } else {
            // Cerrar el popup
            closePopup(dashboardOptionsPopup);
        }
        e.stopPropagation(); // Evitar que el clic se propague al documento
    });

    // El cierre al hacer clic fuera ahora se maneja en dialogs.js
    // Configurar el botón de opciones para usar el nuevo sistema
    dashboardOptionsBtn.dataset.toggleElement = 'dashboard-options-popup';

    // Botones del popup de opciones de tablero
    const newDashboardBtn = document.getElementById('new-dashboard-btn');
    const duplicateDashboardBtn = document.getElementById('duplicate-dashboard-btn');
    const deleteDashboardBtn = document.getElementById('delete-dashboard-btn');
    const closeDashboardOptionsBtn = document.getElementById('close-dashboard-options');
    const cancelDashboardOptionsBtn = document.getElementById('cancel-dashboard-options');

    // Botón para crear un nuevo tablero
    newDashboardBtn.addEventListener('click', () => {
        // Limpiar el campo de nombre y mensaje de error
        const nameInput = document.getElementById('new-dashboard-name');
        const errorElement = document.getElementById('new-dashboard-error');
        nameInput.value = '';
        errorElement.textContent = '';

        // Cerrar el popup de opciones si estaba visible
        if (!dashboardOptionsPopup.classList.contains('hidden'))
            closePopup(dashboardOptionsPopup)

        // Mostrar el modal para crear nuevo tablero
        openModal('dashboard-ask-name');

        // Guardar el estado del modal de configuración para restaurarlo si se cancela
        nameInput.focus();
    });

    // Botón para duplicar el tablero actual
    duplicateDashboardBtn.addEventListener('click', () => {
        // Obtener el nombre del tablero actual para sugerir un nombre para la copia
        const dashboardMgr = app.dashboardManager || window.dashboardManager; // Usar app o fallback a window
        const currentDashboardName = dashboardMgr.dashboard.name;
        const suggestedName = `Copia de ${currentDashboardName}`;

        // Limpiar el campo de nombre y mensaje de error
        const nameInput = document.getElementById('new-dashboard-name');
        const errorElement = document.getElementById('new-dashboard-error');
        nameInput.value = suggestedName;
        errorElement.textContent = '';

        // Obtener el modal
        const dashboardAskNameModal = document.getElementById('dashboard-ask-name');

        // Guardar el ID del tablero actual para duplicarlo cuando se acepte el nombre
        dashboardAskNameModal.dataset.duplicateMode = 'true';

        // Mostrar el modal para crear nuevo tablero
        openModal('dashboard-ask-name');

        // Dar foco a la caja de texto y seleccionar todo el texto
        setTimeout(() => {
            nameInput.focus();
            nameInput.select();
        }, 100);

        // Cerrar el popup de opciones
        closePopup(dashboardOptionsPopup);

        // Guardar el estado del modal de configuración para restaurarlo si se cancela
        dashboardAskNameModal.dataset.configModalOpen = !dashboardConfigModal.classList.contains('hidden');

        // Cerrar el modal de configuración
        closeModal(dashboardConfigModal);
    });

    // Botón para eliminar el tablero actual
    deleteDashboardBtn.addEventListener('click', () => {
        // Obtener referencias a los managers (usando app o fallback a window)
        const dashboardMgr = app.dashboardManager || window.dashboardManager;
        const widgetMgr = app.widgetManager || window.widgetManager;

        // Verificar si hay más de un tablero
        if (dashboardMgr.dashboards.length <= 1) {
            alert('No se puede eliminar el único tablero existente. Crea otro tablero primero.');
            return;
        }

        // Pedir confirmación usando el diálogo de eliminación con tinte rojizo
        dialogManager.confirmDelete('¿Estás seguro de que deseas eliminar este tablero? Esta acción no se puede deshacer.').then(confirmed => {
            if (confirmed) {
                // Eliminar el tablero actual
                dashboardMgr.deleteDashboard();

                // Aplicar la configuración del nuevo tablero actual
                dashboardMgr.applyDashboardConfig();

                // Renderizar los widgets del nuevo tablero actual
                const dashboardElement = document.getElementById('dashboard');
                if (dashboardElement) {
                    widgetMgr.renderWidgets(dashboardElement);
                }

                // Cerrar el popup y el modal
                closePopup(dashboardOptionsPopup);
                closeModal(dashboardConfigModal);
            }
        });
    });

    saveDashboardConfigBtn.addEventListener('click', () => {
        // Obtener valores del formulario
        const name = document.getElementById('dashboard-name').value;
        const width = parseInt(document.getElementById('dashboard-width').value);
        const height = parseInt(document.getElementById('dashboard-height').value);
        const backgroundColor = document.getElementById('dashboard-bg-color').value;
        const showWidgetBorders = document.getElementById('dashboard-show-borders').checked;
        const transparentWidgets = document.getElementById('dashboard-transparent-widgets').checked;
        const showGrid = document.getElementById('dashboard-show-grid').checked;
        const gridColor = document.getElementById('dashboard-grid-color').value;
        const widgetTextColor = document.getElementById('dashboard-widget-text-color').value;
        const widgetBgColor = document.getElementById('dashboard-widget-bg-color').value;

        // Actualizar configuración (usando app o fallback a window)
        const dashboardMgr = app.dashboardManager || window.dashboardManager;
        dashboardMgr.updateDashboardConfig({
            name,
            width,
            height,
            backgroundColor,
            showWidgetBorders,
            transparentWidgets,
            showGrid,
            gridColor,
            widgetTextColor,
            widgetBgColor
        });

        // Ocultar modal
        closeModal(dashboardConfigModal);
    });

    // Botón de cancelar configuración de tablero
    cancelDashboardConfigBtn.addEventListener('click', () => {
        // Ocultar modal sin guardar cambios
        closeModal(dashboardConfigModal);
    });

    // El botón addWidgetBtn ya está declarado arriba

    // Prepara y abre el diálogo para añadir un nuevo widget
    function openAddWidgetDialog() {
        // Obtener referencias a los managers
        const dashboardMgr = app.dashboardManager || window.dashboardManager;
        const widgetMgr = app.widgetManager || window.widgetManager;

        // Crear un widget vacío de tipo texto con dimensiones predeterminadas
        const widgetType = 'text';
        let width, height;

        // Crear inputs temporales para usar con setDefaultWidgetDimensions
        const tempWidthInput = {
            value: 0
        };
        const tempHeightInput = {
            value: 0
        };

        // Establecer dimensiones predeterminadas para widget de texto
        widgetMgr.setDefaultWidgetDimensions(widgetType, tempWidthInput, tempHeightInput);
        width = parseInt(tempWidthInput.value);
        height = parseInt(tempHeightInput.value);

        // Calcular posición inicial para el nuevo widget
        const dashboardWidth = dashboardMgr.dashboard.width || 800;
        const dashboardHeight = dashboardMgr.dashboard.height || 600;
        const maxX = Math.max(0, dashboardWidth - width);
        const maxY = Math.max(0, dashboardHeight - height);

        // Generar posición aleatoria dentro de los límites del dashboard
        const x = Math.floor(Math.random() * maxX);
        const y = Math.floor(Math.random() * maxY);

        // Crear objeto de estilos con los colores por defecto del tablero
        const style = {
            backgroundColor: "defecto",
            textColor: "defecto",
            borderColor: "defecto"
        };

        // Parámetros para el widget de texto
        const params = {
            text: "(texto)"
        };

        console.log('Creando widget de texto vacío con dimensiones:', width, 'x', height);

        // Crear el widget
        widgetMgr.createWidget({
                type: widgetType,
                width: width,
                height: height,
                x: x,
                y: y,
                params: params,
                style: style
            })
            .then((widget) => {
                // Guardar el tablero
                return dashboardMgr.saveDashboard()
                    .then(() => {
                        console.log('Tablero guardado correctamente con el nuevo widget');
                        return widget;
                    })
                    .catch(error => {
                        console.error('Error al guardar el tablero:', error);
                        return widget;
                    });
            })
            .then((widget) => {
                // Renderizar widgets
                const dashboardElement = document.getElementById('dashboard');
                if (dashboardElement) {
                    widgetMgr.renderWidgets(dashboardElement);
                }

                // Abrir el diálogo de edición para el nuevo widget
                dashboardMgr.openWidgetEditDialog(widget.id);
            })
            .catch(error => {
                console.error('Error al crear widget:', error);
            });
    }

    // Modificar el event listener del botón addWidgetBtn
    addWidgetBtn.addEventListener('click', () => {
        openAddWidgetDialog();
        closeMenuUniversal();
    });

    // El event listener para cambio de tipo de widget ahora se configura dentro de openAddWidgetDialog()
    // para cada instancia clonada del diálogo

    // Event listeners para los checkboxes de configuración del widget "Ultimos"
    const setupLatestWidgetCheckboxes = (prefix) => {
        const includeFullTimeCheckbox = document.getElementById(`${prefix}latest-include-full-time`);
        const includeMinutesSecondsCheckbox = document.getElementById(`${prefix}latest-include-minutes-seconds`);

        if (includeFullTimeCheckbox && includeMinutesSecondsCheckbox) {
            // Cuando se marca "Incluir hora completa", desmarcar "Incluir minutos y segundos"
            includeFullTimeCheckbox.addEventListener('change', () => {
                if (includeFullTimeCheckbox.checked) {
                    includeMinutesSecondsCheckbox.checked = false;
                }
            });

            // Cuando se marca "Incluir minutos y segundos", desmarcar "Incluir hora completa"
            includeMinutesSecondsCheckbox.addEventListener('change', () => {
                if (includeMinutesSecondsCheckbox.checked) {
                    includeFullTimeCheckbox.checked = false;
                }
            });
        }
    };

    // Configurar checkboxes para el formulario de creación
    setupLatestWidgetCheckboxes('');

    // Configurar checkboxes para el formulario de edición
    setupLatestWidgetCheckboxes('edit-');

    // El event listener para el botón de guardar widget ahora se configura
    // dentro de openAddWidgetDialog() para cada instancia clonada del diálogo

    // Botón de borrar widget (si existe)
    if (deleteWidgetBtn) {
        deleteWidgetBtn.addEventListener('click', () => {
            dashboardManager.deactivateEditMode();
            dashboardManager.activateDeleteMode();
            closeMenuUniversal();
        });
    }

    // Botón de mover widget (si existe)
    if (moveWidgetBtn) {
        moveWidgetBtn.addEventListener('click', () => {
            dashboardManager.deactivateEditMode();
            dashboardManager.activateMoveMode();
            closeMenuUniversal();
        });
    }

    // Botón de cambio de tablero
    const switchDashboardBtn = document.getElementById('switch-dashboard-btn');
    const switchDashboardPopup = document.getElementById('switch-dashboard-popup');
    const dashboardList = document.getElementById('dashboard-list');
    const acceptDashboardSwitch = document.getElementById('accept-dashboard-switch');
    const cancelDashboardSwitch = document.getElementById('cancel-dashboard-switch');
    let selectedDashboardId = null;

    // Función para mostrar el popup de cambio de tablero
    const showSwitchDashboardPopup = () => {
        // Mostrar un indicador de carga
        dashboardList.innerHTML = '<div class="loading-indicator">Cargando tableros...</div>';

        // Mostrar popup inmediatamente para que el usuario vea que algo está pasando
        openPopup(switchDashboardPopup);

        // Resetear el ID del tablero seleccionado
        selectedDashboardId = null;

        // Desactivar el botón Aceptar inicialmente
        acceptDashboardSwitch.disabled = true;

        // Obtener el ID de la empresa del dashboard actual usando el nuevo método
        const companyId = dashboardManager.getEmpresaId();
        const currentDashboardId = dashboardManager.currentDashboardId;

        console.log(`Obteniendo tableros para la empresa ${companyId}...`);

        // Obtener los tableros de la empresa desde la base de datos
        dbManager.getDashboards(companyId)
            .then(dashboards => {
                console.log(`Tableros obtenidos para empresa ${companyId}:`, dashboards);

                // Actualizar los dashboards en el dashboardManager
                // (Los dashboards ya tienen el ID de empresa asignado por dbManager.getDashboards)
                dashboardManager.dashboards = dashboards;
                dashboardManager.companyId = companyId;

                // Limpiar lista actual
                dashboardList.innerHTML = '';

                // Si no hay tableros, mostrar mensaje
                if (dashboards.length === 0) {
                    dashboardList.innerHTML = '<div class="no-dashboards">No hay tableros disponibles</div>';
                    return;
                }

                // Crear elementos para cada tablero
                dashboards.forEach(dashboard => {
                    const item = document.createElement('button');
                    item.className = `popup-option ${dashboard.id === currentDashboardId ? 'danger-option' : ''}`;

                    // Mostrar el nombre del tablero y el usuario que lo creó
                    const dashboardName = document.createElement('span');
                    dashboardName.className = 'dashboard-name';
                    dashboardName.textContent = dashboard.name;

                    const userName = document.createElement('span');
                    userName.className = 'dashboard-user';
                    userName.textContent = dashboard.userName ? `(${dashboard.userName})` : '';

                    item.appendChild(dashboardName);
                    item.appendChild(userName);
                    item.dataset.dashboardId = dashboard.id;

                    // Si no es el tablero actual, añadir eventos
                    if (dashboard.id !== currentDashboardId) {
                        item.addEventListener('click', () => {
                            // Deseleccionar todos los items
                            document.querySelectorAll('.popup-option').forEach(el => {
                                el.classList.remove('selected');
                            });
                            // Seleccionar este item
                            item.classList.add('selected');
                            selectedDashboardId = dashboard.id;

                            // Activar el botón Aceptar
                            acceptDashboardSwitch.disabled = false;
                        });

                        item.addEventListener('dblclick', () => {
                            // Deseleccionar todos los items primero
                            document.querySelectorAll('.popup-option').forEach(el => {
                                el.classList.remove('selected');
                            });
                            // Seleccionar este item visualmente
                            item.classList.add('selected');
                            selectedDashboardId = dashboard.id;
                            acceptDashboardSwitch.click();
                        });
                    }

                    dashboardList.appendChild(item);
                });
            })
            .catch(error => {
                console.error(`Error al obtener tableros para empresa ${companyId}:`, error);
                dashboardList.innerHTML = `<div class="error-message">Error al cargar tableros: ${error.message}</div>`;
            });

        closeMenuUniversal();
    };

    // Evento para el botón de cambio de tablero
    switchDashboardBtn.addEventListener('click', showSwitchDashboardPopup);

    // Evento para el título del tablero
    const headerTitle = document.querySelector('header h1');
    headerTitle.addEventListener('dblclick', showSwitchDashboardPopup);

    // Eventos para los botones de acción rápida
    const quickSwitchDashboardBtn = document.getElementById('quick-switch-dashboard');
    const quickNewDashboardBtn = document.getElementById('quick-new-dashboard');
    const quickConfigDashboardBtn = document.getElementById('quick-config-dashboard');
    const quickAddWidgetBtn = document.getElementById('quick-add-widget');

    // Conectar con las funcionalidades existentes
    if (quickSwitchDashboardBtn) {
        quickSwitchDashboardBtn.addEventListener('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            showSwitchDashboardPopup();
        });
    }

    if (quickNewDashboardBtn) {
        quickNewDashboardBtn.addEventListener('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            // Reutilizar la funcionalidad del botón de nuevo tablero
            newDashboardBtn.click();
        });
    }

    if (quickConfigDashboardBtn) {
        quickConfigDashboardBtn.addEventListener('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            // Reutilizar la funcionalidad del botón de configurar tablero
            configDashboardBtn.click();
        });
    }

    if (quickAddWidgetBtn) {
        quickAddWidgetBtn.addEventListener('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            // Reutilizar la funcionalidad del botón de añadir widget
            addWidgetBtn.click();
        });
    }

    // Evento para el botón Aceptar
    acceptDashboardSwitch.addEventListener('click', () => {
        if (selectedDashboardId) {
            dashboardManager.useDashboard(selectedDashboardId);
            // Resetear el ID del tablero seleccionado después de usarlo
            selectedDashboardId = null;
        }
        closePopup(switchDashboardPopup);
    });

    // Evento para el botón Cancelar
    cancelDashboardSwitch.addEventListener('click', () => {
        closePopup(switchDashboardPopup);
    });

    // El cierre al hacer clic fuera ahora se maneja en dialogs.js
    // Configurar el botón de cambio de tablero para usar el nuevo sistema
    switchDashboardBtn.dataset.toggleElement = 'switch-dashboard-popup';
    // También configurar el título del tablero que puede abrir este popup
    headerTitle.dataset.toggleElement = 'switch-dashboard-popup';

    // Botón de editar widget (si existe)
    const editWidgetBtn = document.getElementById('edit-widget-btn');
    if (editWidgetBtn) {
        editWidgetBtn.addEventListener('click', () => {
            dashboardManager.deactivateEditMode();
            dashboardManager.activateEditWidgetMode();
            closeMenuUniversal();
        });
    }

    // Botón de vaciar tablero (ahora en el popup)
    const clearDashboardBtn = document.getElementById('clear-dashboard-btn');
    clearDashboardBtn.addEventListener('click', () => {
        dialogManager.confirmDelete('¿Estás seguro de que deseas eliminar TODOS los widgets del tablero? Esta acción no se puede deshacer.').then(confirmed => {
            if (confirmed) {
                // Usar app o fallback a window
                const widgetMgr = app.widgetManager || window.widgetManager;
                widgetMgr.clearDashboard();

                // Renderizar el dashboard vacío
                const dashboardElement = document.getElementById('dashboard');
                if (dashboardElement) {
                    widgetMgr.renderWidgets(dashboardElement);
                }

                // Cerrar el popup de opciones
                closePopup(dashboardOptionsPopup);

                // Cerrar el modal de configuración del tablero
                closeModal(dashboardConfigModal);

                console.log("Tablero vaciado y modales cerrados");
            }
        });
    });

    // Botón de cerrar (X) en el popup de opciones de tablero
    if (closeDashboardOptionsBtn) {
        closeDashboardOptionsBtn.addEventListener('click', () => {
            closePopup(dashboardOptionsPopup);
        });
    }

    // Botón de cancelar en el popup de opciones de tablero
    if (cancelDashboardOptionsBtn) {
        cancelDashboardOptionsBtn.addEventListener('click', () => {
            closePopup(dashboardOptionsPopup);
        });
    }

    // Los event listeners para el modal de editar widget ahora se configuran
    // dentro de openWidgetEditDialog() para cada instancia clonada del diálogo





    // Los event listeners para los botones del diálogo de edición de widgets ahora se configuran
    // dentro de openWidgetEditDialog() para cada instancia clonada del diálogo

    // Configurar el modal de pedir nombre de tablero
    const dashboardAskNameModal = document.getElementById('dashboard-ask-name');
    const createDashboardBtn = document.getElementById('create-dashboard-btn');
    const newDashboardNameInput = document.getElementById('new-dashboard-name');
    const newDashboardError = document.getElementById('new-dashboard-error');

    // Inicializar el modal en modo creación por defecto
    dashboardAskNameModal.dataset.duplicateMode = 'false';
    dashboardAskNameModal.dataset.configModalOpen = 'false';

    // Evento para el botón Aceptar del modal de pedir nombre de tablero
    createDashboardBtn.addEventListener('click', () => {
        // Obtener el nombre del tablero
        const dashboardName = newDashboardNameInput.value.trim();

        // Validar que no esté vacío
        if (!dashboardName) {
            newDashboardError.textContent = 'El nombre del tablero no puede estar vacío.';
            return;
        }

        // Validar que no exista otro tablero con el mismo nombre normalizado
        if (dashboardNameExists(dashboardName)) {
            newDashboardError.textContent = 'Ya existe un tablero con ese nombre.';
            return;
        }

        // Verificar si estamos en modo duplicación o creación
        if (dashboardAskNameModal.dataset.duplicateMode === 'true') {
            // Duplicar el tablero actual
            dashboardManager.duplicateDashboard()
                .then(newId => {
                    // Cambiar al tablero duplicado
                    return dashboardManager.useDashboard(newId);
                })
                .then(() => {
                    // Actualizar el nombre del tablero duplicado
                    return dashboardManager.updateDashboardConfig({
                        name: dashboardName
                    });
                })
                .then(() => {
                    // Aplicar la configuración
                    dashboardManager.applyDashboardConfig();

                    // Renderizar el dashboard
                    const dashboardElement = document.getElementById('dashboard');
                    if (dashboardElement) {
                        widgetManager.renderWidgets(dashboardElement);
                    }

                    // Limpiar el flag de modo duplicación
                    dashboardAskNameModal.dataset.duplicateMode = 'false';

                    // Limpiar el flag de estado del modal de configuración
                    dashboardAskNameModal.dataset.configModalOpen = 'false';

                    // Cerrar el modal
                    closeModal(dashboardAskNameModal);
                })
                .catch(error => {
                    console.error('Error al duplicar tablero:', error);
                });

            // Salir de la función para evitar que se ejecute el código siguiente
            // ya que se manejará en las promesas
            return;
        } else {
            // Crear un nuevo tablero con el nombre proporcionado
            dashboardManager.createNewDashboard()
                .then(newId => {
                    // Cambiar al nuevo tablero
                    return dashboardManager.useDashboard(newId);
                })
                .then(() => {
                    // Actualizar el nombre del tablero
                    return dashboardManager.updateDashboardConfig({
                        name: dashboardName
                    });
                })
                .then(() => {
                    // Aplicar la configuración
                    dashboardManager.applyDashboardConfig();

                    // Renderizar el dashboard
                    const dashboardElement = document.getElementById('dashboard');
                    if (dashboardElement) {
                        widgetManager.renderWidgets(dashboardElement);
                    }

                    // Limpiar el flag de estado del modal de configuración
                    dashboardAskNameModal.dataset.configModalOpen = 'false';

                    // Cerrar el modal
                    closeModal(dashboardAskNameModal);
                })
                .catch(error => {
                    console.error('Error al crear nuevo tablero:', error);
                });

            // Salir de la función para evitar que se ejecute el código siguiente
            // ya que se manejará en las promesas
            return;
        }

        // Este código ya no se ejecutará porque ambas ramas (duplicación y creación)
        // tienen un return, pero lo dejamos como referencia de lo que se hacía antes
        // y por si en el futuro se añade una tercera opción que no tenga return.
    });

    // Evento para la tecla Enter en el campo de nombre
    newDashboardNameInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            // Aceptar con Enter
            createDashboardBtn.click();

            // Prevenir comportamiento por defecto
            e.preventDefault();
        }
    });

    // Limpiar mensaje de error al escribir
    newDashboardNameInput.addEventListener('input', () => {
        newDashboardError.textContent = '';
    });

    // Cerrar modales
    const closeButtons = document.querySelectorAll('.close-btn');
    closeButtons.forEach(button => {
        button.addEventListener('click', () => {
            const modal = button.closest('.modal');

            // Si es el modal de pedir nombre de tablero
            if (modal === dashboardAskNameModal) {
                // Limpiar el modo de duplicación
                dashboardAskNameModal.dataset.duplicateMode = 'false';

                // Si el modal de configuración estaba abierto, reabrirlo
                if (dashboardAskNameModal.dataset.configModalOpen === 'true') {
                    setTimeout(() => openModal('dashboard-config-modal'), 100);
                }

                // Limpiar el flag
                dashboardAskNameModal.dataset.configModalOpen = 'false';
            }

            closeModal(modal);
        });
    });

    // El cierre al hacer clic fuera ahora se maneja en dialogs.js
    // Configurar un manejador especial para el modal de pedir nombre de tablero

    // Guardar una referencia al manejador original para poder usarlo en nuestro manejador personalizado
    dashboardAskNameModal._specialCloseHandler = () => {
        // Limpiar el modo de duplicación
        dashboardAskNameModal.dataset.duplicateMode = 'false';

        // Si el modal de configuración estaba abierto, reabrirlo
        if (dashboardAskNameModal.dataset.configModalOpen === 'true') {
            setTimeout(() => openModal('dashboard-config-modal'), 100);
        }

        // Limpiar el flag
        dashboardAskNameModal.dataset.configModalOpen = 'false';

        // Cerrar el modal
        closeModal(dashboardAskNameModal);
    };

    // Desactivar modos de edición al hacer clic en el dashboard
    const dashboard = document.getElementById('dashboard');
    dashboard.addEventListener('click', (e) => {
        if (e.target === dashboard) {
            dashboardManager.deactivateEditMode();
        }
    });
}



/**
 * Normaliza un texto: convierte a mayúsculas, quita acentos, convierte Ñ a N y puntuación a espacios
 * @param {string} text - Texto a normalizar
 * @returns {string} - Texto normalizado
 */
function normalizeText(text) {
    if (!text) return '';

    // Convertir a mayúsculas
    let normalized = text.toUpperCase();

    // Reemplazar caracteres acentuados
    const accentMap = {
        'Á': 'A',
        'É': 'E',
        'Í': 'I',
        'Ó': 'O',
        'Ú': 'U',
        'À': 'A',
        'È': 'E',
        'Ì': 'I',
        'Ò': 'O',
        'Ù': 'U',
        'Ä': 'A',
        'Ë': 'E',
        'Ï': 'I',
        'Ö': 'O',
        'Ü': 'U',
        'Â': 'A',
        'Ê': 'E',
        'Î': 'I',
        'Ô': 'O',
        'Û': 'U',
        'Ñ': 'N'
    };

    for (const [accented, plain] of Object.entries(accentMap)) {
        normalized = normalized.replace(new RegExp(accented, 'g'), plain);
    }

    // Reemplazar caracteres de puntuación por espacios
    normalized = normalized.replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, ' ');

    // Reemplazar múltiples espacios por uno solo
    normalized = normalized.replace(/\s+/g, ' ').trim();

    return normalized;
}

/**
 * Verifica si ya existe un tablero con el mismo nombre normalizado
 * @param {string} name - Nombre a verificar
 * @returns {boolean} - true si ya existe un tablero con ese nombre, false en caso contrario
 */
function dashboardNameExists(name) {
    const normalizedName = normalizeText(name);
    return dashboardManager.dashboards.some(dashboard =>
        normalizeText(dashboard.name) === normalizedName
    );
}

// La función openWidgetEditDialog se ha movido a la clase DashboardManager en dashboard.js
function openWidgetEditDialog(widgetId) {
    // Redirigir a la función en dashboardManager
    dashboardManager.openWidgetEditDialog(widgetId);
}