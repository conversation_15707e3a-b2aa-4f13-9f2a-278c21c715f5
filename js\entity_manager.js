/**
 * Gestor genérico de entidades
 * Proporciona funcionalidad para listar, filtrar, ordenar y realizar operaciones CRUD en entidades
 */
class EntityManager {
    constructor() {
        this.entityType = null;
        this.entities = [];
        this.filteredEntities = [];
        this.currentSort = {
            field: 'id',
            direction: 'asc'
        };
        this.filterValue = '';
        this.entityConfig = {};
        this.entityHandlers = {};
        this.containerElement = null;
        this.initialized = false;

        // Registrar la instancia en el objeto global app
        if (!app.entityManagers) {
            app.entityManagers = [];
        }
        //NO hacer éso: entityManagers se crean muchas veces para el mismo tipo.
        //Los handlers se registran así al final del fichero que lo contiene:
        //app['entityManager'].registerEntityHandler('tipo entidad', new TipoQueSeaEntityHandler());
        //
        //  --> no hacer ésto app.entityManagers.push(this);
    }

    /** Mira si un handler tiene un rutina específica y lo devuelve. Si no, devuelve null */
    rutinaPropiaHandler(nom_rutina) {
        // Usar el manejador si está disponible
        const entityHandler = this.entityHandlers[this.entityType];
        if (entityHandler && entityHandler.nom_rutina) return entityHandler.nom_rutina.bind(this);
        return null;
    }

    /** Si hay una rutina propia en el handler de este tipo de entidad, la ejecuta con this a este EntityManager*/
    execRutinaPropia(nom_rutina) {
        let f = this.rutinaPropiaHandler(nom_rutina);
        if (f) return f();
        return null;
    }

    /**
     * Inicializa el gestor de entidades
     * @param {string} entityType - Tipo de entidad a gestionar ('usuarios', 'companies', etc.)
     * @param {Object} params - Parámetros adicionales para la inicialización
     * @returns {Promise} - Promesa que se resuelve con la instancia actual cuando se han cargado las entidades
     */
    init(entityType, params = {}) {
        this.entityType = entityType;
        this.params = params; // Guardar los parámetros para usarlos en loadEntities

        // Verificar si tenemos el handler para este tipo de entidad
        if (!this.entityHandlers[entityType] && app.entityManager && app.entityManager !== this) {
            // Si no lo tenemos, intentar obtenerlo de la instancia global
            const globalHandler = app.entityManager.entityHandlers[entityType];
            if (globalHandler) {
                // Si lo encontramos, copiarlo a nuestra instancia
                this.entityHandlers[entityType] = globalHandler;
                console.log(`Handler para '${entityType}' copiado desde la instancia global`);
            }
        }

        // Configurar la entidad según su tipo
        this.configureEntity();

        // Cargar las entidades y devolver la promesa
        return this.loadEntities()
            .then(() => {
                // No configuramos la UI aquí, eso lo hará show()
                return this; // Devolver this para encadenar métodos
            })
            .catch(error => {
                console.error(`Error al inicializar el gestor de entidades (${entityType}):`, error);
                showNotification(`Error al cargar los datos: ${error.message}`, 'error');
                // Re-lanzar el error para que pueda ser capturado por quien llama a init()
                throw error;
            });
    }

    /**
     * Muestra la pantalla de gestión de entidades
     * Clona el contenedor de entidades y lo configura
     */
    show() {
        // Clonar el contenedor de entidades
        const originalContainer = document.getElementById('entity-management-container');
        if (!originalContainer) {
            console.error('No se encontró el contenedor de entidades');
            return this;
        }

        // Crear un nuevo contenedor clonando el original
        this.containerElement = originalContainer.cloneNode(true);

        // Generar un ID único para el nuevo contenedor
        const uniqueId = `entity-management-container-${this.entityType}-${Date.now()}`;
        this.containerElement.id = uniqueId;

        // Actualizar el título con el tipo de entidad
        const titleElement = this.containerElement.querySelector('#entity-title #titulo');
        if (titleElement && this.entityConfig && this.entityConfig.title) {
            titleElement.textContent = this.entityConfig.title;
            //titleElement.id = `entity-title-${uniqueId}`;
        }

        // Añadir el contenedor al DOM
        document.body.appendChild(this.containerElement);
        showContainer(this.containerElement, this);

        // Configurar la interfaz de usuario y renderizar la tabla
        this.setupUI();
        this.renderEntityTable();
        initNavigationButtons(this.containerElement);

        // Actualizar el hash de la URL según el tipo de entidad
        this.updateUrlHash();

        return this;
    }

    /**
     * Actualiza el hash de la URL según el tipo de entidad actual
     */
    updateUrlHash() {
        // Crear objeto de parámetros con el tipo de entidad
        const params = {
            entityType: this.entityType
        };

        // Añadir parámetros adicionales si existen
        if (this.params) {
            // Copiar los parámetros adicionales al objeto de parámetros
            Object.assign(params, this.params);

            // Registrar en consola los parámetros para depuración
            console.log(`EntityManager.updateUrlHash: Parámetros para ${this.entityType}:`, this.params);
        }

        window.updateUrlHash('entity-management', params);
    }

    /**
     * Actualiza el título de la entidad en la interfaz de usuario
     * @param {string} title - Nuevo título a mostrar
     */
    updateTitle(title) {
        if (!this.containerElement) return;

        // Actualizar el título en el elemento correspondiente
        const titleElement = this.containerElement.querySelector('#entity-title #titulo');
        if (titleElement) {
            titleElement.textContent = title;
        }
    }

    /**
     * Crea un nuevo tablero vacío y lo muestra
     * Este método se llama cuando no hay tableros y el usuario hace clic en el botón "Crear nuevo tablero"
     */
    createNewEmptyDashboard() {
        // Verificar que estamos en la entidad de tableros
        if (this.entityType !== 'tableros') return;

        // Obtener el usuario actual y la empresa
        const currentUser = authManager.user;
        const currentCompanyId = authManager.companyId;

        // Obtener el ID de empresa de los parámetros o usar el del usuario actual
        const specificCompanyId = this.params && this.params.companyId;
        const specificUserId = this.params && this.params.userId;
        const companyIdToUse = specificCompanyId || currentCompanyId;

        // Determinar el usuario al que asociar el tablero
        // Si estamos viendo los tableros de un usuario específico, asociar el tablero a ese usuario
        // Si no, asociar al usuario actual de la aplicación
        const userIdToUse = specificUserId || currentUser.id;
        let userNameToUse = currentUser.nombre;

        // Si hay un ID de usuario específico, intentar obtener su nombre
        if (specificUserId && specificUserId !== currentUser.id) {
            // Buscar el usuario en la base de datos para obtener su nombre
            dbManager.getUsers(companyIdToUse)
                .then(users => {
                    const user = users.find(u => u.id === specificUserId);
                    if (user) {
                        userNameToUse = user.nombre;
                    }
                })
                .catch(error => {
                    console.error('Error al obtener nombre de usuario:', error);
                });
        }

        // Crear un nuevo tablero básico
        const now = new Date().toISOString();
        const loggedUser = authManager.user;
        const newDashboard = {
            id: 1, // Se actualizará con el siguiente ID disponible
            name: 'Tablero 1',
            width: 800,
            height: 600,
            backgroundColor: '#ffffff',
            showWidgetBorders: true,
            transparentWidgets: false,
            showGrid: true,
            gridColor: '#1a3a5a',
            widgetCount: 0,
            theme: themeManager.getCurrentTheme(),
            widgets: [],
            observ: '',
            activo: true,
            userId: userIdToUse,
            userName: userNameToUse,
            empresaId: companyIdToUse,
            // Campos de auditoría
            fechaAlta: now,
            fechaModificacion: now,
            usuarioAltaId: loggedUser.id,
            usuarioModificacionId: loggedUser.id
        };

        // Obtener el siguiente ID disponible
        dbManager.getDashboards(companyIdToUse)
            .then(dashboards => {
                // Calcular el siguiente ID
                const maxId = dashboards.length > 0
                    ? Math.max(...dashboards.map(d => d.id)) + 1
                    : 1;

                newDashboard.id = maxId;
                newDashboard.name = `Tablero ${maxId}`;

                // Guardar el nuevo tablero
                return dbManager.saveDashboard(newDashboard, companyIdToUse);
            })
            .then(() => {
                // Cerrar el contenedor actual antes de abrir el tablero
                const currentElement = peekUIStack();
                if (currentElement && currentElement.element) {
                    // Cerrar el contenedor actual sin animación para evitar problemas
                    currentElement.element.classList.add('hidden');
                    popFromUIStack(true);
                }

                // Limpiar cualquier clase que pueda interferir con los eventos del ratón
                document.body.classList.remove('selection-mode');

                // Buscar el handler de tableros
                const entityHandler = this.entityHandlers[this.entityType];
                if (entityHandler && entityHandler.openDashboard) {
                    // Abrir el tablero recién creado
                    entityHandler.openDashboard(newDashboard);
                } else {
                    // Si no se puede abrir directamente, recargar la lista
                    this.loadEntities().then(() => this.renderEntityTable());
                }
            })
            .catch(error => {
                console.error('Error al crear nuevo tablero:', error);
                showNotification(`Error al crear tablero: ${error.message}`, 'error');
            });
    }

    /**
     * Configura la entidad según su tipo
     */
    configureEntity() {
        // Configuración por defecto
        this.entityConfig = {
            title: 'Entidades',
            fields: [{
                name: 'id',
                label: 'ID',
                type: 'number',
                sortable: true,
                visible: true,
                minWidth: null // Ancho mínimo de la columna (null = sin ancho mínimo)
            }],
            actions: ['edit', 'delete']
        };

        // Obtener la configuración específica del tipo de entidad
        const entityHandler = this.entityHandlers[this.entityType];
        if (entityHandler && entityHandler.getConfig) {
            const specificConfig = entityHandler.getConfig();
            this.entityConfig = {
                ...this.entityConfig,
                ...specificConfig
            };
            let local_config = localStorage.getItem('ipram_' + this.entityType + '_cols');
            if (local_config) {
                local_config = JSON.parse(local_config);
                // Obtener los nombres de los campos en ambos arrays
                const entityNames = new Set(this.entityConfig.fields.map(f => f.name));
                const localNames = new Set(local_config.map(f => f.name));

                // Añadir campos que están en `this.entityConfig.fields` pero no en `local_config`
                this.entityConfig.fields.forEach(field => {
                    if (!localNames.has(field.name)) {
                        local_config.push(field);
                    }
                });

                // Eliminar campos que están en `local_config` pero no en `this.entityConfig.fields`
                local_config = local_config.filter(f => entityNames.has(f.name));
                this.entityConfig.fields = local_config;
            }
        }
    }

    /**
     * Registra un manejador para un tipo de entidad específico
     * @param {string} entityType - Tipo de entidad
     * @param {Object} handler - Objeto con métodos para manejar la entidad
     */
    registerEntityHandler(entityType, handler) {
        this.entityHandlers[entityType] = handler;

        // Registrar también en el sistema de navegación por hash si está disponible
        if (typeof registerEntityHash === 'function') {
            // Convertir el tipo de entidad a un nombre de hash adecuado
            let hashName;
            switch (entityType) {
                case 'companies':
                    hashName = 'empresas';
                    break;
                case 'usuarios':
                    hashName = 'usuarios';
                    break;
                default:
                    // Para otros tipos, usar el mismo nombre
                    hashName = entityType;
            }

            // Registrar en el sistema de navegación por hash
            registerEntityHash(hashName, entityType);
        }
    }

    /**
     * Carga las entidades desde la base de datos
     * @returns {Promise} - Promesa que se resuelve cuando se han cargado las entidades
     */
    loadEntities() {
        return new Promise((resolve, reject) => {
            if (!this.entityType) {
                reject(new Error('Tipo de entidad no especificado'));
                return;
            }

            // Usar el manejador si está disponible
            const entityHandler = this.entityHandlers[this.entityType];
            if (entityHandler && entityHandler.loadEntities) {
                entityHandler.loadEntities(this)
                    .then(entities => {
                        this.entities = entities;
                        this.applySort();
                        this._applyFilter();
                        resolve();
                    })
                    .catch(error => reject(error));
            } else {
                reject(new Error(`No hay un manejador registrado para el tipo de entidad: ${this.entityType}`));
            }
        });
    }

    /** Refresca la pantalla. Esta rutina es para ser llamada cuando se vuelve de una pantalla
     * a la lista de registros de una entidad.
     */
    refresh() {
        this.loadEntities().then(() => {
            if (this.execRutinaPropia('refresh')) return;
            this.renderEntityTable();

        });
    }

    /**
     * Configura los eventos de la interfaz de usuario
     */
    setupUI() {
        if (!this.containerElement) return;

        // Configurar el filtro
        const filterInput = this.containerElement.querySelector('.entity-filter');
        const clearFilterBtn = this.containerElement.querySelector('.clear-filter-btn');

        if (filterInput) {
            filterInput.value = this.filterValue;
            filterInput.addEventListener('input', () => {
                this.filterValue = filterInput.value;
                this.applyFilter();

                // Mostrar/ocultar el botón de limpiar filtro
                if (clearFilterBtn) {
                    clearFilterBtn.classList.toggle('hidden', !this.filterValue);
                }
            });
        }

        if (clearFilterBtn) {
            clearFilterBtn.classList.toggle('hidden', !this.filterValue);
            clearFilterBtn.addEventListener('click', () => {
                if (filterInput) {
                    filterInput.value = '';
                    this.filterValue = '';
                    this.applyFilter();
                    clearFilterBtn.classList.add('hidden');
                }
            });
        }

        // Configurar menú móvil
        const mobileMenuBtn = this.containerElement.querySelector('#entity-mobile-menu-btn, [id^="entity-mobile-menu-btn-"]');
        const optionsMenu = this.containerElement.querySelector('#entity-options-menu, [id^="entity-options-menu-"]');

        if (mobileMenuBtn && optionsMenu) {
            // Función para abrir el menú
            const openOptionsMenu = () => {
                // Si el menú ya está visible, no hacer nada
                if (!optionsMenu.classList.contains('hidden')) {
                    return;
                }

                // Quitar la clase hidden para mostrar el menú
                optionsMenu.classList.remove('hidden');

                // Animar el icono de hamburguesa
                const hamburgerIcon = mobileMenuBtn.querySelector('.hamburger-icon');
                if (hamburgerIcon) {
                    hamburgerIcon.classList.remove('menu-closing');
                    hamburgerIcon.classList.add('menu-opening');
                }

                // Configurar cierre al hacer clic fuera
                setTimeout(() => {
                    document.addEventListener('click', handleClickOutside);
                    document.addEventListener('touchstart', handleClickOutside);
                }, 10);
            };

            // Función para cerrar el menú
            const closeOptionsMenu = () => {
                // Si el menú ya está oculto, no hacer nada
                if (optionsMenu.classList.contains('hidden')) {
                    return;
                }

                // Ocultar el menú
                optionsMenu.classList.add('hidden');

                // Animar el icono de hamburguesa
                const hamburgerIcon = mobileMenuBtn.querySelector('.hamburger-icon');
                if (hamburgerIcon) {
                    hamburgerIcon.classList.remove('menu-opening');
                    hamburgerIcon.classList.add('menu-closing');
                }

                // Eliminar los event listeners
                document.removeEventListener('click', handleClickOutside);
                document.removeEventListener('touchstart', handleClickOutside);
            };

            // Función para manejar clics fuera del menú
            const handleClickOutside = (e) => {
                // Si el clic no es dentro del menú y no es en el botón que lo abrió
                if (!optionsMenu.contains(e.target) && e.target !== mobileMenuBtn && !mobileMenuBtn.contains(e.target)) {
                    closeOptionsMenu();
                }
            };

            // Evento para alternar el menú al hacer clic en el botón
            mobileMenuBtn.addEventListener('click', (e) => {
                e.stopPropagation(); // Evitar que el clic se propague al documento

                if (optionsMenu.classList.contains('hidden')) {
                    openOptionsMenu();
                } else {
                    closeOptionsMenu();
                }
            });
        }
    }

    /**
     * Cierra la pantalla de gestión de entidades con animación
     */
    close() {
        //Vamos a hacer que closeContainer sea quien decida  siempre a quien devolver el control,
        //que de momento es siempre a la pantalla anterior a la que llamó a esta pantalla
        //excepto si no hay, en cuyo caso va a la pantalla ppal

        // Cerrar el contenedor con animación
        // popFromUIStack se encargará de eliminar el nodo del DOM
        closeContainer(this.containerElement, true);
    }


    /** Aplica filtros y refresca la tabla */
    applyFilter() {
        this._applyFilter();
        this.renderEntityTable();
    }
    /**
     * Aplica el filtro actual a las entidades. Función de uso interno y de apoyo para SOLO filtrar
     */
    _applyFilter() {
        if (!this.filterValue) {
            this.filteredEntities = [...this.entities];
            return;
        }

        // Separar por espacios en blanco (AND)
        const andTerms = this.filterValue.trim().split(/\s+/);

        this.filteredEntities = this.entities.filter(entity => {
            // Cada término separado por espacios debe coincidir (AND)
            return andTerms.every(term => {
                // Separar por punto y coma (OR)
                const orTerms = term.split(';');

                return orTerms.some(orTerm => {
                    // Comprobar si es un rango (N..M)
                    const rangeMatch = orTerm.match(/^(\d+)\.\.(\d+)$/);

                    if (rangeMatch) {
                        const min = parseInt(rangeMatch[1], 10);
                        const max = parseInt(rangeMatch[2], 10);

                        // Buscar en campos numéricos (excepto passwords)
                        return this.entityConfig.fields.some(field => {
                            if (!field.visible || field.type === 'password') return false;

                            const value = entity[field.name];
                            if (value === null || value === undefined) return false;

                            // Solo aplicar a campos numéricos
                            if (typeof value === 'number') {
                                return value >= min && value <= max;
                            }

                            return false;
                        });
                    } else {
                        // Búsqueda normal por texto
                        const normalizedTerm = normalizeText(orTerm);

                        // Buscar en todos los campos visibles (excepto passwords)
                        return this.entityConfig.fields.some(field => {
                            if (!field.visible || field.type === 'password') return false;

                            const value = entity[field.name];
                            if (value === null || value === undefined) return false;

                            const normalizedValue = normalizeText(String(value));
                            return normalizedValue.includes(normalizedTerm);
                        });
                    }
                });
            });
        });
    }

    /**
     * Aplica la ordenación actual a las entidades filtradas
     */
    applySort() {
        const {
            field,
            direction
        } = this.currentSort;

        this.filteredEntities.sort((a, b) => {
            let valueA = a[field];
            let valueB = b[field];

            // Convertir a string para comparación si no son números
            if (typeof valueA !== 'number') valueA = String(valueA || '').toLowerCase();
            if (typeof valueB !== 'number') valueB = String(valueB || '').toLowerCase();

            if (valueA < valueB) return direction === 'asc' ? -1 : 1;
            if (valueA > valueB) return direction === 'asc' ? 1 : -1;
            return 0;
        });
    }

    /**
     * Cambia la ordenación de la tabla
     * @param {string} field - Campo por el que ordenar
     */
    changeSort(field) {
        if (this.currentSort.field === field) {
            // Cambiar dirección si es el mismo campo
            this.currentSort.direction = this.currentSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
            // Nuevo campo, ordenar ascendente
            this.currentSort = {
                field,
                direction: 'asc'
            };
        }

        this.applySort();
        this.renderEntityTable();
    }

    /** Muestra la tabla recordando la posición actual del scroll. Se usa para refrescar la tabla al editar registros o al volver de otra pantalla. */
    renderEntityTable() {
        const tableContainer = this.containerElement.querySelector('.entity-table-container');
        let scrollTop = tableContainer.scrollTop,
            scrollLeft = tableContainer.scrollLeft;

        this._renderEntityTable();

        tableContainer.scrollTop = scrollTop;
        tableContainer.scrollLeft = scrollLeft;
    }
    /**
     * Rutina auxiliar intera para renderiza la tabla de entidades. No hace nada más.
     */
    _renderEntityTable() {
        if (!this.containerElement) return;

        const tableContainer = this.containerElement.querySelector('.entity-table-container');
        if (!tableContainer) return;

        // Actualizar contador de entidades
        const countElement = this.containerElement.querySelector('.entity-count');
        if (countElement) {
            countElement.textContent = `(${this.filteredEntities.length} de ${this.entities.length})`;
        }

        // Crear tabla
        const table = document.createElement('table');
        table.className = 'entity-table';

        // Crear encabezado
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');

        // Añadir columna de selección (checkbox)
        const selectAllHeader = document.createElement('th');
        selectAllHeader.className = 'checkbox-cell';
        const selectAllCheckbox = document.createElement('input');
        selectAllCheckbox.type = 'checkbox';
        selectAllCheckbox.id = 'select-all-checkbox';
        selectAllCheckbox.addEventListener('change', () => {
            // Seleccionar/deseleccionar todos los checkboxes
            const checkboxes = table.querySelectorAll('tbody input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
        });
        selectAllHeader.appendChild(selectAllCheckbox);
        headerRow.appendChild(selectAllHeader);

        // Añadir columna de acciones
        const actionsHeader = document.createElement('th');
        actionsHeader.className = 'action-cell';

        // Botón de acciones para la tabla
        const tableActionButton = document.createElement('button');
        tableActionButton.className = 'action-button';
        tableActionButton.innerHTML = '&#8230;'; // Tres puntos horizontales
        tableActionButton.title = 'Acciones de tabla';
        tableActionButton.addEventListener('click', (e) => {
            e.stopPropagation();
            this.showTableActionMenu(e.target);
        });

        actionsHeader.appendChild(tableActionButton);
        headerRow.appendChild(actionsHeader);

        // Añadir columnas según la configuración
        this.entityConfig.fields.forEach(field => {
            // No mostrar campos ocultos ni campos tipo password
            if (!field.visible || field.type === 'password') return;

            const th = document.createElement('th');
            th.textContent = field.label;

            // Aplicar ancho mínimo si está definido
            if (field.minWidth) {
                th.style.minWidth = `${field.minWidth}px`;
            }

            if (field.sortable) {
                th.classList.add('sortable');
                if (this.currentSort.field === field.name) {
                    th.classList.add(this.currentSort.direction === 'asc' ? 'sort-asc' : 'sort-desc');
                }

                th.addEventListener('click', () => {
                    this.changeSort(field.name);
                });
            }

            headerRow.appendChild(th);
        });

        thead.appendChild(headerRow);
        table.appendChild(thead);

        // Crear cuerpo de la tabla
        const tbody = document.createElement('tbody');

        if (this.filteredEntities.length === 0) {
            // Mostrar mensaje si no hay entidades
            const emptyRow = document.createElement('tr');
            const emptyCell = document.createElement('td');
            emptyCell.colSpan = this.entityConfig.fields.filter(f => f.visible).length + 2; // +2 por las columnas de checkbox y acciones
            emptyCell.style.textAlign = 'center';
            emptyCell.style.padding = '20px';

            // Crear contenedor para mensaje y botón
            const container = document.createElement('div');
            container.style.display = 'flex';
            container.style.flexDirection = 'column';
            container.style.alignItems = 'center';
            container.style.gap = '15px';

            // Añadir mensaje
            const messageDiv = document.createElement('div');
            messageDiv.textContent = 'No hay datos para mostrar';
            container.appendChild(messageDiv);

            // Si es la entidad 'tableros', añadir botón para crear nuevo tablero
            if (this.entityType === 'tableros') {
                const createButton = document.createElement('button');
                createButton.className = 'btn btn-primary';
                createButton.textContent = 'Crear nuevo tablero';
                createButton.onclick = () => this.createNewEmptyDashboard();
                container.appendChild(createButton);
            }

            emptyCell.appendChild(container);
            emptyRow.appendChild(emptyCell);
            tbody.appendChild(emptyRow);
        } else {
            // Añadir filas con datos
            this.filteredEntities.forEach((entity, index) => {
                const row = document.createElement('tr');
                row.dataset.ndx = index;
                row.dataset.accion2 = 'handleRowDoubleClick';
                // Añadir celda de selección (checkbox)
                const checkboxCell = document.createElement('td');
                checkboxCell.className = 'checkbox-cell';
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.dataset.entityId = entity.id;
                checkbox.addEventListener('change', () => {
                    // Verificar si todos los checkboxes están marcados
                    const allCheckboxes = tbody.querySelectorAll('input[type="checkbox"]');
                    const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
                    const anyChecked = Array.from(allCheckboxes).some(cb => cb.checked);

                    // Actualizar el checkbox de seleccionar todos
                    const selectAllCheckbox = table.querySelector('#select-all-checkbox');
                    if (selectAllCheckbox) {
                        selectAllCheckbox.checked = allChecked;
                        selectAllCheckbox.indeterminate = anyChecked && !allChecked;
                    }
                });
                checkboxCell.appendChild(checkbox);
                row.appendChild(checkboxCell);

                // Añadir celda de acciones
                const actionsCell = document.createElement('td');
                actionsCell.className = 'action-cell';

                // Botón de acciones para la fila
                const actionButton = document.createElement('button');
                actionButton.className = 'action-button';
                actionButton.innerHTML = '&#8230;'; // Tres puntos horizontales
                actionButton.title = 'Acciones';
                actionButton.dataset.accion = 'showActionMenu'
                actionButton.dataset.ndx = index;
                actionsCell.appendChild(actionButton);
                row.appendChild(actionsCell);

                // Añadir celdas con datos
                this.entityConfig.fields.forEach(field => {
                    // No mostrar campos ocultos ni campos tipo password
                    if (!field.visible || field.type === 'password') return;

                    const cell = document.createElement('td');
                    let value = entity[field.name];

                    // Formatear valor según el tipo
                    if (field.type === 'date') {
                        value = formatDate(value);
                    } else if (field.type === 'boolean' || field.type === 'checkbox') {
                        value = value ? 'Sí' : 'No';
                    }

                    cell.textContent = value !== undefined && value !== null ? value : '';

                    // Aplicar ancho mínimo si está definido
                    if (field.minWidth) {
                        cell.style.minWidth = `${field.minWidth}px`;
                    }

                    row.appendChild(cell);
                });

                /* TO DELETE
                // Añadir evento para manejar el doble clic en la fila
                 row.addEventListener('dblclick', () => {
                     // Verificar si hay un manejador específico para el doble clic
                     if (this.entityHandlers[this.entityType] && this.entityHandlers[this.entityType].handleRowDoubleClick) {
                         // Usar el manejador específico
                         this.entityHandlers[this.entityType].handleRowDoubleClick(entity);
                     } else if (this.entityConfig.actions.includes('edit')) {
                         // Comportamiento por defecto: editar la entidad
                         this.editEntity(entity);
                     }
                 });*/

                tbody.appendChild(row);
            });
        }

        table.appendChild(tbody);

        // Limpiar y añadir la tabla al contenedor
        tableContainer.innerHTML = '';
        tableContainer.appendChild(table);
    }

    /** Doble clic en una fila */
    handleRowDoubleClick(ev) {
        let entity = this.event_to_entity(ev);
        // Verificar si hay un manejador específico para el doble clic
        if (this.entityHandlers[this.entityType] && this.entityHandlers[this.entityType].handleRowDoubleClick) {
            // Usar el manejador específico
            this.entityHandlers[this.entityType].handleRowDoubleClick(entity);
        } else if (this.entityConfig.actions.includes('edit')) {
            // Comportamiento por defecto: editar la entidad
            this.editEntity(entity);
        }
    }

    /**
     * Muestra el menú de acciones para la tabla
     * @param {HTMLElement} targetElement - Elemento que disparó la acción
     */
    showTableActionMenu(targetElement) {
        // Eliminar menús de acciones existentes
        const existingMenus = document.querySelectorAll('.entity-action-menu');
        existingMenus.forEach(menu => menu.remove());

        // Crear menú de acciones
        const actionMenu = document.createElement('div');
        actionMenu.className = 'entity-action-menu remove_on_click';

        // Verificar si se debe mostrar la opción para crear nuevo registro
        const showAddButton = !this.entityHandlers[this.entityType].verifPrivilegies ||
            this.entityHandlers[this.entityType].verifPrivilegies('addRecord');

        // Añadir opción para crear nuevo registro si tiene privilegios o no hay verificación
        if (showAddButton) {
            uhtml.crearBoton({
                text: 'Nuevo registro',
                accion: 'showEntityForm'
            }, actionMenu);
        }

        // Añadir opción para exportar a CSV
        uhtml.crearBoton({
            text: 'Exportar a CSV',
            accion: 'exportToCSV'
        }, actionMenu);

        // Añadir opción para configurar columnas
        uhtml.crearBoton({
            text: 'Configurar columnas',
            accion: 'showColumnConfig'
        }, actionMenu);

        // Añadir opciones específicas de la entidad si existen
        if (this.entityHandlers[this.entityType].getTableMenuOptions) {
            const specificOptions = this.entityHandlers[this.entityType].getTableMenuOptions();
            if (specificOptions && specificOptions.length > 0) {
                specificOptions.forEach(option => {
                    const button = document.createElement('button');
                    button.textContent = option.label;
                    button.dataset.action = option.action;
                    if (option.className) {
                        button.className = option.className;
                    }
                    /*TO DELETE
                    button.addEventListener('click', () => {
                        actionMenu.remove();
                        if (option.action) {
                            option.action();
                        }
                    });*/
                    actionMenu.appendChild(button);
                });
            }
        }

        // Posicionar el menú
        const rect = targetElement.getBoundingClientRect();
        actionMenu.style.top = `${rect.bottom + window.scrollY}px`;
        actionMenu.style.left = `${rect.left + window.scrollX}px`;

        // Añadir el menú al documento
        document.body.appendChild(actionMenu);

        // Función para cerrar el menú
        const closeActionMenu = () => {
            if (document.body.contains(actionMenu)) {
                actionMenu.remove();
            }
            // Asegurarse de eliminar el listener
            document.removeEventListener('click', handleClickOutside);
            document.removeEventListener('touchstart', handleClickOutside);
        };

        // Función para manejar clics fuera del menú
        const handleClickOutside = (e) => {
            // Si el clic no es dentro del menú y no es en el elemento que lo abrió
            if (!actionMenu.contains(e.target) && e.target !== targetElement) {
                closeActionMenu();
            }
        };

        // Usar setTimeout para evitar que el evento actual cierre el menú inmediatamente
        setTimeout(() => {
            document.addEventListener('click', handleClickOutside);
            // Añadir soporte para eventos táctiles
            document.addEventListener('touchstart', handleClickOutside);
        }, 10);
    }

    /**
     * Exporta la tabla actual a un archivo CSV
     */
    exportToCSV() {
        // Obtener solo las columnas visibles y excluir campos tipo password
        const visibleFields = this.entityConfig.fields.filter(field =>
            field.visible && field.type !== 'password'
        );

        if (visibleFields.length === 0 || this.filteredEntities.length === 0) {
            showNotification('No hay datos para exportar', 'warning');
            return;
        }

        // Crear cabecera del CSV
        let csv = visibleFields.map(field => `"${field.label}"`).join(',') + '\n';

        // Añadir filas
        this.filteredEntities.forEach(entity => {
            const row = visibleFields.map(field => {
                let value = entity[field.name];

                // Formatear valor según el tipo
                if (field.type === 'date') {
                    value = formatDate(value);
                } else if (field.type === 'boolean' || field.type === 'checkbox') {
                    value = value ? 'Sí' : 'No';
                }

                // Escapar comillas dobles y encerrar en comillas
                return `"${String(value !== undefined && value !== null ? value : '').replace(/"/g, '""')}"`;
            }).join(',');

            csv += row + '\n';
        });

        // Crear blob y descargar
        const blob = new Blob([csv], {
            type: 'text/csv;charset=utf-8;'
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', `${this.entityConfig.title}_${new Date().toISOString().slice(0, 10)}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showNotification('Datos exportados correctamente', 'success');
    }

    /**
     * Muestra el popup para configurar las columnas de la tabla
     */
    showColumnConfig() {
        // Crear modal para configurar columnas
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.id = 'column-config-modal';

        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';
        modalContent.style.width = '430px'; // Ancho fijo de 430px
        /*
                // Asegurar que el modal quepa en la pantalla
                const checkModalFit = () => {
                    const windowHeight = window.innerHeight;
                    const windowWidth = window.innerWidth;
                    const isLandscape = windowWidth > windowHeight;

                    // En móvil horizontal, ajustar altura al 100% de la ventana
                    if (isLandscape && windowWidth <= 768) {
                        modalContent.style.height = `${windowHeight}px`;
                        modalContent.style.maxHeight = `${windowHeight}px`;
                    } else {
                        // En móvil vertical y ordenador, asegurar que quepa en pantalla
                        modalContent.style.maxHeight = `${windowHeight - 20}px`;
                    }
                };

                // Ejecutar al crear y cuando cambie el tamaño de la ventana
                checkModalFit();
                window.addEventListener('resize', checkModalFit);
                */

        // Añadir título y botón de cierre
        const header = document.createElement('div');
        header.className = 'modal-header';
        header.style.display = 'flex';
        header.style.justifyContent = 'space-between';
        header.style.alignItems = 'center';
        header.style.paddingTop = '5px'; // Reducir espacio superior

        const title = document.createElement('h2');
        title.textContent = 'Configurar columnas';
        title.style.margin = '0';

        const closeBtn = document.createElement('span');
        closeBtn.className = 'close-btn';
        closeBtn.innerHTML = '&times;';
        closeBtn.style.float = 'none'; // Quitar float para alineación correcta
        closeBtn.style.lineHeight = '1'; // Ajustar alineación vertical
        closeBtn.style.display = 'flex';
        closeBtn.style.alignItems = 'center';
        closeBtn.style.justifyContent = 'center';

        // Función para cerrar el modal y limpiar todos los event listeners
        const handleClose = () => {
            // Limpiar todos los event listeners
            window.removeEventListener('resize', checkModalFit);
            window.removeEventListener('resize', updateColumnsHeight);

            // Usar la función global closeModal que maneja la animación y la pila UI
            closeModal(modal);
        };

        closeBtn.addEventListener('click', handleClose);

        header.appendChild(title);
        header.appendChild(closeBtn);
        modalContent.appendChild(header);

        // Contenedor para la lista de columnas
        const columnsContainer = document.createElement('div');
        columnsContainer.className = 'columns-container';
        columnsContainer.style.overflowY = 'auto';
        columnsContainer.style.overflowX = 'hidden';

        // Ajustar la altura del contenedor de columnas dinámicamente
        const updateColumnsHeight = () => {
            const windowHeight = window.innerHeight;
            const headerHeight = header.offsetHeight || 50; // Altura estimada si aún no está en el DOM
            const buttonGroupHeight = 60; // Altura estimada del grupo de botones
            const padding = 40; // Padding adicional para márgenes

            // Calcular altura disponible
            let availableHeight = windowHeight - headerHeight - buttonGroupHeight - padding;

            // Limitar a un mínimo razonable
            availableHeight = Math.max(availableHeight, 150);

            columnsContainer.style.maxHeight = `${availableHeight}px`;
        };

        // Actualizar altura cuando cambie el tamaño de la ventana
        window.addEventListener('resize', updateColumnsHeight);

        // Llamar inicialmente después de un breve retraso para asegurar que los elementos estén en el DOM
        setTimeout(updateColumnsHeight, 0);

        // Crear lista de columnas (excluyendo las dos primeras columnas fijas y los campos tipo password)
        this.entityConfig.fields.forEach(field => {
            // No mostrar campos tipo password en la configuración de columnas
            if (field.type === 'password') return;

            const columnItem = document.createElement('div');
            columnItem.className = 'column-item';
            columnItem.dataset.fieldName = field.name;

            // Permitir que los elementos se envuelvan si es necesario
            columnItem.style.flexWrap = 'wrap';
            columnItem.style.rowGap = '5px';

            // Icono para reordenar
            const dragHandle = document.createElement('span');
            dragHandle.className = 'drag-handle';
            dragHandle.innerHTML = '&#8597;'; // Icono de flechas arriba/abajo
            dragHandle.title = 'Arrastrar para reordenar';

            // Checkbox para visibilidad
            const visibilityCheck = document.createElement('input');
            visibilityCheck.type = 'checkbox';
            visibilityCheck.checked = field.visible;
            visibilityCheck.id = `visibility-${field.name}`;

            // Etiqueta para el nombre de la columna
            const label = document.createElement('label');
            label.setAttribute('for', `visibility-${field.name}`);
            label.textContent = field.label;
            label.style.wordBreak = 'break-word'; // Permitir que el texto se rompa
            label.style.whiteSpace = 'normal'; // Permitir múltiples líneas
            label.style.flex = '1'; // Tomar el espacio disponible

            // Input para el ancho mínimo
            const minWidthContainer = document.createElement('div');
            minWidthContainer.style.marginLeft = 'auto';
            minWidthContainer.style.display = 'flex';
            minWidthContainer.style.alignItems = 'center';

            const minWidthLabel = document.createElement('span');
            minWidthLabel.textContent = 'Ancho:';
            minWidthLabel.style.marginRight = '5px';
            minWidthLabel.style.fontSize = '12px';

            const minWidthInput = document.createElement('input');
            minWidthInput.type = 'number';
            minWidthInput.min = '0';
            minWidthInput.step = '10';
            minWidthInput.style.width = '60px';
            minWidthInput.value = field.minWidth || '';
            minWidthInput.placeholder = 'Auto';

            minWidthContainer.appendChild(minWidthLabel);
            minWidthContainer.appendChild(minWidthInput);

            columnItem.appendChild(dragHandle);
            columnItem.appendChild(visibilityCheck);
            columnItem.appendChild(label);
            columnItem.appendChild(minWidthContainer);

            columnsContainer.appendChild(columnItem);
        });

        modalContent.appendChild(columnsContainer);

        // Botones de acción
        const buttonGroup = document.createElement('div');
        buttonGroup.className = 'button-group';

        const acceptBtn = document.createElement('button');
        acceptBtn.className = 'primary-btn';
        acceptBtn.textContent = 'Aceptar';
        acceptBtn.addEventListener('click', () => {
            // Actualizar configuración de columnas
            const columnItems = columnsContainer.querySelectorAll('.column-item');
            const newOrder = [];

            columnItems.forEach(item => {
                const fieldName = item.dataset.fieldName;
                const isVisible = item.querySelector('input[type="checkbox"]').checked;
                const minWidthInput = item.querySelector('input[type="number"]');
                const minWidth = minWidthInput && minWidthInput.value ? parseInt(minWidthInput.value) : null;

                // Encontrar el campo en la configuración actual
                const field = this.entityConfig.fields.find(f => f.name === fieldName);
                if (field) {
                    field.visible = isVisible;
                    field.minWidth = minWidth;
                    newOrder.push(field);
                }
            });

            // Reordenar campos según el nuevo orden
            this.entityConfig.fields = newOrder;

            localStorage.setItem('ipram_' + this.entityType + '_cols', JSON.stringify(newOrder));

            // Renderizar tabla con la nueva configuración
            this.renderEntityTable();

            // Cerrar modal usando la función que limpia los event listeners
            handleClose();
        });

        const cancelBtn = document.createElement('button');
        cancelBtn.className = 'secondary-btn';
        cancelBtn.textContent = 'Cancelar';
        cancelBtn.addEventListener('click', handleClose);

        buttonGroup.appendChild(acceptBtn);
        buttonGroup.appendChild(cancelBtn);
        modalContent.appendChild(buttonGroup);

        modal.appendChild(modalContent);
        document.body.appendChild(modal);

        // Usar la función global openModal que maneja la animación y la pila UI
        openModal(modal);

        // Implementar funcionalidad de arrastrar y soltar para reordenar
        this.setupDragAndDrop(columnsContainer);
    }

    /**
     * Configura la funcionalidad de arrastrar y soltar para reordenar elementos
     * @param {HTMLElement} container - Contenedor con los elementos arrastrables
     */
    setupDragAndDrop(container) {
        let draggedItem = null;

        const items = container.querySelectorAll('.column-item');

        items.forEach(item => {
            // Hacer que el elemento sea arrastrable
            item.setAttribute('draggable', 'true');

            // Eventos de arrastre
            item.addEventListener('dragstart', function () {
                draggedItem = this;
                setTimeout(() => {
                    this.style.opacity = '0.5';
                }, 0);
            });

            item.addEventListener('dragend', function () {
                draggedItem = null;
                this.style.opacity = '1';
            });

            // Eventos para cuando se arrastra sobre otros elementos
            item.addEventListener('dragover', function (e) {
                e.preventDefault();
            });

            item.addEventListener('dragenter', function (e) {
                e.preventDefault();
                this.classList.add('drag-over');
            });

            item.addEventListener('dragleave', function () {
                this.classList.remove('drag-over');
            });

            item.addEventListener('drop', function (e) {
                e.preventDefault();
                this.classList.remove('drag-over');

                if (draggedItem !== this) {
                    // Determinar si se debe insertar antes o después
                    const rect = this.getBoundingClientRect();
                    const y = e.clientY - rect.top;
                    const height = rect.height;

                    if (y < height / 2) {
                        // Insertar antes
                        container.insertBefore(draggedItem, this);
                    } else {
                        // Insertar después
                        container.insertBefore(draggedItem, this.nextSibling);
                    }
                }
            });
        });
    }

    /**
     * Muestra el menú de acciones para una entidad
     * @param {Object} entity - Entidad sobre la que realizar acciones o evento que provoca la llamada
     * @param {HTMLElement} targetElement - Elemento que disparó la acción 0 null si llamado desde un data-accion
     */
    showActionMenu(entity, targetElement) {
        //Si no llega targetElement es que entity es el targetElement
        if (!targetElement) {
            targetElement = entity.target;
            entity = this.filteredEntities[parseInt(targetElement.dataset.ndx)];
        }
        // Eliminar menús de acciones existentes
        const existingMenus = document.querySelectorAll('.entity-action-menu');
        existingMenus.forEach(menu => menu.remove());

        // Crear menú de acciones
        const actionMenu = document.createElement('div');
        actionMenu.className = 'entity-action-menu remove_on_click';
        actionMenu.dataset.ndx = targetElement.dataset.ndx;

        // Añadir opciones según la configuración
        if (this.entityConfig.actions.includes('edit')) {
            uhtml.crearBoton({
                text: 'Editar',
                accion: 'editEntity'
            }, actionMenu);
        }

        // Verificar si debe mostrarse la opción de eliminar
        const showDeleteOption = !this.entityHandlers[this.entityType] ||
            typeof this.entityHandlers[this.entityType].verifPrivilegies !== 'function' ||
            this.entityHandlers[this.entityType].verifPrivilegies('deleteRecord');

        if (this.entityConfig.actions.includes('delete') && showDeleteOption) {
            uhtml.crearBoton({
                text: 'Eliminar',
                accion: 'deleteEntity',
                class: 'danget-option'
            }, actionMenu);
        }

        // Añadir opciones específicas de la entidad si existen
        if (this.entityHandlers[this.entityType] && this.entityHandlers[this.entityType].getRowMenuOptions) {
            const specificOptions = this.entityHandlers[this.entityType].getRowMenuOptions(entity);
            if (specificOptions && specificOptions.length > 0) {
                specificOptions.forEach(option => {
                    const button = document.createElement('button');
                    button.textContent = option.label;
                    if (option.className) {
                        button.className = option.className;
                    }
                    button.addEventListener('click', () => {
                        actionMenu.remove();
                        if (option.action) {
                            option.action();
                        }
                    });
                    actionMenu.appendChild(button);
                });
            }
        }

        // Posicionar el menú
        const rect = targetElement.getBoundingClientRect();
        actionMenu.style.top = `${rect.bottom + window.scrollY}px`;
        actionMenu.style.left = `${rect.left + window.scrollX}px`;

        // Añadir el menú al documento
        document.body.appendChild(actionMenu);

        // Función para cerrar el menú
        const closeActionMenu = () => {
            if (document.body.contains(actionMenu)) {
                actionMenu.remove();
            }
            // Asegurarse de eliminar el listener
            document.removeEventListener('click', handleClickOutside);
            document.removeEventListener('touchstart', handleClickOutside);
        };

        // Función para manejar clics fuera del menú
        const handleClickOutside = (e) => {
            // Si el clic no es dentro del menú y no es en el elemento que lo abrió
            if (!actionMenu.contains(e.target) && e.target !== targetElement) {
                closeActionMenu();
            }
        };

        // Usar setTimeout para evitar que el evento actual cierre el menú inmediatamente
        setTimeout(() => {
            document.addEventListener('click', handleClickOutside);
            // Añadir soporte para eventos táctiles
            document.addEventListener('touchstart', handleClickOutside);
        }, 10);
    }

    /**
     * Obtiene la entidad anterior a la actual en la lista filtrada
     * @param {number} currentId - ID de la entidad actual
     * @returns {Object|null} - Entidad anterior o null si no hay
     */
    getPreviousEntity(currentId) {
        if (!this.filteredEntities.length) return null;

        const currentIndex = this.filteredEntities.findIndex(entity => entity.id === currentId);
        if (currentIndex === -1) return null;

        // Navegación circular: si es el primero, ir al último
        const prevIndex = currentIndex === 0 ? this.filteredEntities.length - 1 : currentIndex - 1;
        return this.filteredEntities[prevIndex];
    }

    /**
     * Obtiene la entidad siguiente a la actual en la lista filtrada
     * @param {number} currentId - ID de la entidad actual
     * @returns {Object|null} - Entidad siguiente o null si no hay
     */
    getNextEntity(currentId) {
        if (!this.filteredEntities.length) return null;

        const currentIndex = this.filteredEntities.findIndex(entity => entity.id === currentId);
        if (currentIndex === -1) return null;

        // Navegación circular: si es el último, ir al primero
        const nextIndex = currentIndex === this.filteredEntities.length - 1 ? 0 : currentIndex + 1;
        return this.filteredEntities[nextIndex];
    }

    /**
     * Navega al registro anterior
     */
    navigateToPreviousEntity() {
        // Buscar el formulario específico para este tipo de entidad
        const formId = `entity-form-${this.entityType}`;
        const form = document.getElementById(formId) || document.getElementById('entity-form');
        if (!form || !form.dataset.entityId) {
            console.error(`No se encontró el formulario ${formId} ni entity-form para navegar`);
            return;
        }

        const entityId = parseInt(form.dataset.entityId, 10);
        const prevEntity = this.getPreviousEntity(entityId);

        if (prevEntity) {
            this.updateEntityFormData(prevEntity);
        }
    }

    /**
     * Navega al registro siguiente
     */
    navigateToNextEntity() {
        // Buscar el formulario específico para este tipo de entidad
        const formId = `entity-form-${this.entityType}`;
        const form = document.getElementById(formId) || document.getElementById('entity-form');
        if (!form || !form.dataset.entityId) {
            console.error(`No se encontró el formulario ${formId} ni entity-form para navegar`);
            return;
        }

        const entityId = parseInt(form.dataset.entityId, 10);
        const nextEntity = this.getNextEntity(entityId);

        if (nextEntity) {
            this.updateEntityFormData(nextEntity);
        }
    }

    /**
     * Actualiza los datos del formulario con la entidad proporcionada sin recrear el formulario
     * @param {Object} entity - Entidad con los datos a mostrar
     */
    updateEntityFormData(entity) {
        if (!entity) return;

        // Buscar el formulario específico para este tipo de entidad
        const formId = `entity-form-${this.entityType}`;
        const form = document.getElementById(formId) || document.getElementById('entity-form');
        if (!form) return;

        // Actualizar el ID de la entidad en el formulario
        form.dataset.entityId = entity.id;
        form.dataset.isNew = 'false';

        // Actualizar el título del modal
        const titleId = `entity-edit-title-${this.entityType}`;
        const title = document.getElementById(titleId) || document.getElementById('entity-edit-title');
        if (title) {
            title.textContent = `Editar ${this.entityConfig.title.slice(0, -1)}`;
        }

        // Actualizar los valores de los campos
        this.entityConfig.fields.forEach(field => {
            if (field.name === 'id') return; // No actualizar el ID

            const input = document.getElementById(`entity-${field.name}`);
            if (!input) return;

            // Limpiar mensajes de error
            const errorElement = document.getElementById(`error-${field.name}`);
            if (errorElement) {
                errorElement.textContent = '';
                input.classList.remove('invalid');
            }

            // Establecer el valor según el tipo de campo
            if (field.type === 'checkbox') {
                input.checked = Boolean(entity[field.name]);
            } else if (field.type === 'date' && entity[field.name]) {
                // Formatear fecha para input type="date" (YYYY-MM-DD)
                const date = new Date(entity[field.name]);
                input.value = date.toISOString().split('T')[0];
            } else {
                input.value = entity[field.name] !== undefined && entity[field.name] !== null ? entity[field.name] : '';
            }
        });

        // Actualizar estado de los botones de opciones
        const optionsButton = document.getElementById('entity-options-btn');
        if (optionsButton) {
            // Forzar actualización del menú de opciones la próxima vez que se abra
            optionsButton.dataset.isNew = 'false';
        }
    }

    /**
     * Obtiene opciones adicionales para el menú de opciones de la entidad
     * @param {boolean} isNew - Indica si es un nuevo registro
     * @returns {Array} - Array de objetos con opciones adicionales
     */
    getEntityFormOptions(isNew) {
        let options = [];

        // Opciones básicas
        options.push({
            id: 'new',
            label: 'Nuevo',
            disabled: isNew,
            action: () => this.showEntityForm()
        });

        options.push({
            id: 'delete',
            label: 'Eliminar',
            disabled: isNew,
            className: 'danger-option',
            action: () => this.deleteCurrentEntity()
        });

        // Opción para establecer clave en usuarios
        if (this.entityType === 'usuarios') {
            options.push({
                id: 'set-password',
                label: 'Clave',
                disabled: false,
                action: () => this.setUserPassword()
            });
        }

        // Obtener opciones adicionales del handler específico
        if (this.entityHandlers[this.entityType] && this.entityHandlers[this.entityType].getEntityFormOptions) {
            const additionalOptions = this.entityHandlers[this.entityType].getEntityFormOptions(isNew);
            if (additionalOptions && additionalOptions.length) {
                options = [...options, ...additionalOptions];
            }
        }

        return options;
    }

    /**
     * Elimina la entidad actual mostrada en el formulario
     */
    async deleteCurrentEntity() {
        const form = document.getElementById('entity-form');
        if (!form || !form.dataset.entityId) return;

        const entityId = parseInt(form.dataset.entityId, 10);
        const entity = this.entities.find(e => e.id === entityId);

        if (!entity) return;

        const entityName = this.entityConfig.title.slice(0, -1).toLowerCase();
        const confirmed = await dialogManager.confirmDelete(`¿Está seguro de que desea eliminar ${entityName} "${entity.id}"?`);

        if (confirmed) {
            // Obtener la siguiente entidad antes de eliminar
            const nextEntity = this.getNextEntity(entityId);

            // Usar el manejador específico para eliminar
            if (this.entityHandlers[this.entityType] && this.entityHandlers[this.entityType].deleteEntity) {
                try {
                    await this.entityHandlers[this.entityType].deleteEntity(entityId);

                    // Recargar entidades
                    await this.loadEntities();
                    this.renderEntityTable();

                    showNotification(`${this.entityConfig.title.slice(0, -1)} eliminada correctamente`, 'success');

                    // Si hay más entidades, mostrar la siguiente
                    if (this.filteredEntities.length > 0 && nextEntity) {
                        // Si el formulario ya está abierto, actualizar los datos
                        const form = document.getElementById('entity-form');
                        if (form) {
                            this.updateEntityFormData(nextEntity);
                        } else {
                            // Si no está abierto, mostrarlo
                            this.showEntityForm(nextEntity);
                        }
                    } else {
                        // Si no hay más entidades, mostrar mensaje y cerrar
                        await dialogManager.alert('No hay más registros');
                        this.closeEntityForm();
                    }
                } catch (error) {
                    console.error('Error al eliminar la entidad:', error);
                    await dialogManager.alert(`Error al eliminar: ${error.message}`);
                    showNotification(`Error al eliminar: ${error.message}`, 'error');
                }
            } else {
                console.error(`No hay un manejador para eliminar entidades de tipo: ${this.entityType}`);
                await dialogManager.alert(`No hay un manejador para eliminar entidades de tipo: ${this.entityType}`);
            }
        }
    }

    /**
     * Configura los eventos de navegación gestual para el formulario
     * @param {HTMLElement} modal - Elemento modal que contiene el formulario
     * @param {boolean} hasMultipleEntities - Indica si hay múltiples entidades para navegar
     */
    setupGestureNavigation(modal, hasMultipleEntities) {
        if (!modal) return;

        // Obtener el contenido del modal
        const modalContent = modal.querySelector('.modal-content');
        if (!modalContent) return;

        // Variables para seguimiento de toques
        let touchStartX = 0;
        let touchEndX = 0;

        /**
         * Verifica si el elemento objetivo es válido para el gesto de deslizamiento
         * @param {HTMLElement} target - Elemento donde se inició el evento
         * @returns {boolean} - true si es un área válida para deslizar
         */
        const isValidTarget = (target) => {
            // No permitir deslizamiento en campos de entrada, selects, checkboxes, etc.
            if (target.tagName === 'INPUT' ||
                target.tagName === 'SELECT' ||
                target.tagName === 'TEXTAREA' ||
                target.tagName === 'BUTTON' ||
                target.tagName === 'A' ||
                target.tagName === 'LABEL' ||
                target.isContentEditable) {
                return false;
            }

            // Verificar si alguno de los padres es un elemento no válido
            let parent = target.parentElement;
            while (parent && parent !== modalContent) {
                if (parent.tagName === 'INPUT' ||
                    parent.tagName === 'SELECT' ||
                    parent.tagName === 'TEXTAREA' ||
                    parent.tagName === 'BUTTON' ||
                    parent.tagName === 'A' ||
                    parent.tagName === 'LABEL' ||
                    parent.isContentEditable) {
                    return false;
                }
                parent = parent.parentElement;
            }

            // Por defecto, permitir el deslizamiento
            return true;
        };

        // Función para manejar el inicio del toque
        const handleTouchStart = (e) => {
            // Verificar si el toque comenzó en un área válida
            if (!isValidTarget(e.target)) return;

            touchStartX = e.changedTouches[0].screenX;
        };

        // Función para manejar el fin del toque
        const handleTouchEnd = (e) => {
            if (!hasMultipleEntities) return;

            // Verificar si el toque comenzó en un área válida
            if (!isValidTarget(e.target)) return;

            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        };

        // Función para manejar el inicio del arrastre con ratón
        const handleMouseDown = (e) => {
            // No iniciar arrastre con clic derecho
            if (e.button !== 0) return;

            // Verificar si el clic comenzó en un área válida
            if (!isValidTarget(e.target)) return;

            touchStartX = e.screenX;

            // Añadir eventos para seguir el movimiento y soltar
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
        };

        // Variables para seguimiento de arrastre con ratón
        let isDragging = false;

        // Función para manejar el movimiento del ratón
        const handleMouseMove = (e) => {
            // Marcar como arrastre si el movimiento es significativo
            if (Math.abs(e.screenX - touchStartX) > 30) {
                isDragging = true;
            }
        };

        // Función para manejar el fin del arrastre con ratón
        const handleMouseUp = (e) => {
            if (!hasMultipleEntities || !isDragging) {
                // Limpiar eventos
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
                isDragging = false;
                return;
            }

            touchEndX = e.screenX;
            handleSwipe();

            // Limpiar eventos
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
            isDragging = false;
        };

        // Función para manejar el gesto de deslizamiento
        const handleSwipe = () => {
            // Buscar el formulario específico para este tipo de entidad
            const formId = `entity-form-${this.entityType}`;
            const form = document.getElementById(formId) || document.getElementById('entity-form');
            if (!form || !form.dataset.entityId) return;

            const swipeThreshold = 100; // Umbral para considerar un deslizamiento válido

            if (touchEndX - touchStartX > swipeThreshold) {
                // Deslizamiento hacia la derecha -> entidad siguiente (avanzar)
                this.navigateToNextEntity();
            } else if (touchStartX - touchEndX > swipeThreshold) {
                // Deslizamiento hacia la izquierda -> entidad anterior (retroceder)
                this.navigateToPreviousEntity();
            }
        };

        // Añadir eventos de toque
        modalContent.addEventListener('touchstart', handleTouchStart);
        modalContent.addEventListener('touchend', handleTouchEnd);

        // Añadir eventos de ratón para arrastre
        modalContent.addEventListener('mousedown', handleMouseDown);

        // Guardar referencias a los manejadores para poder eliminarlos después
        modalContent.gestureHandlers = {
            touchStart: handleTouchStart,
            touchEnd: handleTouchEnd,
            mouseDown: handleMouseDown
        };
    }

    /**
     * Limpia los eventos de navegación gestual
     * @param {HTMLElement} modal - Elemento modal que contiene el formulario
     */
    cleanupGestureNavigation(modal) {
        if (!modal) return;

        const modalContent = modal.querySelector('.modal-content');
        if (!modalContent || !modalContent.gestureHandlers) return;

        // Eliminar eventos de toque
        modalContent.removeEventListener('touchstart', modalContent.gestureHandlers.touchStart);
        modalContent.removeEventListener('touchend', modalContent.gestureHandlers.touchEnd);

        // Eliminar eventos de ratón
        modalContent.removeEventListener('mousedown', modalContent.gestureHandlers.mouseDown);

        // Limpiar referencias
        delete modalContent.gestureHandlers;
    }

    /**
     * Muestra el formulario para añadir/editar una entidad
     * @param {Object} entity - Entidad a editar (null para nueva entidad)
     */
    showEntityForm(entity = null) {
        const isNew = !entity || entity.target != undefined;

        this.entidad_actual = entity;

        // Generar un ID único para este modal específico de la entidad
        const modalId = `entity-edit-modal-${this.entityType}`;

        // Verificar si ya existe un modal para este tipo de entidad
        let modal = document.getElementById(modalId);

        if (!modal) {
            // Buscar el modal original para clonarlo
            const originalModal = document.getElementById('entity-edit-modal');

            // Clonar el modal original
            modal = originalModal.cloneNode(true);
            modal.id = modalId;

            // Actualizar IDs de los elementos internos
            const titleElement = modal.querySelector('h2');
            if (titleElement) {
                titleElement.id = `entity-edit-title-${this.entityType}`;
            }

            const formContainerElement = modal.querySelector('#entity-form-container');
            if (formContainerElement) {
                formContainerElement.id = `entity-form-container-${this.entityType}`;
            }

            // Añadir el modal al DOM
            document.body.appendChild(modal);
        }

        // Obtener referencias a los elementos del modal
        const title = modal.querySelector('h2');
        const formContainer = modal.querySelector('#entity-form-container-' + this.entityType) ||
            modal.querySelector('#entity-form-container') ||
            modal.querySelector('.modal-content > div:nth-child(3)');
        const buttonGroup = modal.querySelector('.button-group');

        if (!title || !formContainer || !buttonGroup) {
            console.error('No se pudieron encontrar elementos necesarios en el modal');
            return;
        }

        // Actualizar IDs para que sean específicos de esta entidad
        if (formContainer.id !== `entity-form-container-${this.entityType}`) {
            formContainer.id = `entity-form-container-${this.entityType}`;
        }
        if (title.id !== `entity-edit-title-${this.entityType}`) {
            title.id = `entity-edit-title-${this.entityType}`;
        }

        // Limpiar eventos de navegación gestual previos
        this.cleanupGestureNavigation(modal);

        // Actualizar título
        title.textContent = isNew ? `Añadir ${this.entityConfig.title.slice(0, -1)}` : `Editar ${this.entityConfig.title.slice(0, -1)}`;

        // Limpiar formulario
        formContainer.innerHTML = '';

        // Limpiar grupo de botones y recrearlo
        buttonGroup.innerHTML = '';
        if (entity) buttonGroup.dataset.entity_id = entity.id;

        // Crear formulario con ID específico para este tipo de entidad
        const form = document.createElement('form');
        form.id = `entity-form-${this.entityType}`;
        form.dataset.entityId = isNew ? '' : entity.id;
        form.dataset.isNew = isNew ? 'true' : 'false';

        // Añadir campos según la configuración
        this.entityConfig.fields.forEach(field => {
            // No mostrar el campo ID en el formulario, los campos no editables, ni los campos tipo password
            if (field.name === 'id' || field.editable === false || field.type === 'password') return;

            const formGroup = document.createElement('div');
            formGroup.className = 'form-group';

            const label = document.createElement('label');
            label.setAttribute('for', `entity-${field.name}`);
            label.textContent = field.label;

            let input;

            switch (field.type) {
                case 'textarea':
                    input = document.createElement('textarea');
                    input.rows = 3;
                    break;
                case 'select':
                    input = document.createElement('select');
                    if (field.options) {
                        field.options.forEach(option => {
                            const optionElement = document.createElement('option');
                            optionElement.value = option.value;
                            optionElement.textContent = option.label;
                            input.appendChild(optionElement);
                        });
                    }
                    break;
                case 'checkbox':
                    input = document.createElement('input');
                    input.type = 'checkbox';
                    break;
                case 'date':
                    input = document.createElement('input');
                    input.type = 'date';
                    break;
                case 'password':
                    input = document.createElement('input');
                    input.type = 'password';
                    break;
                default:
                    input = document.createElement('input');
                    input.type = field.type === 'number' ? 'number' : 'text';
            }

            input.id = `entity-${field.name}`;
            input.name = field.name;

            // Establecer valor si es edición
            if (!isNew && entity[field.name] !== undefined) {
                if (field.type === 'checkbox') {
                    input.checked = Boolean(entity[field.name]);
                } else if (field.type === 'date' && entity[field.name]) {
                    // Formatear fecha para input type="date" (YYYY-MM-DD)
                    const date = new Date(entity[field.name]);
                    input.value = date.toISOString().split('T')[0];
                } else {
                    input.value = entity[field.name] !== undefined && entity[field.name] !== null ? entity[field.name] : '';
                }
            } else if (isNew && field.name === 'activo' && field.type === 'checkbox') {
                // Para nuevos registros, marcar "Activo" por defecto
                input.checked = true;
            }

            // Añadir atributos adicionales
            if (field.required) {
                input.required = true;
            }

            if (field.placeholder) {
                input.placeholder = field.placeholder;
            }

            // Añadir elementos al grupo
            if (field.type === 'checkbox') {
                // Para checkboxes, colocar el input al lado de la etiqueta
                formGroup.style.display = 'flex';
                formGroup.style.alignItems = 'center';
                formGroup.style.flexWrap = 'wrap';

                // Crear un contenedor para el checkbox y la etiqueta
                const checkboxContainer = document.createElement('div');
                checkboxContainer.style.display = 'flex';
                checkboxContainer.style.alignItems = 'center';
                checkboxContainer.style.marginBottom = '5px';

                // Ajustar el estilo del label para que esté al lado del checkbox
                label.style.marginBottom = '0';
                label.style.marginLeft = '8px';
                label.style.display = 'inline';

                checkboxContainer.appendChild(input);
                checkboxContainer.appendChild(label);
                formGroup.appendChild(checkboxContainer);
            } else {
                // Para otros tipos de campos, mantener el comportamiento original
                formGroup.appendChild(label);
                formGroup.appendChild(input);
            }

            // Añadir mensaje de error
            const errorMessage = document.createElement('p');
            errorMessage.className = 'error-message';
            errorMessage.id = `error-${field.name}`;

            // Para checkboxes, asegurar que el mensaje de error aparezca en una nueva línea
            if (field.type === 'checkbox') {
                errorMessage.style.width = '100%';
                errorMessage.style.marginTop = '5px';
            }

            formGroup.appendChild(errorMessage);

            form.appendChild(formGroup);
        });

        // Añadir botón de clave para usuarios
        if (this.entityType === 'usuarios') {
            const passwordButtonContainer = document.createElement('div');
            passwordButtonContainer.className = 'form-group';
            passwordButtonContainer.style.marginTop = '20px';

            const passwordButton = document.createElement('button');
            passwordButton.type = 'button';
            passwordButton.className = 'secondary-btn';
            passwordButton.textContent = 'Clave';
            passwordButton.title = 'Establecer contraseña';
            passwordButton.addEventListener('click', () => {
                this.setUserPassword();
            });

            passwordButtonContainer.appendChild(passwordButton);
            form.appendChild(passwordButtonContainer);
        }

        formContainer.appendChild(form);

        // Verificar si hay múltiples entidades para la navegación
        const hasMultipleEntitiesForNav = this.filteredEntities.length > 1;

        // Eliminar el formulario del contenedor (ya que lo vamos a añadir de otra manera)
        if (form.parentNode === formContainer) {
            formContainer.removeChild(form);
        }

        // Crear botones de acción
        const isMobile = window.innerWidth <= 768;
        uhtml.crearBoton({
            html: isMobile ? '&#10004;' : 'Guardar',
            title: 'Guardar cambios',
            class: 'primary-btn',
            accion: 'saveEntity'
        }, buttonGroup);
        uhtml.crearBoton({
            id: 'entity-cancel-btn',
            html: isMobile ? '&#10006;' : 'Cancelar',
            title: 'Cancelar y Cerrar',
            class: 'secondary-btn',
            accion: 'closeEntityForm'
        }, buttonGroup);

        // Añadir flechas de navegación si no es un nuevo registro y hay múltiples entidades
        if (!isNew && hasMultipleEntitiesForNav) {
            // Crear contenedor principal del formulario que incluirá las flechas laterales
            const formWithNavContainer = document.createElement('div');
            formWithNavContainer.className = 'form-with-nav-container';
            formWithNavContainer.style.position = 'relative';
            formWithNavContainer.style.width = '100%';

            // Añadir el formulario al contenedor
            formWithNavContainer.appendChild(form);

            // Flecha izquierda (anterior)
            uhtml.crearBoton({
                html: '&#8592;',
                accion: 'navigateToPreviousEntity',
                class: 'secondary-btn icono-boton' //nav-button prev-button side-nav-button'
            }, buttonGroup);
            uhtml.crearBoton({
                html: '&#8594;',
                accion: 'navigateToNextEntity',
                class: 'secondary-btn icono-boton' //nav-button prev-button side-nav-button'
            }, buttonGroup);

            // Añadir el contenedor con el formulario y las flechas al contenedor principal
            formContainer.appendChild(formWithNavContainer);

            // Configurar navegación gestual
            this.setupGestureNavigation(modal, hasMultipleEntitiesForNav);
        } else {
            // Si no hay navegación, añadir el formulario directamente
            formContainer.appendChild(form);
        }

        // Verificar si debe mostrarse el botón de hamburguesa
        const showHamburgerButton = !isNew && (!this.entityHandlers[this.entityType] ||
            typeof this.entityHandlers[this.entityType].verifPrivilegies !== 'function' ||
            this.entityHandlers[this.entityType].verifPrivilegies('showHamburgerMenu'));

        // Ocultar el botón si no tiene privilegios
        if (showHamburgerButton) {
            // Botón Opciones con icono de hamburguesa
            let optionsButton = uhtml.crearBoton({
                id: 'entity-options-btn',
                title: 'Opciones adicionales',
                class: 'secondary-btn icono-boton',
                accion: 'handleClickEntityOptionsButton',
                html: '<span class="hamburguer-icon">&#9776;</span>'
            }, buttonGroup);


            // Variable para rastrear si el menú está abierto
            optionsButton.dataset.menuOpen = 'false';
            optionsButton.dataset.isNew = isNew ? 1 : 0;
        }

        // Mostrar modal
        openModal(modal);
    }

    /**
     * Muestra el menú de opciones para el formulario de entidad
     * @param {HTMLElement} targetElement - Elemento que disparó la acción
     * @param {boolean} isNew - Indica si es un nuevo registro
     * @param {Function} onCloseCallback - Función a ejecutar cuando se cierre el menú
     */
    showEntityOptionsMenu(targetElement, isNew, onCloseCallback) {
        // TODO: Implementar lógica para mostrar el menú de opciones
        // Eliminar menús existentes
        const existingMenus = document.querySelectorAll('.entity-options-menu');
        existingMenus.forEach(menu => menu.remove());

        // Crear menú de opciones
        const optionsMenu = document.createElement('div');
        optionsMenu.className = 'entity-options-menu entity-action-menu';

        // Obtener opciones
        const options = this.getEntityFormOptions(isNew);

        // Añadir opciones al menú
        options.forEach(option => {
            const button = document.createElement('button');
            button.textContent = option.label;
            button.disabled = option.disabled;

            if (option.className) {
                button.className = option.className;
            }

            if (option.disabled) {
                button.classList.add('disabled');
            }

            button.addEventListener('click', () => {
                closeOptionsMenu();
                if (option.action && !option.disabled) {
                    option.action();
                }
            });

            optionsMenu.appendChild(button);
        });

        // Posicionar el menú
        const rect = targetElement.getBoundingClientRect();

        // Calcular posición para asegurar que quepa en la pantalla
        const menuWidth = 200; // Ancho estimado del menú
        const windowWidth = window.innerWidth;

        // Posicionar preferentemente a la derecha, pero ajustar si no cabe
        let leftPos = rect.right;
        if (leftPos + menuWidth > windowWidth) {
            leftPos = rect.left - menuWidth;
        }

        // Si aún no cabe, centrar sobre el botón
        if (leftPos < 0) {
            leftPos = rect.left + (rect.width / 2) - (menuWidth / 2);
        }

        // Posicionar el menú según el dispositivo
        if (window.innerWidth <= 768) {
            // En móvil, posicionar encima del botón
            optionsMenu.style.position = 'fixed';

            // Calcular si hay suficiente espacio arriba
            const spaceAbove = rect.top;
            const menuHeight = 200; // Altura estimada del menú (ajustar según sea necesario)

            if (spaceAbove >= menuHeight) {
                // Si hay suficiente espacio arriba, mostrar el menú encima del botón
                optionsMenu.style.bottom = `${window.innerHeight - rect.top + 10}px`;
                optionsMenu.style.top = 'auto';
            } else {
                // Si no hay suficiente espacio arriba, mostrar el menú debajo del botón
                optionsMenu.style.top = `${rect.bottom + 10}px`;
                optionsMenu.style.bottom = 'auto';
            }

            // Centrar horizontalmente respecto al botón
            const buttonCenter = rect.left + (rect.width / 2);
            const menuWidth = 200; // Ancho estimado del menú
            optionsMenu.style.left = `${Math.max(10, buttonCenter - (menuWidth / 2))}px`;
        } else {
            // En escritorio, posicionar debajo del botón
            optionsMenu.style.position = 'absolute';
            optionsMenu.style.top = `${rect.bottom + window.scrollY}px`;
            optionsMenu.style.left = `${leftPos}px`;
        }

        // Añadir el menú al documento
        document.body.appendChild(optionsMenu);

        // Función para cerrar el menú
        const closeOptionsMenu = () => {
            if (document.body.contains(optionsMenu)) {
                optionsMenu.remove();
            }
            document.removeEventListener('click', handleClickOutside);
            document.removeEventListener('touchstart', handleClickOutside);

            // Ejecutar callback si existe
            if (typeof onCloseCallback === 'function') {
                onCloseCallback();
            }
        };

        // Función para manejar clics fuera del menú
        const handleClickOutside = (e) => {
            if (!optionsMenu.contains(e.target) && e.target !== targetElement) {
                closeOptionsMenu();
            }
        };

        // Usar setTimeout para evitar que el evento actual cierre el menú
        setTimeout(() => {
            document.addEventListener('click', handleClickOutside);
            document.addEventListener('touchstart', handleClickOutside);
        }, 10);
    }

    /**
     * Cierra el formulario de edición
     */
    closeEntityForm() {
        // Buscar el modal específico para este tipo de entidad
        const modalId = `entity-edit-modal-${this.entityType}`;
        const modal = document.getElementById(modalId);

        if (modal) {
            // Usar la función global closeModal que maneja la animación y la pila UI
            closeModal(modal);
        } else {
            // Si no se encuentra el modal específico, intentar con el genérico
            const genericModal = document.getElementById('entity-edit-modal');
            if (genericModal) {
                closeModal(genericModal);
            } else {
                console.warn(`No se encontró el modal ${modalId} ni entity-edit-modal para cerrar`);
            }
        }
    }

    /**
     * Guarda la entidad (nueva o editada)
     */
    async saveEntity(entity) {
        entity = this.event_to_entity(entity);
        // Buscar el formulario específico para este tipo de entidad
        const formId = `entity-form-${this.entityType}`;
        const form = document.getElementById(formId) || document.getElementById('entity-form');
        if (!form) {
            console.error(`No se encontró el formulario ${formId} ni entity-form`);
            return;
        }

        // Validar formulario
        if (!this.validateEntityForm()) {
            return;
        }

        // Obtener datos del formulario
        const entityData = {
            _entity: entity
        };
        const entityId = form.dataset.entityId;
        const isNew = form.dataset.isNew === 'true';

        // Si hay ID, es una edición
        if (entityId) {
            entityData.id = parseInt(entityId, 10);
        }

        // Recoger valores de los campos
        this.entityConfig.fields.forEach(field => {
            if (field.name === 'id') return; // El ID ya se ha procesado

            // Para campos editables, obtener valor del formulario
            if (field.editable !== false) {
                // Buscar el input dentro del formulario específico en lugar de en todo el documento
                const input = form.querySelector(`#entity-${field.name}`);
                if (!input) {
                    console.warn(`Campo ${field.name} no encontrado en el formulario actual`);
                    return;
                }

                let value;

                switch (field.type) {
                    case 'number':
                        value = input.value ? parseFloat(input.value) : null;
                        break;
                    case 'checkbox':
                        value = input.checked;
                        break;
                    case 'date':
                        value = input.value ? new Date(input.value) : null;
                        break;
                    default:
                        value = input.value;
                }

                entityData[field.name] = value;
                console.log(`Campo ${field.name} = ${value}`);
            }
            // Para campos no editables, no hacemos nada aquí
            // El handler específico se encargará de asignar estos valores
        });

        // Usar el manejador específico para guardar
        if (this.entityHandlers[this.entityType] && this.entityHandlers[this.entityType].saveEntity) {
            try {
                await this.entityHandlers[this.entityType].saveEntity(entityData);

                // Recargar entidades
                await this.loadEntities();
                // this.renderEntityTable();

                // Mostrar notificación
                showNotification(`${this.entityConfig.title.slice(0, -1)} ${entityId ? 'actualizada' : 'creada'} correctamente`, 'success');

                // Si era un nuevo registro, buscar la entidad recién creada
                if (isNew) {
                    // Buscar la entidad recién creada (normalmente será la última)
                    const newEntity = this.entities.find(e => {
                        // Comparar todos los campos excepto ID para identificar la entidad
                        let match = true;
                        Object.keys(entityData).forEach(key => {
                            if (key !== 'id' && String(e[key]) !== String(entityData[key])) {
                                match = false;
                            }
                        });
                        return match;
                    });

                    // Si se encontró, mostrar el formulario con la entidad creada
                    if (newEntity) {
                        this.showEntityForm(newEntity);
                    } else {
                        // Si no se encontró, cerrar el formulario
                        this.closeEntityForm();
                    }
                } else {
                    // Si era una edición, buscar la entidad actualizada
                    const updatedEntity = this.entities.find(e => e.id === parseInt(entityId, 10));

                    // Si se encontró, mostrar el formulario con la entidad actualizada
                    if (updatedEntity) {
                        this.showEntityForm(updatedEntity);
                    } else {
                        // Si no se encontró, cerrar el formulario
                        this.closeEntityForm();
                    }
                }
            } catch (error) {
                console.error('Error al guardar la entidad:', error);
                showNotification(`Error al guardar: ${error.message}`, 'error');
            }
        } else {
            console.error(`No hay un manejador para guardar entidades de tipo: ${this.entityType}`);
            showNotification(`Error: No hay un manejador para guardar entidades de tipo: ${this.entityType}`, 'error');
        }
    }

    /**
     * Valida el formulario de entidad
     * @returns {boolean} - true si el formulario es válido
     */
    validateEntityForm() {
        // Buscar el formulario específico para este tipo de entidad
        const formId = `entity-form-${this.entityType}`;
        const form = document.getElementById(formId) || document.getElementById('entity-form');
        if (!form) {
            console.error(`No se encontró el formulario ${formId} ni entity-form para validar`);
            return false;
        }

        let isValid = true;

        // Validar cada campo según su configuración
        this.entityConfig.fields.forEach(field => {
            if (field.name === 'id') return; // No validar el ID

            // Buscar el input y el elemento de error dentro del formulario específico
            const input = form.querySelector(`#entity-${field.name}`);
            const errorElement = form.querySelector(`#error-${field.name}`);

            if (!input || !errorElement) {
                console.warn(`Campo ${field.name} o su elemento de error no encontrado en el formulario actual`);
                return;
            }

            let errorMessage = '';
            let valor = (input.value || '').trim();

            // Validar campo requerido
            if (field.required &&
                field.type !== 'checkbox' && !valor) {
                errorMessage = `El campo ${field.label} es obligatorio`;
            }

            // Validar email
            if (field.type === 'email' && valor && !validateEmail(valor)) {
                errorMessage = 'El formato de email no es válido';
            }

            // Validar longitud mínima
            if (field.minLength && input.value.length < field.minLength) {
                errorMessage = `El campo debe tener al menos ${field.minLength} caracteres`;
            }

            // Validar longitud máxima
            if (field.maxLength && input.value.length > field.maxLength) {
                errorMessage = `El campo no puede tener más de ${field.maxLength} caracteres`;
            }

            // Validaciones personalizadas
            if (field.validate && valor) {
                const customError = field.validate(valor);
                if (customError) {
                    errorMessage = customError;
                }
            }

            // Validar unicidad del campo nombre
            if (field.name === 'nombre' && valor) {
                // Usar el formulario que ya tenemos
                let entityId = form ? form.dataset.entityId : null;
                const normalizedValue = normalizeText(valor);

                entityId = parseInt(entityId, 10);
                const ndxOtro = this.entities.indexOf(item => item.id != entityId && normalizeText(entity.nombre) === normalizedValue);

                /*TO DELETE // Buscar si ya existe una entidad con el mismo nombre normalizado
                 const exists = this.entities.some(entity => {
                     // Excluir la entidad actual en caso de edición
                     if (entityId && entity.id === parseInt(entityId, 10)) {
                         return false;
                     }
                     return normalizeText(entity.nombre) === normalizedValue;
                 });
 */
                if (ndxOtro >= 0) {
                    errorMessage = `Ya existe ${this.entityConfig.title.slice(0, -1).toLowerCase()} con este nombre`;
                }

            }

            // Actualizar mensaje de error y validez
            errorElement.textContent = errorMessage;

            if (errorMessage) {
                input.classList.add('invalid');
                isValid = false;
            } else {
                input.classList.remove('invalid');
            }
        });

        return isValid;
    }

    /**
     * Edita una entidad existente
     * @param {Object} entity - Entidad a editar
     */
    editEntity(entity) {
        entity = this.event_to_entity(entity);
        this.showEntityForm(entity);
    }

    /** Clic en el botón para mostrar las opciones de una ficha de una entidad
     * @param {evento} e - Evento clic que dispara el mostrar ese menú.
     */
    handleClickEntityOptionsButton(e) {
        let optionsButton = e.target;
        let hamburgerIcon;
        if (optionsButton.classList.contains('hamburguer-icon')) {
            hamburgerIcon = optionsButton;
            optionsButton = hamburgerIcon.closest('#entity-options-btn');
        } else
            hamburgerIcon = optionsButton.children.item('.hamburguer-icon');

        // Verificar si el menú ya está abierto
        const isMenuOpen = optionsButton.dataset.menuOpen === 'true';

        if (isMenuOpen) {
            // Si el menú está abierto, cerrarlo
            const existingMenu = document.querySelector('.entity-options-menu');
            if (existingMenu) {
                existingMenu.remove();
            }

            // Actualizar estado y animar el icono
            optionsButton.dataset.menuOpen = 'false';
        } else {
            // Actualizar estado
            optionsButton.dataset.menuOpen = 'true';

            this.showEntityOptionsMenu(optionsButton, optionsButton.dataset.isNew, () => {
                // Callback para cuando se cierre el menú
                optionsButton.dataset.menuOpen = 'false';
            });
        }
    }

    /** Devuelve la entidad o la calcula si le llega un evento */
    event_to_entity(entity) {
        if (entity.target) {
            let targetElement = entity.target;
            if (targetElement.dataset.ndx == undefined) {
                targetElement = targetElement.closest('[data-ndx]')
                if (!targetElement) {
                    let id = parseInt(entity.target.closest('[data-entity_id]').dataset.entity_id);
                    return this.filteredEntities.find(
                        item => {
                            return item.id == id;
                        });
                }
            }
            entity = this.filteredEntities[parseInt(targetElement.dataset.ndx)];
        }
        return entity;
    }

    /**
     * Establece la contraseña para un usuario
     * Muestra un diálogo simple para establecer la contraseña en texto plano
     */
    async setUserPassword() {
        if (this.entityType !== 'usuarios') return;

        // Buscar el formulario específico para usuarios
        const formId = `entity-form-${this.entityType}`;
        const form = document.getElementById(formId) || document.getElementById('entity-form');
        if (!form) {
            console.error(`No se encontró el formulario ${formId} ni entity-form`);
            return;
        }

        const entityId = form.dataset.entityId;
        if (!entityId) {
            showNotification('Debe guardar el usuario antes de establecer la contraseña', 'error');
            return;
        }

        // Obtener el usuario actual
        const userId = parseInt(entityId, 10);
        const currentEntity = this.entities.find(e => e.id === userId);

        if (!currentEntity) {
            showNotification('Usuario no encontrado', 'error');
            return;
        }

        // Mostrar diálogo para establecer la contraseña
        const password = await dialogManager.prompt('Introduzca la nueva contraseña:', '');

        if (password === null) return; // Cancelado

        if (!password.trim()) {
            showNotification('La contraseña no puede estar vacía', 'error');
            return;
        }

        try {
            // Actualizar la contraseña
            const updatedEntity = {
                ...currentEntity,
                clave: password
            };

            // Guardar usando el handler específico
            if (this.entityHandlers[this.entityType] && this.entityHandlers[this.entityType].saveEntity) {
                await this.entityHandlers[this.entityType].saveEntity(updatedEntity);

                // Actualizar la entidad en la lista
                const index = this.entities.findIndex(e => e.id === userId);
                if (index !== -1) {
                    this.entities[index] = updatedEntity;
                }

                showNotification('Contraseña establecida correctamente', 'success');

                // Actualizar el formulario si está abierto
                // Buscar el formulario específico para usuarios
                const formId = `entity-form-${this.entityType}`;
                const form = document.getElementById(formId) || document.getElementById('entity-form');
                if (form) {
                    // Buscar el input de contraseña dentro del formulario específico
                    const passwordInput = form.querySelector('#entity-clave');
                    if (passwordInput) {
                        passwordInput.value = password;
                    }
                }
            }
        } catch (error) {
            console.error('Error al establecer la contraseña:', error);
            showNotification(`Error al establecer la contraseña: ${error.message}`, 'error');
        }
    }

    /**
     * Elimina una entidad
     * @param {Object} entity - Entidad a eliminar
     */
    async deleteEntity(entity) {
        const entityName = this.entityConfig.title.slice(0, -1).toLowerCase();

        // Usar el diálogo de confirmación de eliminación con estilo rojizo
        const confirmed = await dialogManager.confirmDelete(`¿Está seguro de que desea eliminar ${entityName} "${entity.id}"?`);

        if (confirmed) {
            // Usar el manejador específico para eliminar
            if (this.entityHandlers[this.entityType] && this.entityHandlers[this.entityType].deleteEntity) {
                try {
                    await this.entityHandlers[this.entityType].deleteEntity(entity.id);
                    // Recargar entidades
                    await this.loadEntities();
                    this.renderEntityTable();
                    showNotification(`${this.entityConfig.title.slice(0, -1)} eliminada correctamente`, 'success');
                } catch (error) {
                    console.error('Error al eliminar la entidad:', error);
                    await dialogManager.alert(`Error al eliminar: ${error.message}`);
                    showNotification(`Error al eliminar: ${error.message}`, 'error');
                }
            } else {
                console.error(`No hay un manejador para eliminar entidades de tipo: ${this.entityType}`);
                await dialogManager.alert(`No hay un manejador para eliminar entidades de tipo: ${this.entityType}`);
            }
        }
    }
}

// Crear instancia global del gestor de entidades
const entityManager = new EntityManager();

// Registrar la instancia global en el objeto app
app['entityManager'] = entityManager;