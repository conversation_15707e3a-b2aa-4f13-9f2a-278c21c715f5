/**
 * Script para generar datos de prueba para la aplicación iPRA
 */

// Función para generar un ID único
function generateUniqueId() {
    return Date.now() + Math.floor(Math.random() * 1000);
}

// Función para generar un nombre de empresa aleatorio
function generateCompanyName() {
    // Prefijos para empresas tecnológicas
    const techPrefixes = ['Tech', 'Cyber', 'Digital', 'Smart', 'Intelligent', 'Advanced', 'Future', 'Next', 'Modern', 'Dynamic', 'Quantum', 'Nano', 'Micro', 'Macro', 'Giga', 'Meta', 'Virtual', 'Cloud', 'Data', 'Info'];
    const techRoots = ['Soft', 'Tech', 'Systems', 'Solutions', 'Dynamics', 'Logic', 'Data', 'Info', 'Net', 'Web', 'Cloud', 'Mobile', 'Vision', 'Mind', 'Think', 'Idea', 'Concept', 'Innovation', 'Creation', 'Development'];
    const techSuffixes = ['Corp', 'Inc', 'Ltd', 'Group', 'Systems', 'Solutions', 'Technologies', 'Enterprises', 'Partners', 'Associates', 'International', 'Global', 'World', 'Industries', 'Services', 'Consulting', 'Software', 'Hardware', 'Networks', 'Communications'];

    // Nombres para empresas industriales
    const industrialNames = ['Aceros', 'Hierros', 'Metales', 'Construcciones', 'Materiales', 'Cementos', 'Hormigones', 'Estructuras', 'Maquinaria', 'Equipos', 'Herramientas', 'Montajes', 'Instalaciones', 'Mantenimiento', 'Reparaciones', 'Fabricaciones', 'Industrias', 'Manufacturas', 'Talleres', 'Fundiciones'];
    const industrialSuffixes = ['Industrial', 'Mecánica', 'Técnica', 'Metalúrgica', 'Constructora', 'Ingeniería', 'Montajes', 'Estructuras', 'Maquinaria', 'Equipos', 'Herramientas', 'Instalaciones', 'Mantenimiento', 'Reparaciones', 'Fabricaciones', 'Industrias', 'Manufacturas', 'Talleres', 'Fundiciones', 'Soldaduras'];

    // Nombres para empresas de servicios
    const serviceNames = ['Asesoría', 'Consultoría', 'Gestión', 'Administración', 'Servicios', 'Soluciones', 'Atención', 'Asistencia', 'Ayuda', 'Soporte', 'Apoyo', 'Orientación', 'Dirección', 'Planificación', 'Organización', 'Coordinación', 'Supervisión', 'Control', 'Evaluación', 'Seguimiento'];
    const serviceAreas = ['Fiscal', 'Contable', 'Laboral', 'Jurídica', 'Legal', 'Financiera', 'Económica', 'Empresarial', 'Comercial', 'Marketing', 'Publicidad', 'Comunicación', 'Recursos Humanos', 'Personal', 'Formación', 'Capacitación', 'Desarrollo', 'Innovación', 'Calidad', 'Medioambiental'];

    // Nombres para empresas comerciales
    const commercialPrefixes = ['Comercial', 'Distribuciones', 'Almacenes', 'Tiendas', 'Supermercados', 'Hipermercados', 'Centros', 'Grandes', 'Pequeños', 'Medianos', 'Exclusivos', 'Selectos', 'Especializados', 'Generales', 'Mayoristas', 'Minoristas', 'Importaciones', 'Exportaciones', 'Representaciones', 'Delegaciones'];
    const commercialProducts = ['Alimentación', 'Bebidas', 'Textil', 'Ropa', 'Calzado', 'Muebles', 'Decoración', 'Hogar', 'Electrodomésticos', 'Electrónica', 'Informática', 'Telefonía', 'Papelería', 'Librería', 'Juguetes', 'Deportes', 'Ferretería', 'Bricolaje', 'Jardinería', 'Mascotas'];

    // Nombres para empresas de construcción
    const constructionPrefixes = ['Construcciones', 'Promociones', 'Edificaciones', 'Obras', 'Proyectos', 'Desarrollos', 'Rehabilitaciones', 'Reformas', 'Restauraciones', 'Conservaciones', 'Mantenimientos', 'Instalaciones', 'Montajes', 'Estructuras', 'Cerramientos', 'Acabados', 'Revestimientos', 'Pavimentos', 'Cubiertas', 'Fachadas'];
    const constructionSuffixes = ['Constructora', 'Inmobiliaria', 'Promotora', 'Urbanizadora', 'Edificadora', 'Rehabilitadora', 'Reformadora', 'Restauradora', 'Conservadora', 'Mantenedora', 'Instaladora', 'Montadora', 'Estructurista', 'Cerradora', 'Acabadora', 'Revestidora', 'Pavimentadora', 'Cubridora', 'Fachadista', 'Decoradora'];

    // Nombres para empresas de alimentación
    const foodPrefixes = ['Alimentos', 'Productos', 'Elaborados', 'Preparados', 'Conservas', 'Congelados', 'Frescos', 'Naturales', 'Ecológicos', 'Biológicos', 'Orgánicos', 'Tradicionales', 'Artesanales', 'Caseros', 'Selectos', 'Gourmet', 'Delicatessen', 'Especialidades', 'Variedades', 'Surtidos'];
    const foodProducts = ['Panadería', 'Pastelería', 'Repostería', 'Confitería', 'Chocolatería', 'Heladería', 'Lácteos', 'Quesos', 'Embutidos', 'Carnes', 'Pescados', 'Mariscos', 'Frutas', 'Verduras', 'Hortalizas', 'Legumbres', 'Cereales', 'Aceites', 'Vinos', 'Licores'];

    // Nombres para empresas de transporte
    const transportPrefixes = ['Transportes', 'Mudanzas', 'Mensajería', 'Paquetería', 'Logística', 'Distribución', 'Reparto', 'Envíos', 'Traslados', 'Portes', 'Fletes', 'Acarreos', 'Cargas', 'Descargas', 'Almacenaje', 'Depósito', 'Custodia', 'Guardamuebles', 'Garaje', 'Aparcamiento'];
    const transportSuffixes = ['Express', 'Rápido', 'Urgente', 'Inmediato', 'Directo', 'Puerta a Puerta', 'Nacional', 'Internacional', 'Europeo', 'Mundial', 'Global', 'Universal', 'Total', 'Integral', 'Completo', 'Seguro', 'Fiable', 'Puntual', 'Económico', 'Barato'];

    // Nombres para empresas de hostelería
    const hospitalityPrefixes = ['Hotel', 'Hostal', 'Pensión', 'Albergue', 'Residencia', 'Apartamentos', 'Camping', 'Bungalows', 'Cabañas', 'Casas', 'Villas', 'Chalets', 'Restaurante', 'Bar', 'Cafetería', 'Pub', 'Discoteca', 'Sala', 'Club', 'Salón'];
    const hospitalitySuffixes = ['Palace', 'Luxury', 'Confort', 'Relax', 'Descanso', 'Vacaciones', 'Ocio', 'Tiempo Libre', 'Turismo', 'Viajes', 'Excursiones', 'Aventura', 'Naturaleza', 'Rural', 'Urbano', 'Playa', 'Montaña', 'Sierra', 'Valle', 'Río'];

    // Nombres para empresas de salud
    const healthPrefixes = ['Centro', 'Clínica', 'Hospital', 'Consultorio', 'Gabinete', 'Instituto', 'Laboratorio', 'Farmacia', 'Óptica', 'Ortopedia', 'Fisioterapia', 'Rehabilitación', 'Psicología', 'Psiquiatría', 'Odontología', 'Dental', 'Médico', 'Sanitario', 'Salud', 'Bienestar'];
    const healthSuffixes = ['Médico', 'Clínico', 'Hospitalario', 'Sanitario', 'Salud', 'Bienestar', 'Vida', 'Vital', 'Saludable', 'Sano', 'Natural', 'Alternativo', 'Complementario', 'Integral', 'Holístico', 'Preventivo', 'Curativo', 'Terapéutico', 'Asistencial', 'Cuidados'];

    // Nombres para empresas de educación
    const educationPrefixes = ['Colegio', 'Escuela', 'Instituto', 'Academia', 'Centro', 'Aula', 'Estudio', 'Taller', 'Formación', 'Capacitación', 'Enseñanza', 'Aprendizaje', 'Educación', 'Instrucción', 'Preparación', 'Entrenamiento', 'Desarrollo', 'Perfeccionamiento', 'Especialización', 'Actualización'];
    const educationSuffixes = ['Educativo', 'Formativo', 'Académico', 'Escolar', 'Docente', 'Pedagógico', 'Didáctico', 'Instructivo', 'Educacional', 'Formacional', 'Capacitador', 'Preparatorio', 'Entrenador', 'Desarrollador', 'Perfeccionador', 'Especializador', 'Actualizador', 'Profesional', 'Técnico', 'Superior'];

    // Elegir un tipo de empresa al azar
    const companyTypes = [{
            prefixes: techPrefixes,
            roots: techRoots,
            suffixes: techSuffixes,
            format: (p, r, s) => `${p}${r} ${s}`
        },
        {
            prefixes: industrialNames,
            suffixes: industrialSuffixes,
            format: (p, s) => `${p} ${s}`
        },
        {
            prefixes: serviceNames,
            suffixes: serviceAreas,
            format: (p, s) => `${p} ${s}`
        },
        {
            prefixes: commercialPrefixes,
            suffixes: commercialProducts,
            format: (p, s) => `${p} ${s}`
        },
        {
            prefixes: constructionPrefixes,
            suffixes: constructionSuffixes,
            format: (p, s) => `${p} ${s}`
        },
        {
            prefixes: foodPrefixes,
            suffixes: foodProducts,
            format: (p, s) => `${p} ${s}`
        },
        {
            prefixes: transportPrefixes,
            suffixes: transportSuffixes,
            format: (p, s) => `${p} ${s}`
        },
        {
            prefixes: hospitalityPrefixes,
            suffixes: hospitalitySuffixes,
            format: (p, s) => `${p} ${s}`
        },
        {
            prefixes: healthPrefixes,
            suffixes: healthSuffixes,
            format: (p, s) => `${p} ${s}`
        },
        {
            prefixes: educationPrefixes,
            suffixes: educationSuffixes,
            format: (p, s) => `${p} ${s}`
        }
    ];

    const companyType = companyTypes[Math.floor(Math.random() * companyTypes.length)];

    if (companyType.roots) {
        const prefix = companyType.prefixes[Math.floor(Math.random() * companyType.prefixes.length)];
        const root = companyType.roots[Math.floor(Math.random() * companyType.roots.length)];
        const suffix = companyType.suffixes[Math.floor(Math.random() * companyType.suffixes.length)];
        return companyType.format(prefix, root, suffix);
    } else {
        const prefix = companyType.prefixes[Math.floor(Math.random() * companyType.prefixes.length)];
        const suffix = companyType.suffixes[Math.floor(Math.random() * companyType.suffixes.length)];
        return companyType.format(prefix, suffix);
    }
}

// Función para generar un CIF aleatorio
function generateCIF() {
    const letters = 'ABCDEFGHJKLMNPQRSUVW';
    const letter = letters[Math.floor(Math.random() * letters.length)];
    const numbers = Array.from({
        length: 8
    }, () => Math.floor(Math.random() * 10)).join('');
    return `${letter}${numbers}`;
}

// Función para generar una dirección aleatoria
function generateAddress() {
    const streetTypes = ['Calle', 'Avenida', 'Plaza', 'Paseo', 'Ronda', 'Vía', 'Camino', 'Carretera', 'Bulevar', 'Travesía'];
    const streetNames = ['Mayor', 'Real', 'Principal', 'Grande', 'Nueva', 'Vieja', 'Alta', 'Baja', 'Larga', 'Corta', 'Ancha', 'Estrecha', 'Norte', 'Sur', 'Este', 'Oeste', 'Central', 'Industrial', 'Comercial', 'Residencial'];
    const cities = ['Madrid', 'Barcelona', 'Valencia', 'Sevilla', 'Zaragoza', 'Málaga', 'Murcia', 'Palma', 'Las Palmas', 'Bilbao', 'Alicante', 'Córdoba', 'Valladolid', 'Vigo', 'Gijón', 'Hospitalet', 'La Coruña', 'Granada', 'Vitoria', 'Elche'];
    const provinces = ['Madrid', 'Barcelona', 'Valencia', 'Sevilla', 'Zaragoza', 'Málaga', 'Murcia', 'Baleares', 'Las Palmas', 'Vizcaya', 'Alicante', 'Córdoba', 'Valladolid', 'Pontevedra', 'Asturias', 'Barcelona', 'La Coruña', 'Granada', 'Álava', 'Alicante'];
    const postalCodes = Array.from({
        length: 5
    }, () => Math.floor(Math.random() * 10)).join('');

    const streetType = streetTypes[Math.floor(Math.random() * streetTypes.length)];
    const streetName = streetNames[Math.floor(Math.random() * streetNames.length)];
    const number = Math.floor(Math.random() * 200) + 1;
    const cityIndex = Math.floor(Math.random() * cities.length);
    const city = cities[cityIndex];
    const province = provinces[cityIndex];

    return {
        street: `${streetType} ${streetName}, ${number}`,
        city: city,
        province: province,
        postalCode: postalCodes
    };
}

// Función para generar un teléfono aleatorio
function generatePhone() {
    const prefixes = ['6', '7', '9'];
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
    const numbers = Array.from({
        length: 8
    }, () => Math.floor(Math.random() * 10)).join('');
    return `${prefix}${numbers}`;
}

// Función para generar un email aleatorio
function generateEmail(companyName) {
    const domains = ['gmail.com', 'hotmail.com', 'yahoo.com', 'outlook.com', 'protonmail.com', 'icloud.com', 'aol.com', 'zoho.com', 'mail.com', 'gmx.com'];
    const domain = domains[Math.floor(Math.random() * domains.length)];

    // Simplificar el nombre de la empresa para el email
    const simplifiedName = companyName.toLowerCase().replace(/[^a-z0-9]/g, '');

    return `info@${simplifiedName}.com`;
}

// Función para generar una fecha aleatoria en los últimos 5 años
function generateDate() {
    const now = new Date();
    const pastDate = new Date(now.getFullYear() - Math.floor(Math.random() * 5), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);
    return pastDate;
}

// Función para generar un nombre de contacto aleatorio
function generateContactName() {
    const firstNames = ['Juan', 'María', 'Pedro', 'Ana', 'Carlos', 'Laura', 'Javier', 'Carmen', 'Miguel', 'Lucía', 'Antonio', 'Isabel', 'José', 'Marta', 'Francisco', 'Elena', 'David', 'Cristina', 'Manuel', 'Raquel'];
    const lastNames = ['García', 'Rodríguez', 'González', 'Fernández', 'López', 'Martínez', 'Sánchez', 'Pérez', 'Gómez', 'Martín', 'Jiménez', 'Ruiz', 'Hernández', 'Díaz', 'Moreno', 'Álvarez', 'Romero', 'Alonso', 'Gutiérrez', 'Navarro'];

    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];

    return `${firstName} ${lastName}`;
}

// Función para generar datos de empresa aleatorios
function generateCompanyData(id) {
    const name = generateCompanyName();
    const address = generateAddress();

    return {
        id: id,
        nombre: name,
        cif: generateCIF(),
        direccion: address.street,
        ciudad: address.city,
        provincia: address.province,
        codigoPostal: address.postalCode,
        telefono: generatePhone(),
        email: generateEmail(name),
        nombreContacto: generateContactName(),
        activo: Math.random() > 0.2 // 80% de probabilidad de estar activo
    };
}

// Función para generar múltiples empresas
function generateCompanies(count) {
    const companies = [];
    for (let i = 1; i <= count; i++) {
        companies.push(generateCompanyData(i));
    }
    return companies;
}

// Generar empresas y guardarlas en localStorage
function saveTestCompanies(count = 500) {
    const companies = generateCompanies(count);
    localStorage.setItem('companies', JSON.stringify(companies));
    console.log(`Se han generado y guardado ${companies.length} empresas de prueba.`);
    return companies;
}

// No ejecutamos la función aquí, se ejecutará desde load_test_data.js