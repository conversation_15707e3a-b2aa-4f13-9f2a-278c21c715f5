/**
 * Módulo para la gestión del tablero (dashboard)
 */
class DashboardManager {
    constructor() {
        this.dashboards = [];
        this.currentDashboardId = null;
        this.dashboard = null;
        this.companyId = null;
        this.dashboardElement = document.getElementById('dashboard');

        // Vincular métodos para que mantengan el contexto correcto
        this.handleWidgetDeleteClick = this.handleWidgetDeleteClick.bind(this);
        this.handleWidgetEditClick = this.handleWidgetEditClick.bind(this);
        this.handleWidgetTypeChange = this.handleWidgetTypeChange.bind(this);

        app['dashboardManager'] = this; // Registrar en app
        // Inicializar el DashboardSelectionManager
        this.selectionManager = new DashboardSelectionManager(this.dashboardElement);
    }


    /**
     * Verifica si el usuario actual es administrador
     * @returns {boolean} - true si el usuario es administrador, false en caso contrario
     */
    isUserAdmin() {
        // Obtener información del usuario actual
        const currentUser = authManager.user;

        // Verificar si el usuario es administrador
        return currentUser && currentUser.tipo === 'admin';
    }

    /**
     * Obtiene un dashboard por su ID
     * @param {number} id - ID del dashboard a obtener
     * @returns {Object|null} - Dashboard encontrado o null si no existe
     */
    getDashboard(id) {
        // Asegurarse de que el ID sea un número
        const dashboardId = parseInt(id, 10);
        console.log(`Buscando dashboard con ID: ${dashboardId} entre ${this.dashboards.length} dashboards`);

        // Buscar el dashboard por ID numérico
        return this.dashboards.find(d => d.id === dashboardId) || null;
    }

    /**
     * Cambia al dashboard especificado
     * @param {number|Object} id - ID del dashboard a usar o el objeto dashboard completo
     * @returns {Promise} - Promesa que se resuelve con true si el cambio fue exitoso, false si no se encontró el dashboard
     */
    useDashboard(id) {
        return new Promise((resolve, reject) => {
            let dashboard;

            if (id && typeof id === 'object') {
                // Si recibimos un objeto dashboard completo
                dashboard = id;
                // Asegurarse de que el ID sea un número
                if (dashboard.id) {
                    dashboard.id = parseInt(dashboard.id, 10);
                }
                console.log(`Usando dashboard pasado como objeto: ID ${dashboard.id}`);
            } else {
                // Si recibimos un ID, convertirlo a número
                const dashboardId = id ? parseInt(id, 10) : null;
                console.log(`Buscando dashboard con ID: ${dashboardId}`);
                dashboard = dashboardId ? this.getDashboard(dashboardId) : this.dashboard;
            }

            if (dashboard) {
                // Mostrar el contenedor del dashboard si no está visible
                const appContainer = document.getElementById('app-container');
                if (appContainer && appContainer.classList.contains('hidden')) {
                    showContainer(appContainer, this);
                }

                // Guardar el dashboard y su ID en las propiedades de la clase
                this.dashboard = dashboard;
                this.currentDashboardId = dashboard.id;
                this.companyId = dashboard.companyId || dashboard.empresaId || this.companyId;

                // Asegurarse de que nextWidgetId esté actualizado antes de renderizar
                widgetManager.updateNextWidgetId();

                // Aplicar la configuración del dashboard
                this.applyDashboardConfig();

                // Renderizar los widgets del nuevo dashboard
                if (this.dashboardElement) {
                    widgetManager.renderWidgets(this.dashboardElement);
                }

                // Actualizar el título con el nombre del tablero
                const headerTitle = document.getElementById('dashboard-title');
                if (headerTitle) {
                    headerTitle.textContent = dashboard.name;
                }

                // Solo después de renderizar, guardar el ID del tablero actual en la base de datos
                dbManager.setCurrentDashboardId(this.companyId, this.currentDashboardId)
                    .then(() => {
                        resolve(true);
                    })
                    .catch(error => {
                        console.error('Error al establecer el tablero actual:', error);
                        reject(error);
                    });
            } else {
                resolve(false);
            }
        });
    }

    /**
     * Inicializa el dashboard
     * @param {number} companyId - ID de la empresa
     * @returns {Promise} - Promesa que se resuelve cuando se ha inicializado el dashboard
     */
    init(companyId) {
        this.companyId = companyId;

        // Cuando se accede desde el menú principal, restablecer el usuario actual
        // al usuario de la aplicación para que los nuevos tableros se asocien a él
        const currentUser = authManager.user;
        this.currentUserId = currentUser.id;
        this.currentUserName = currentUser.nombre;

        console.log(`Inicializando dashboard para usuario: ${this.currentUserId} (${this.currentUserName})`);

        return this.loadDashboard()
            .then(() => {
                // Actualizar la interfaz según los permisos del usuario
                this.updateUIBasedOnPermissions();
                return Promise.resolve();
            })
            .catch(error => {
                console.error('Error al inicializar el dashboard:', error);
                return Promise.reject(error);
            });
    }

    /**
     * Mecanismo para refrescar el dashboard cuando se vuelve a él
     * Implementado para compatibilidad con el sistema de navegación
     * @returns {Promise} - Promesa que se resuelve cuando se ha refrescado el dashboard
     */
    loadEntities() {
        console.log('Refrescando dashboard...');

        // Si no hay dashboard o companyId, no hacer nada
        if (!this.dashboard || !this.companyId) {
            return Promise.resolve();
        }

        // Aplicar la configuración del dashboard
        this.applyDashboardConfig();

        // Renderizar los widgets
        if (this.dashboardElement) {
            widgetManager.renderWidgets(this.dashboardElement);
        }

        return Promise.resolve();
    }

    /**
     * Actualiza la interfaz de usuario según los permisos del usuario actual
     */
    updateUIBasedOnPermissions() {
        // Verificar si el usuario es administrador
        const isAdmin = this.isUserAdmin();

        // Elementos a ocultar para usuarios no administradores
        const elementsToHide = isAdmin ? [] : [
            'quick-new-dashboard', // Botón rápido de nuevo tablero
            'quick-config-dashboard', // Botón rápido de configurar tablero
            'quick-add-widget', // Botón rápido de añadir widget
            'new-dashboard-btn', // Botón de nuevo tablero en el popup de opciones
            'duplicate-dashboard-btn', // Botón de duplicar tablero en el popup de opciones
            'clear-dashboard-btn', // Botón de vaciar tablero en el popup de opciones
            'delete-dashboard-btn', // Botón de eliminar tablero en el popup de opciones
            'config-dashboard-btn', // Botón de configurar tablero en el menú de opciones
            'add-widget-btn', // Botón de añadir widget en el menú de opciones
            'edit-widget-btn', // Botón de editar widget en el menú de opciones
            'delete-widget-btn', // Botón de borrar widget en el menú de opciones
            'move-widget-btn', // Botón de mover widget en el menú de opciones
            'switch-dashboard-btn' // Botón de cambio de tablero en el menú de opciones
        ];

        // Ocultar o mostrar elementos según corresponda
        elementsToHide.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.display = 'none';
            }
        });

        // Modificar el menú de opciones para usuarios no administradores
        if (!isAdmin) {
            // Obtener el menú de opciones
            const optionsMenu = document.getElementById('options-menu');
            if (optionsMenu) {
                // Limpiar el menú
                optionsMenu.innerHTML = '';

                // Añadir solo los botones permitidos
                // Crear el botón de Modo Selección
                const newSelectionModeBtn = document.createElement('button');
                newSelectionModeBtn.id = 'selection-mode-btn';
                newSelectionModeBtn.dataset.accion = 'dashboardSelectionManager.toggleWidgetSelection';
                newSelectionModeBtn.textContent = 'Modo Selección';
                optionsMenu.appendChild(newSelectionModeBtn);

                // Crear el botón de Copiar Seleccionados
                const newCopySelectsBtn = document.createElement('button');
                newCopySelectsBtn.id = 'copy-selecs-btn';
                newCopySelectsBtn.dataset.accion = 'dashboardSelectionManager.copySelectedWidgets';
                newCopySelectsBtn.textContent = 'Copiar Selecs.';
                optionsMenu.appendChild(newCopySelectsBtn);

                // Crear el botón de Pegar
                const newPasteBtn = document.createElement('button');
                newPasteBtn.id = 'paste-widgets-btn';
                newPasteBtn.dataset.accion = 'dashboardSelectionManager.pasteWidgets';
                newPasteBtn.textContent = 'Pegar';
                optionsMenu.appendChild(newPasteBtn);

                // Crear el botón de Eliminar Seleccionados
                const newDeleteSelectsBtn = document.createElement('button');
                newDeleteSelectsBtn.id = 'delete-selecs-btn';
                newDeleteSelectsBtn.dataset.accion = 'dashboardSelectionManager.handleWidgetDeleteSelecsClick';
                newDeleteSelectsBtn.textContent = 'Eliminar Selecs.';
                optionsMenu.appendChild(newDeleteSelectsBtn);

                // Crear el botón de menú principal
                const newMainMenuBtn = document.createElement('button');
                newMainMenuBtn.id = 'main-menu-btn';
                newMainMenuBtn.textContent = 'Menú principal';
                newMainMenuBtn.addEventListener('click', () => {
                    navigateTo('main-menu');
                    closeMenuUniversal();
                });
                optionsMenu.appendChild(newMainMenuBtn);

                // Crear el botón de cerrar sesión
                const newLogoutBtn = document.createElement('button');
                newLogoutBtn.id = 'logout-btn';
                newLogoutBtn.dataset.accion = 'authManager.logout';
                newLogoutBtn.textContent = 'Cerrar sesión';
                optionsMenu.appendChild(newLogoutBtn);
            }

            // Ocultar el botón de opciones de tablero (engranaje)
            const dashboardOptionsBtn = document.getElementById('dashboard-options-btn');
            if (dashboardOptionsBtn) {
                dashboardOptionsBtn.style.display = 'none';
            }
        }
    }

    /**
     * Actualiza la configuración del dashboard
     * @param {Object} config - Nueva configuración
     * @returns {Promise} - Promesa que se resuelve cuando se ha actualizado la configuración
     */
    updateDashboardConfig(config) {
        return new Promise((resolve, reject) => {
            if (!this.dashboard || !this.getEmpresaId()) {
                reject(new Error('No hay dashboard o empresa activa'));
                return;
            }

            // Verificar si cambiaron las dimensiones
            const sizeChanged = (
                config.width !== this.dashboard.width ||
                config.height !== this.dashboard.height
            );

            // Actualizar propiedades
            this.dashboard = {
                ...this.dashboard,
                ...config
            };

            this.dashboard.companyId = this.getEmpresaId();
            this.dashboard.empresaId = this.getEmpresaId();

            // Aplicar configuración
            this.applyDashboardConfig();

            // Guardar configuración
            this.saveDashboard()
                .then(() => {
                    // Si cambiaron las dimensiones, asegurar que los widgets estén dentro del tablero
                    // COMENTADO: No ajustar automáticamente las posiciones para permitir widgets fuera del área visible
                    // if (sizeChanged) {
                    //     widgetManager.ensureWidgetsAreWithinDashboard();
                    // }

                    // Siempre volver a renderizar los widgets para aplicar los nuevos colores y configuraciones
                    const dashboardElement = document.getElementById('dashboard');
                    if (dashboardElement) {
                        widgetManager.renderWidgets(dashboardElement);
                    }

                    resolve();
                })
                .catch(error => {
                    console.error('Error al guardar la configuración del dashboard:', error);
                    reject(error);
                });
        });
    }

    /**
     * Aplica la configuración actual al elemento del dashboard
     */
    applyDashboardConfig() {
        const dashboardElement = document.getElementById('dashboard');
        if (!dashboardElement) return;

        dashboardElement.style.width = `${this.dashboard.width}px`;
        dashboardElement.style.height = `${this.dashboard.height}px`;
        dashboardElement.style.backgroundColor = this.dashboard.backgroundColor;

        // Aplicar configuración de bordes de widgets
        if (this.dashboard.showWidgetBorders) {
            dashboardElement.classList.add('show-widget-borders');
        } else {
            dashboardElement.classList.remove('show-widget-borders');
        }

        // Aplicar configuración de transparencia de widgets
        if (this.dashboard.transparentWidgets) {
            dashboardElement.classList.add('transparent-widgets');
        } else {
            dashboardElement.classList.remove('transparent-widgets');
        }

        // Establecer variables CSS para los colores de texto y fondo de widgets
        if (this.dashboard.widgetTextColor) {
            dashboardElement.style.setProperty('--widget-text-color', this.dashboard.widgetTextColor);
        }

        if (this.dashboard.widgetBgColor) {
            dashboardElement.style.setProperty('--widget-bg-color', this.dashboard.widgetBgColor);
        }

        // Actualizar título del dashboard
        const headerTitle = document.getElementById('dashboard-title');
        if (headerTitle) {
            headerTitle.textContent = this.dashboard.name;
        }

        // Aplicar configuración de cuadrícula según el tema actual
        this.applyGridConfigForCurrentTheme();
    }

    /**
     * Aplica la configuración de cuadrícula según el tema actual
     */
    applyGridConfigForCurrentTheme() {
        // Obtener el tema actual
        const currentTheme = themeManager.getCurrentTheme();

        // Configurar la cuadrícula según el tema actual
        if (currentTheme === 'tron') {
            // Para el tema Tron, establecer color de cuadrícula azul oscuro
            if (!this.dashboard.gridColor || this.dashboard.gridColor === '#dddddd' || this.dashboard.gridColor === '#cbd5e0') {
                this.dashboard.gridColor = '#1a3a5a';
            }

            // Para el tema Tron, activar la cuadrícula por defecto si no se ha configurado explícitamente
            if (this.dashboard.showGrid === undefined) {
                this.dashboard.showGrid = true;
            }
        } else if (currentTheme === 'neumorphic') {
            // Para el tema Neumorphic, establecer color de cuadrícula gris medio
            if (!this.dashboard.gridColor || this.dashboard.gridColor === '#dddddd' || this.dashboard.gridColor === '#1a3a5a' || this.dashboard.gridColor === '#4a6fa5') {
                this.dashboard.gridColor = '#cbd5e0';
            }

            // Para el tema Neumorphic, la cuadrícula es opcional
            if (this.dashboard.showGrid === undefined) {
                this.dashboard.showGrid = false;
            }
        } else if (currentTheme === 'azul_nuboso') {
            // Para el tema Azul Nuboso, establecer color de cuadrícula azul claro
            if (!this.dashboard.gridColor || this.dashboard.gridColor === '#dddddd' || this.dashboard.gridColor === '#1a3a5a' || this.dashboard.gridColor === '#cbd5e0') {
                this.dashboard.gridColor = '#4a6fa5';
            }

            // Para el tema Azul Nuboso, la cuadrícula es opcional
            if (this.dashboard.showGrid === undefined) {
                this.dashboard.showGrid = false;
            }
        } else {
            // Para otros temas, establecer color de cuadrícula gris claro
            if (!this.dashboard.gridColor || this.dashboard.gridColor === '#1a3a5a' || this.dashboard.gridColor === '#cbd5e0' || this.dashboard.gridColor === '#4a6fa5') {
                this.dashboard.gridColor = '#dddddd';
            }
        }

        // Aplicar la configuración de la cuadrícula
        const dashboardElement = document.getElementById('dashboard');
        if (dashboardElement) {
            // Establecer la variable CSS para el color de la cuadrícula
            dashboardElement.style.setProperty('--grid-color', this.dashboard.gridColor);

            // Aplicar o quitar la clase show-grid según la configuración
            if (this.dashboard.showGrid) {
                dashboardElement.classList.add('show-grid');
            } else {
                dashboardElement.classList.remove('show-grid');
            }
        }
    }

    /**
     * Incrementa y devuelve el contador de widgets
     * @returns {Promise} - Promesa que se resuelve con el nuevo valor del contador
     */
    incrementWidgetCount() {
        return new Promise((resolve, reject) => {
            if (!this.dashboard || !this.companyId) {
                reject(new Error('No hay dashboard o empresa activa'));
                return;
            }

            this.dashboard.widgetCount++;
            this.saveDashboard()
                .then(() => resolve(this.dashboard.widgetCount))
                .catch(error => reject(error));
        });
    }

    /**
     * Guarda la configuración de los dashboards en la base de datos
     * @returns {Promise} - Promesa que se resuelve cuando se ha guardado el dashboard
     */
    saveDashboard() {
        return new Promise((resolve, reject) => {
            if (!this.dashboard || !this.getEmpresaId()) {
                reject(new Error('No hay dashboard o empresa activa'));
                return;
            }

            // Asegurarse de que los widgets estén actualizados en el tablero actual
            this.dashboard.widgets = widgetManager.getWidgets();

            // Actualizar fecha de modificación y usuario
            const currentUser = authManager.user;
            this.dashboard.fechaModificacion = new Date().toISOString();
            this.dashboard.usuarioModificacionId = currentUser.id;

            // Guardar el dashboard en la base de datos
            dbManager.saveDashboard(this.dashboard, this.getEmpresaId())
                .then(() => resolve())
                .catch(error => reject(error));
        });
    }

    /**
     * Carga la configuración de los dashboards desde la base de datos
     * @returns {Promise} - Promesa que se resuelve cuando se han cargado los dashboards
     */
    loadDashboard() {
        return new Promise((resolve, reject) => {
            if (!this.companyId) {
                reject(new Error('No hay empresa activa'));
                return;
            }

            // Cargar los dashboards de la empresa
            dbManager.getDashboards(this.companyId)
                .then(dashboards => {
                    this.dashboards = dashboards;

                    // Si no hay dashboards, crear uno por defecto
                    if (this.dashboards.length === 0) {
                        // Obtener el usuario actual
                        const currentUser = authManager.user;

                        const now = new Date().toISOString();
                        const defaultDashboard = {
                            id: 1,
                            name: 'Tablero principal',
                            width: 800,
                            height: 600,
                            backgroundColor: '#ffffff',
                            showWidgetBorders: true,
                            transparentWidgets: false,
                            showGrid: true,
                            gridColor: '#1a3a5a',
                            widgetTextColor: '#000000',
                            widgetBgColor: '#ffffff',
                            widgetCount: 0,
                            theme: themeManager.getCurrentTheme(),
                            widgets: [],
                            // Asociar el tablero al usuario actual
                            userId: currentUser.id,
                            userName: currentUser.nombre,
                            // Campos de auditoría
                            fechaAlta: now,
                            fechaModificacion: now,
                            usuarioAltaId: currentUser.id,
                            usuarioModificacionId: currentUser.id
                        };

                        this.dashboards.push(defaultDashboard);
                        this.dashboard = defaultDashboard;
                        this.currentDashboardId = 1;

                        // Guardar el dashboard por defecto
                        return dbManager.saveDashboard(defaultDashboard, this.companyId)
                            .then(() => dbManager.setCurrentDashboardId(this.companyId, 1));
                    }

                    // Obtener el ID del dashboard actual
                    return dbManager.getCurrentDashboardId(this.companyId)
                        .then(currentId => {
                            // Si hay un ID guardado y existe ese dashboard, usarlo
                            if (currentId && this.getDashboard(currentId)) {
                                this.dashboard = this.getDashboard(currentId);
                                this.currentDashboardId = currentId;
                            } else if (this.dashboards.length > 0) {
                                // Si no hay ID guardado o no existe ese dashboard, usar el primero
                                this.dashboard = this.dashboards[0];
                                this.currentDashboardId = this.dashboard.id;

                                // Guardar el ID del dashboard actual
                                return dbManager.setCurrentDashboardId(this.companyId, this.currentDashboardId);
                            }

                            return Promise.resolve();
                        });
                })
                .then(() => {
                    // Asegurarse de que nextWidgetId esté actualizado antes de renderizar
                    widgetManager.updateNextWidgetId();

                    resolve();
                })
                .catch(error => {
                    console.error('Error al cargar los dashboards:', error);
                    reject(error);
                });
        });
    }

    /**
     * Activa el modo de edición para mover widgets
     */
    activateMoveMode() {
        if (this.dashboardElement.classList.contains('edit-mode')) return;
        this.dashboardElement.classList.add('edit-mode');
    }

    /**
     * Desactiva el modo de edición
     */
    deactivateEditMode() {
        if (!this.dashboardElement.classList.contains('edit-mode')) return;
        const dashboardElement = document.getElementById('dashboard');
        dashboardElement.classList.remove('edit-mode');
        dashboardElement.classList.remove('delete-mode');
        dashboardElement.classList.remove('edit-widget-mode');

        // Eliminar eventos de clic para editar widgets
        const widgets = dashboardElement.querySelectorAll('.widget');
        widgets.forEach(widget => {
            widget.removeEventListener('click', this.handleWidgetEditClick);
        });
    }

    /**
     * Activa el modo de edición de widgets
     */
    activateEditWidgetMode() {
        const dashboardElement = document.getElementById('dashboard');
        dashboardElement.classList.add('edit-widget-mode');

        // Añadir evento de clic a los widgets para editarlos
        const widgets = dashboardElement.querySelectorAll('.widget');
        widgets.forEach(widget => {
            widget.addEventListener('click', this.handleWidgetEditClick);
        });
    }

    /**
     * Activa el modo de eliminación de widgets
     */
    activateDeleteMode() {
        const dashboardElement = document.getElementById('dashboard');
        dashboardElement.classList.add('delete-mode');

        // Añadir evento de clic a los widgets para eliminarlos
        const widgets = dashboardElement.querySelectorAll('.widget');
        widgets.forEach(widget => {
            widget.addEventListener('click', this.handleWidgetDeleteClick);
        });
    }

    /**
     * Maneja el clic en un widget en modo de eliminación
     * @param {Event} e - Evento de clic
     */
    handleWidgetDeleteClick(e) {
        if (!this.dashboardElement.classList.contains('delete-mode')) return;
        const widgetElement = e.currentTarget;
        const widgetId = parseInt(widgetElement.dataset.widgetId);
        if (!widgetId) return;

        dialogManager.confirmDelete('¿Estás seguro de que deseas eliminar este widget?').then(confirmed => {
            if (confirmed) {
                widgetManager.deleteWidget(widgetId);
                widgetElement.remove();
            }
        });
    }

    /**
     * Maneja el clic en un widget en modo de edición
     * @param {Event} e - Evento de clic
     */
    handleWidgetEditClick(e) {
        // Simplemente llamar a openWidgetEditDialog con el evento
        this.openWidgetEditDialog(e);
    }

    /**
     * Maneja el cambio de tipo de widget en el formulario de edición
     * Esta función se mantiene por compatibilidad pero ya no se utiliza directamente
     */
    handleWidgetTypeChange() {
        // Esta función ya no se utiliza directamente, ya que ahora cada instancia del diálogo
        // tiene su propio manejador de eventos configurado en openWidgetEditDialog()
        console.log('handleWidgetTypeChange: Esta función ya no se utiliza directamente');
    }

    /**
     * Obtiene la configuración actual del dashboard
     * @returns {Object} - Configuración del dashboard
     */
    getDashboardConfig() {
        return {
            ...this.dashboard
        };
    }

    /**
     * Obtiene el ID de la empresa actual
     * Primero intenta obtener el ID de la empresa del dashboard actual,
     * y si no existe, devuelve el ID de empresa almacenado en la clase
     * @returns {number} - ID de la empresa actual
     */
    getEmpresaId() {
        // Si existe el dashboard actual y tiene empresaId, devolverlo
        if (this.dashboard && this.dashboard.empresaId) {
            return this.dashboard.empresaId;
        }

        // Si no, devolver el companyId almacenado en la clase
        return this.companyId;
    }

    /**
     * Crea un nuevo tablero vacío con la misma configuración que el actual
     * @returns {Promise} - Promesa que se resuelve con el ID del nuevo tablero
     */
    createNewDashboard() {
        return new Promise((resolve, reject) => {
            if (!this.dashboard || !this.companyId) {
                reject(new Error('No hay dashboard o empresa activa'));
                return;
            }

            // Generar un nuevo ID (el máximo ID actual + 1)
            const newId = this.dashboards.length > 0 ?
                Math.max(...this.dashboards.map(d => d.id)) + 1 :
                1;

            // Obtener el usuario al que asociar el tablero
            // Usar this.currentUserId si está definido, o el usuario actual de la aplicación
            const currentUser = authManager.user;
            const userIdToUse = this.currentUserId || currentUser.id;

            // Obtener el nombre del usuario
            let userNameToUse = currentUser.nombre;
            if (this.currentUserId && this.currentUserId !== currentUser.id) {
                // Si tenemos un usuario específico, intentar obtener su nombre
                userNameToUse = this.currentUserName || 'Usuario';
            }

            console.log(`Creando tablero para usuario: ${userIdToUse} (${userNameToUse})`);

            // Crear un nuevo tablero con la misma configuración que el actual
            const now = new Date().toISOString();
            const newDashboard = {
                ...this.dashboard,
                id: newId,
                name: `Tablero ${newId}`,
                widgetCount: 0,
                widgets: [], // Inicializar con array vacío de widgets
                // Asociar el tablero al usuario correspondiente
                userId: userIdToUse,
                userName: userNameToUse,
                // Campos de auditoría
                fechaAlta: now,
                fechaModificacion: now,
                usuarioAltaId: currentUser.id,
                usuarioModificacionId: currentUser.id
            };

            // Añadir el nuevo tablero a la lista
            this.dashboards.push(newDashboard);

            // Guardar el nuevo tablero en la base de datos
            dbManager.saveDashboard(newDashboard, this.companyId)
                .then(() => resolve(newId))
                .catch(error => reject(error));
        });
    }

    /**
     * Duplica el tablero actual
     * @returns {Promise} - Promesa que se resuelve con el ID del tablero duplicado
     */
    duplicateDashboard() {
        return new Promise((resolve, reject) => {
            if (!this.dashboard || !this.companyId) {
                reject(new Error('No hay dashboard o empresa activa'));
                return;
            }

            // Generar un nuevo ID (el máximo ID actual + 1)
            const newId = this.dashboards.length > 0 ?
                Math.max(...this.dashboards.map(d => d.id)) + 1 :
                1;

            // Obtener los widgets actuales
            const currentWidgets = widgetManager.getWidgets();
            const duplicatedWidgets = currentWidgets.map(widget => ({
                ...widget,
                id: widget.id + '_dup_' + newId // Crear IDs únicos para los widgets duplicados
            }));

            // Crear una copia del tablero actual
            const duplicatedDashboard = {
                ...this.dashboard,
                id: newId,
                name: `(Copia de) ${this.dashboard.name}`,
                widgetCount: this.dashboard.widgetCount,
                widgets: duplicatedWidgets, // Incluir los widgets duplicados
                // Mantener la asociación de usuario del tablero original
                // Al duplicar un tablero, la copia mantiene el mismo usuario que el original
                userId: this.dashboard.userId,
                userName: this.dashboard.userName
            };

            // Añadir el tablero duplicado a la lista
            this.dashboards.push(duplicatedDashboard);

            // Guardar el tablero duplicado en la base de datos
            dbManager.saveDashboard(duplicatedDashboard, this.companyId)
                .then(() => resolve(newId))
                .catch(error => reject(error));
        });
    }

    /**
     * Elimina el tablero actual
     * @returns {Promise} - Promesa que se resuelve con true si se eliminó correctamente
     */
    deleteDashboard() {
        return new Promise((resolve, reject) => {
            if (!this.dashboard || !this.companyId) {
                reject(new Error('No hay dashboard o empresa activa'));
                return;
            }

            // No permitir eliminar si solo hay un tablero
            if (this.dashboards.length <= 1) {
                resolve(false);
                return;
            }

            // Encontrar el índice del tablero actual
            const currentIndex = this.dashboards.findIndex(d => d.id === this.currentDashboardId);
            const dashboardToDeleteId = this.currentDashboardId;

            // Eliminar el tablero de la lista
            this.dashboards.splice(currentIndex, 1);

            // Determinar el próximo tablero a usar
            let nextDashboardId;
            if (currentIndex > 0) {
                // Usar el tablero anterior
                nextDashboardId = this.dashboards[currentIndex - 1].id;
            } else {
                // Usar el siguiente tablero (que ahora es el primero)
                nextDashboardId = this.dashboards[0].id;
            }

            // Eliminar el tablero de la base de datos
            dbManager.deleteDashboard(dashboardToDeleteId, this.companyId)
                .then(() => {
                    // Cambiar al próximo tablero
                    return this.useDashboard(nextDashboardId);
                })
                .then(() => {
                    // Eliminar los widgets del tablero eliminado
                    widgetManager.clearWidgetsForDashboard(dashboardToDeleteId);
                    resolve(true);
                })
                .catch(error => {
                    console.error('Error al eliminar el tablero:', error);
                    reject(error);
                });
        });
    }

    /**
     * Abre el diálogo de edición de un widget
     * @param {number|Event} widgetIdOrEvent - ID del widget a editar o evento de clic
     */
    openWidgetEditDialog(widgetIdOrEvent) {
        let widgetId;

        // Determinar si se pasó un ID o un evento
        if (typeof widgetIdOrEvent === 'object' && widgetIdOrEvent.currentTarget) {
            // Es un evento, obtener el ID del widget del elemento
            const widgetElement = widgetIdOrEvent.currentTarget;
            widgetId = parseInt(widgetElement.dataset.widgetId);

            // Verificar si estamos en modo de edición
            const dashboardElement = document.getElementById('dashboard');
            if (!dashboardElement.classList.contains('edit-widget-mode')) return;
        } else {
            // Es un ID directo
            widgetId = widgetIdOrEvent;

            // Desactivar cualquier modo de edición activo
            this.deactivateEditMode();
        }

        // Obtener el widget
        const widget = widgetManager.getWidget(widgetId);
        if (!widget) return;

        // Obtener el template original del diálogo
        const originalModal = document.getElementById('edit-widget-modal');
        if (!originalModal) {
            console.error('No se encontró el modal original edit-widget-modal');
            return;
        }

        // Crear un ID único para el nuevo diálogo
        const uniqueId = `edit-widget-modal-${Date.now()}`;

        // Clonar el nodo del diálogo
        const editWidgetModal = originalModal.cloneNode(true);
        editWidgetModal.id = uniqueId;

        // Guardar el ID del widget en el dataset del modal para poder recuperarlo después
        editWidgetModal.dataset.widgetId = widgetId;

        /**
         * Actualiza el título del widget con el texto visible de la opción seleccionada en un select
         * Solo si el campo de título está vacío, coincide con alguna opción del select, o si se fuerza la actualización
         * @param {HTMLElement} selectElement - Elemento select que contiene la opción seleccionada
         * @param {HTMLElement} container - Contenedor donde buscar el campo de título
         * @param {boolean} forceUpdate - Si es true, actualiza el título independientemente de si ya tiene valor
         */
        const updateTitleFromSelect = (selectElement, container, forceUpdate = false) => {
            if (!selectElement || selectElement.selectedIndex === -1) return;

            // Buscar el campo de título generado dinámicamente por el acordeón
            // El ID puede ser edit-widget-title o simplemente widget-title dependiendo del prefijo
            const titleInput = container.querySelector('[data-param-name="title"]') ||
                container.querySelector('#edit-widget-title') ||
                container.querySelector('#widget-title');

            if (!titleInput) {
                console.warn('No se encontró el campo de título en el formulario');
                return;
            }

            // Obtener el texto de la opción seleccionada
            const selectedOption = selectElement.options[selectElement.selectedIndex];
            const optionText = selectedOption.textContent;

            // Verificar si el título actual coincide con alguna de las opciones del select
            let titleMatchesOption = false;
            if (titleInput.value) {
                for (let i = 0; i < selectElement.options.length; i++) {
                    if (titleInput.value === selectElement.options[i].textContent) {
                        titleMatchesOption = true;
                        break;
                    }
                }
            }

            // Actualizar el título si:
            // - Se fuerza la actualización, O
            // - El título está vacío, O
            // - El título actual coincide con alguna opción del select (lo que indica que no ha sido personalizado)
            if (forceUpdate || !titleInput.value || titleMatchesOption) {
                titleInput.value = optionText;
            }
        };

        // Configurar un único evento para actualizar el título cuando cambian los selectores de fuente de datos
        on(editWidgetModal, 'change', '.es_data_source', (ev) => {
            // Solo procesar el evento si el selector está visible (no está dentro de un contenedor oculto)
            if (!ev.target.closest('.widget-fields.hidden')) {
                updateTitleFromSelect(ev.target, editWidgetModal, false);
            }
        });

        // Función para actualizar la lista de series actuales
        const updateSeriesList = (widget, modal) => {
            const seriesListContainer = modal.querySelector('#current-series-list');
            if (!seriesListContainer) return;

            if (!widget.params.series || widget.params.series.length === 0) {
                seriesListContainer.innerHTML = 'No hay series añadidas';
                return;
            }

            let html = '<ul class="series-list">';
            widget.params.series.forEach((serie, index) => {
                // Generar un color aleatorio para esta serie (igual que en el renderizado)
                const hue = ((index + 1) * 137) % 360;
                const color = `hsl(${hue}, 70%, 50%)`;

                html += `<li data-series-index="${index}">
                    <span class="series-color" style="background-color: ${color}"></span>
                    <span class="series-name">${serie.name || serie.type}</span>
                    <button class="remove-series-item-btn" title="Eliminar serie" aria-label="Eliminar serie" data-series-index="${index}">−</button>
                </li>`;
            });
            html += '</ul>';

            seriesListContainer.innerHTML = html;
        };

        // Configurar eventos para los botones de añadir y eliminar series
        on(editWidgetModal, 'click', '.add-series-btn', () => {
            const widgetId = parseInt(editWidgetModal.dataset.widgetId);
            if (!widgetId) return;

            const widget = widgetManager.getWidget(widgetId);
            if (!widget) return;

            // Obtener el selector de tipo de serie
            const seriesTypeSelect = editWidgetModal.querySelector('#edit-chart-series-type');
            if (!seriesTypeSelect || seriesTypeSelect.selectedIndex === -1) {
                console.warn('No se ha seleccionado ningún tipo de serie');
                return;
            }

            // Obtener el tipo de serie seleccionado
            const seriesType = seriesTypeSelect.value;
            if (!seriesType) {
                console.warn('Tipo de serie no válido');
                return;
            }

            // Inicializar el array de series si no existe
            if (!widget.params.series) {
                widget.params.series = [];
            }

            // Obtener información del tipo de dato
            const valueTypeInfo = dataService.getValueTypes().find(type => type.id === seriesType);
            const serieName = valueTypeInfo ? valueTypeInfo.name : seriesType;

            // Añadir la nueva serie
            widget.params.series.push({
                type: seriesType,
                name: serieName
            });

            // Limpiar el selector para la próxima serie
            seriesTypeSelect.selectedIndex = 0;

            // Actualizar la lista de series actuales
            updateSeriesList(widget, editWidgetModal);

            // Actualizar el widget en tiempo real
            widgetManager.updateWidget(widgetId, widget);

            // Destruir el objeto chart si existe para forzar su recreación
            if (widget.chart) {
                widget.chart.destroy();
                widget.chart = null;
            }

            // Renderizar los widgets para reflejar los cambios inmediatamente
            const dashboardElement = document.getElementById('dashboard');
            if (dashboardElement) {
                widgetManager.renderWidgets(dashboardElement);
            }
        });

        // Manejador para el botón de eliminar serie específica (en cada fila)
        on(editWidgetModal, 'click', '.remove-series-item-btn', (ev) => {
            const widgetId = parseInt(editWidgetModal.dataset.widgetId);
            if (!widgetId) return;

            const widget = widgetManager.getWidget(widgetId);
            if (!widget || !widget.params.series || widget.params.series.length === 0) {
                console.warn('No hay series para eliminar');
                return;
            }

            // Obtener el índice de la serie a eliminar
            const seriesIndex = parseInt(ev.target.dataset.seriesIndex);
            if (isNaN(seriesIndex) || seriesIndex < 0 || seriesIndex >= widget.params.series.length) {
                console.warn('Índice de serie no válido:', seriesIndex);
                return;
            }

            // Eliminar la serie específica
            widget.params.series.splice(seriesIndex, 1);

            // Actualizar la lista de series actuales
            updateSeriesList(widget, editWidgetModal);

            // Actualizar el widget en tiempo real
            widgetManager.updateWidget(widgetId, widget);

            // Destruir el objeto chart si existe para forzar su recreación
            if (widget.chart) {
                widget.chart.destroy();
                widget.chart = null;
            }

            // Renderizar los widgets para reflejar los cambios inmediatamente
            const dashboardElement = document.getElementById('dashboard');
            if (dashboardElement) {
                widgetManager.renderWidgets(dashboardElement);
            }
        });

        // Actualizar los IDs de los elementos internos
        const widgetTypeSelect = editWidgetModal.querySelector('#edit-widget-type');
        if (widgetTypeSelect) {
            widgetTypeSelect.id = `edit-widget-type-${uniqueId}`;
        }

        // El contenedor principal para los campos de widget debe existir en el HTML
        const widgetFieldsContainer = editWidgetModal.querySelector('.widget-fields-container');
        if (!widgetFieldsContainer) {
            console.error('ERROR: No se encontró el contenedor principal .widget-fields-container en el HTML del modal');
            throw new Error('Falta el elemento .widget-fields-container en el HTML del modal. Este elemento es obligatorio.');
        }

        // Ya no es necesario ocultar los campos aquí, ya que fillWidgetFields() se encarga de crear los nuevos campos
        // y el sistema de acordeón maneja la visibilidad de los campos

        // NO actualizar los IDs de los inputs internos para evitar problemas con los selectores
        // Solo actualizar los IDs de los elementos principales como el select de tipo de widget
        // Los demás elementos se buscarán dentro del contenedor clonado

        // Obtener referencias a los botones sin cambiar sus IDs
        const saveEditedWidgetBtn = editWidgetModal.querySelector('#save-edited-widget');
        const duplicateWidgetBtn = editWidgetModal.querySelector('#duplicate-widget');
        const deleteWidgetFromModalBtn = editWidgetModal.querySelector('#delete-widget-from-modal-btn');

        // Actualizar IDs de los botones de reset de colores
        const resetBgColorBtn = editWidgetModal.querySelector('#reset-widget-bg-color');
        if (resetBgColorBtn) {
            resetBgColorBtn.id = `reset-widget-bg-color-${uniqueId}`;
        }

        const resetTextColorBtn = editWidgetModal.querySelector('#reset-widget-text-color');
        if (resetTextColorBtn) {
            resetTextColorBtn.id = `reset-widget-text-color-${uniqueId}`;
        }

        const resetBorderColorBtn = editWidgetModal.querySelector('#reset-widget-border-color');
        if (resetBorderColorBtn) {
            resetBorderColorBtn.id = `reset-widget-border-color-${uniqueId}`;
        }

        // Restablecer el título del modal a "Editar Widget"
        const modalTitle = editWidgetModal.querySelector('h2');
        if (modalTitle) {
            modalTitle.textContent = 'Editar Widget';
        }

        // Llenar el formulario con los datos del widget
        const nameInput = editWidgetModal.querySelector(`#edit-widget-name-${uniqueId}`);
        if (nameInput) {
            nameInput.value = widget.name;
        }

        // Establecer el tipo de widget en el selector
        if (widgetTypeSelect) {
            widgetTypeSelect.value = widget.type;

            // Configurar el evento de cambio de tipo de widget
            widgetTypeSelect.addEventListener('change', () => {
                // Ya no es necesario ocultar los campos aquí, ya que fillWidgetFields() se encarga de crear los nuevos campos
                // y el sistema de acordeón maneja la visibilidad de los campos

                // Obtener el nuevo tipo seleccionado
                const newWidgetType = widgetTypeSelect.value;

                // Ya no es necesario mostrar los campos específicos aquí, ya que fillWidgetFields() se encarga de crear los nuevos campos
                // y el sistema de acordeón maneja la visibilidad de los campos

                // Obtener dimensiones predeterminadas para el nuevo tipo de widget
                const tempWidthInput = {
                    value: widget.width
                };
                const tempHeightInput = {
                    value: widget.height
                };

                // Usar la función setDefaultWidgetDimensions para obtener las dimensiones predeterminadas
                widgetManager.setDefaultWidgetDimensions(newWidgetType, tempWidthInput, tempHeightInput);

                // Crear un widget temporal con el nuevo tipo y dimensiones
                const tempWidget = {
                    ...widget,
                    type: newWidgetType,
                    width: parseInt(tempWidthInput.value),
                    height: parseInt(tempHeightInput.value)
                };

                // Llenar los campos con valores por defecto
                app.widgetFieldsManager.fillWidgetFields(tempWidget, true, editWidgetModal);

                // Si el widget era de tipo "text" y ahora es de otro tipo, actualizar el título
                if (widget.type === 'text' && newWidgetType !== 'text') {
                    // Buscar el selector de fuente de datos visible y actualizar el título basado en él
                    // Solo seleccionar elementos .es_data_source que no estén dentro de un contenedor oculto
                    const visibleDataSourceSelect = editWidgetModal.querySelector('.es_data_source');

                    if (visibleDataSourceSelect) {
                        // Forzar la actualización del título cuando se cambia de text a otro tipo
                        updateTitleFromSelect(visibleDataSourceSelect, editWidgetModal, true);
                    }
                }

            });
        }
        // Llenar los campos con los datos del widget
        app.widgetFieldsManager.fillWidgetFields(widget, true, editWidgetModal);

        // Si es un widget de gráfica, actualizar la lista de series
        if (widget.type === 'chart') {
            // Esperar a que el DOM se actualice
            setTimeout(() => {
                updateSeriesList(widget, editWidgetModal);
            }, 100);
        }

        // Establecer colores personalizados si existen
        const bgColorInput = editWidgetModal.querySelector('#edit-widget-bg-color');
        const textColorInput = editWidgetModal.querySelector('#edit-widget-text-color');
        const borderColorInput = editWidgetModal.querySelector('#edit-widget-border-color');

        if (bgColorInput && textColorInput && borderColorInput) {
            // Inicializar los manejadores de eventos para los selectores de color
            if (typeof initColorPickerHandlers === 'function') {
                initColorPickerHandlers(editWidgetModal);
            }

            // Resetear los atributos data-reset y data-defecto
            if (typeof resetColorInputsDataset === 'function') {
                resetColorInputsDataset(editWidgetModal);
            } else {
                // Fallback si la función no está disponible
                bgColorInput.dataset.reset = 'false';
                bgColorInput.dataset.defecto = 'false';
                textColorInput.dataset.reset = 'false';
                textColorInput.dataset.defecto = 'false';
                borderColorInput.dataset.reset = 'false';
                borderColorInput.dataset.defecto = 'false';
            }

            if (widget.style) {
                // Color de fondo
                if (widget.style.backgroundColor === "defecto") {
                    // Mostrar el color de fondo de widgets del dashboard
                    const widgetBgColor = this.dashboard.widgetBgColor || '#ffffff';
                    bgColorInput.value = widgetBgColor;
                    bgColorInput.dataset.defecto = 'true';
                    bgColorInput.dataset.value = ''; // Limpiar el valor personalizado

                    console.log('Inicializado color de fondo como defecto:', {
                        value: bgColorInput.value,
                        dataset: {
                            defecto: bgColorInput.dataset.defecto,
                            value: bgColorInput.dataset.value
                        }
                    });
                } else if (widget.style.backgroundColor) {
                    bgColorInput.value = widget.style.backgroundColor;
                    bgColorInput.dataset.defecto = 'false';
                    bgColorInput.dataset.value = widget.style.backgroundColor;

                    console.log('Inicializado color de fondo con valor personalizado:', {
                        value: bgColorInput.value,
                        dataset: {
                            defecto: bgColorInput.dataset.defecto,
                            value: bgColorInput.dataset.value
                        }
                    });
                } else {
                    bgColorInput.value = '#ffffff'; // Color por defecto
                    bgColorInput.dataset.defecto = 'true';
                    bgColorInput.dataset.value = '';

                    console.log('Inicializado color de fondo con valor por defecto:', {
                        value: bgColorInput.value,
                        dataset: {
                            defecto: bgColorInput.dataset.defecto,
                            value: bgColorInput.dataset.value
                        }
                    });
                }

                // Color de texto
                if (widget.style.textColor === "defecto") {
                    // Determinar el color de texto según el tema actual
                    const theme = document.documentElement.classList.contains('theme-tron') ? 'tron' :
                        (document.documentElement.classList.contains('theme-neumorphic') ? 'neumorphic' :
                            (document.documentElement.classList.contains('theme-azul_nuboso') ? 'azul_nuboso' : 'default'));

                    let textColor = '#000000'; // Color por defecto
                    if (theme === 'tron') {
                        textColor = '#ffffff'; // Color de texto para tema Tron
                    } else if (theme === 'neumorphic') {
                        textColor = '#2d3748'; // Color de texto para tema Neumorphic
                    } else if (theme === 'azul_nuboso') {
                        textColor = '#333333'; // Color de texto para tema Azul Nuboso
                    }

                    textColorInput.value = textColor;
                    textColorInput.dataset.defecto = 'true';
                    textColorInput.dataset.value = ''; // Limpiar el valor personalizado

                    console.log('Inicializado color de texto como defecto:', {
                        value: textColorInput.value,
                        dataset: {
                            defecto: textColorInput.dataset.defecto,
                            value: textColorInput.dataset.value
                        }
                    });
                } else if (widget.style.textColor) {
                    textColorInput.value = widget.style.textColor;
                    textColorInput.dataset.defecto = 'false';
                    textColorInput.dataset.value = widget.style.textColor;

                    console.log('Inicializado color de texto con valor personalizado:', {
                        value: textColorInput.value,
                        dataset: {
                            defecto: textColorInput.dataset.defecto,
                            value: textColorInput.dataset.value
                        }
                    });
                } else {
                    textColorInput.value = '#000000'; // Color por defecto
                    textColorInput.dataset.defecto = 'true';
                    textColorInput.dataset.value = '';

                    console.log('Inicializado color de texto con valor por defecto:', {
                        value: textColorInput.value,
                        dataset: {
                            defecto: textColorInput.dataset.defecto,
                            value: textColorInput.dataset.value
                        }
                    });
                }

                // Color de borde
                if (widget.style.borderColor === "defecto") {
                    // Determinar el color de borde según el tema actual
                    const theme = document.documentElement.classList.contains('theme-tron') ? 'tron' :
                        (document.documentElement.classList.contains('theme-neumorphic') ? 'neumorphic' :
                            (document.documentElement.classList.contains('theme-azul_nuboso') ? 'azul_nuboso' : 'default'));

                    let borderColor = '#cccccc'; // Color por defecto
                    if (theme === 'tron') {
                        borderColor = '#00a2ff'; // Color de borde para tema Tron
                    } else if (theme === 'neumorphic') {
                        borderColor = '#cbd5e0'; // Color de borde para tema Neumorphic
                    } else if (theme === 'azul_nuboso') {
                        borderColor = '#4a6fa5'; // Color de borde para tema Azul Nuboso
                    }

                    borderColorInput.value = borderColor;
                    borderColorInput.dataset.defecto = 'true';
                    borderColorInput.dataset.value = ''; // Limpiar el valor personalizado

                    console.log('Inicializado color de borde como defecto:', {
                        value: borderColorInput.value,
                        dataset: {
                            defecto: borderColorInput.dataset.defecto,
                            value: borderColorInput.dataset.value
                        }
                    });
                } else if (widget.style.borderColor) {
                    borderColorInput.value = widget.style.borderColor;
                    borderColorInput.dataset.defecto = 'false';
                    borderColorInput.dataset.value = widget.style.borderColor;

                    console.log('Inicializado color de borde con valor personalizado:', {
                        value: borderColorInput.value,
                        dataset: {
                            defecto: borderColorInput.dataset.defecto,
                            value: borderColorInput.dataset.value
                        }
                    });
                } else {
                    borderColorInput.value = '#cccccc'; // Color por defecto
                    borderColorInput.dataset.defecto = 'true';
                    borderColorInput.dataset.value = '';

                    console.log('Inicializado color de borde con valor por defecto:', {
                        value: borderColorInput.value,
                        dataset: {
                            defecto: borderColorInput.dataset.defecto,
                            value: borderColorInput.dataset.value
                        }
                    });
                }
            }

            // Configurar eventos para los botones de reset de colores
            if (resetBgColorBtn) {
                resetBgColorBtn.addEventListener('click', () => {
                    // Mostrar el color de fondo de widgets del dashboard pero guardar "defecto"
                    const widgetBgColor = this.dashboard.widgetBgColor || '#ffffff';
                    bgColorInput.value = widgetBgColor;
                    bgColorInput.dataset.reset = 'true';
                    bgColorInput.dataset.defecto = 'true';
                    bgColorInput.dataset.value = ''; // Limpiar el valor personalizado

                    console.log('Reset de color de fondo:', {
                        value: bgColorInput.value,
                        dataset: {
                            reset: bgColorInput.dataset.reset,
                            defecto: bgColorInput.dataset.defecto,
                            value: bgColorInput.dataset.value
                        }
                    });

                    // Disparar un evento de cambio para que se actualice la vista previa
                    bgColorInput.dispatchEvent(new Event('change'));
                });
            }

            if (resetTextColorBtn) {
                resetTextColorBtn.addEventListener('click', () => {
                    // Usar el color de texto del tablero
                    const widgetTextColor = this.dashboard.widgetTextColor || '#000000';
                    textColorInput.value = widgetTextColor;
                    textColorInput.dataset.reset = 'true';
                    textColorInput.dataset.defecto = 'true';
                    textColorInput.dataset.value = ''; // Limpiar el valor personalizado

                    console.log('Reset de color de texto:', {
                        value: textColorInput.value,
                        dataset: {
                            reset: textColorInput.dataset.reset,
                            defecto: textColorInput.dataset.defecto,
                            value: textColorInput.dataset.value
                        }
                    });

                    // Disparar un evento de cambio para que se actualice la vista previa
                    textColorInput.dispatchEvent(new Event('change'));
                });
            }

            if (resetBorderColorBtn) {
                resetBorderColorBtn.addEventListener('click', () => {
                    // Obtener el color de borde según el tema actual para mostrarlo
                    const theme = document.documentElement.classList.contains('theme-tron') ? 'tron' :
                        (document.documentElement.classList.contains('theme-neumorphic') ? 'neumorphic' :
                            (document.documentElement.classList.contains('theme-azul_nuboso') ? 'azul_nuboso' : 'default'));

                    let borderColor = '#cccccc'; // Color por defecto
                    if (theme === 'tron') {
                        borderColor = '#00a2ff'; // Color de borde para tema Tron
                    } else if (theme === 'neumorphic') {
                        borderColor = '#cbd5e0'; // Color de borde para tema Neumorphic
                    } else if (theme === 'azul_nuboso') {
                        borderColor = '#4a6fa5'; // Color de borde para tema Azul Nuboso
                    }

                    borderColorInput.value = borderColor;
                    borderColorInput.dataset.reset = 'true';
                    borderColorInput.dataset.defecto = 'true';
                    borderColorInput.dataset.value = ''; // Limpiar el valor personalizado

                    console.log('Reset de color de borde:', {
                        value: borderColorInput.value,
                        dataset: {
                            reset: borderColorInput.dataset.reset,
                            defecto: borderColorInput.dataset.defecto,
                            value: borderColorInput.dataset.value
                        }
                    });

                    // Disparar un evento de cambio para que se actualice la vista previa
                    borderColorInput.dispatchEvent(new Event('change'));
                });
            }
        }

        // Guardar el ID del widget que se está editando
        editWidgetModal.dataset.widgetId = widgetId;

        // Configurar el evento del botón de guardar
        if (saveEditedWidgetBtn) {
            // Eliminar cualquier manejador de eventos existente
            const newSaveBtn = saveEditedWidgetBtn.cloneNode(true);
            if (saveEditedWidgetBtn.parentNode) {
                saveEditedWidgetBtn.parentNode.replaceChild(newSaveBtn, saveEditedWidgetBtn);
            }

            // Añadir el nuevo manejador de eventos
            newSaveBtn.addEventListener('click', () => {
                console.log('Botón de guardar clickeado');
                const widgetId = parseInt(editWidgetModal.dataset.widgetId);
                if (!widgetId) return;

                const widget = widgetManager.getWidget(widgetId);
                if (!widget) return;

                // Obtener los inputs de color
                const bgColorInput = editWidgetModal.querySelector('#edit-widget-bg-color');
                const textColorInput = editWidgetModal.querySelector('#edit-widget-text-color');
                const borderColorInput = editWidgetModal.querySelector('#edit-widget-border-color');

                // Determinar los valores de color a guardar
                let bgColor = "defecto";
                let textColor = "defecto";
                let borderColor = "defecto";

                // Si el input tiene dataset.defecto = 'true', usar "defecto"
                // Si tiene dataset.value (establecido por el evento input/change), usar ese valor
                // Si no tiene ninguno de los anteriores, mantener el valor original del widget
                if (bgColorInput.dataset.defecto === 'true') {
                    bgColor = "defecto";
                } else if (bgColorInput.dataset.value) {
                    bgColor = bgColorInput.dataset.value;
                } else if (widget.style && widget.style.backgroundColor) {
                    bgColor = widget.style.backgroundColor;
                }

                if (textColorInput.dataset.defecto === 'true') {
                    textColor = "defecto";
                } else if (textColorInput.dataset.value) {
                    textColor = textColorInput.dataset.value;
                } else if (widget.style && widget.style.textColor) {
                    textColor = widget.style.textColor;
                }

                if (borderColorInput.dataset.defecto === 'true') {
                    borderColor = "defecto";
                } else if (borderColorInput.dataset.value) {
                    borderColor = borderColorInput.dataset.value;
                } else if (widget.style && widget.style.borderColor) {
                    borderColor = widget.style.borderColor;
                }

                // Preparar objeto con los cambios
                const nameInput = editWidgetModal.querySelector('#edit-widget-name');
                const widgetConfig = {
                    name: nameInput ? nameInput.value : widget.name,
                    style: {
                        backgroundColor: bgColor,
                        textColor: textColor,
                        borderColor: borderColor
                    },
                    params: {}
                };

                // Obtener el tipo de widget seleccionado (puede haber cambiado)
                const widgetType = widgetTypeSelect ? widgetTypeSelect.value : widget.type;

                // Actualizar el tipo en la configuración si ha cambiado
                if (widgetType !== widget.type) {
                    widgetConfig.type = widgetType;

                    // Obtener dimensiones predeterminadas para el nuevo tipo de widget
                    const tempWidthInput = {
                        value: widget.width
                    };
                    const tempHeightInput = {
                        value: widget.height
                    };

                    // Usar la función setDefaultWidgetDimensions para obtener las dimensiones predeterminadas
                    widgetManager.setDefaultWidgetDimensions(widgetType, tempWidthInput, tempHeightInput);

                    // Actualizar dimensiones según el nuevo tipo
                    widgetConfig.width = parseInt(tempWidthInput.value);
                    widgetConfig.height = parseInt(tempHeightInput.value);
                }

                // Obtener los parámetros del widget
                widgetConfig.params = app.widgetFieldsManager.getWidgetParams(widgetType, true, editWidgetModal);

                // Preservar el array de series si existe en el widget original y no en los nuevos parámetros
                if (widgetType === 'chart' && widget.params && widget.params.series &&
                    (!widgetConfig.params.series || widgetConfig.params.series.length === 0)) {
                    console.log('Preservando series existentes al guardar widget:', widget.params.series);
                    widgetConfig.params.series = [...widget.params.series];
                }

                // Actualizar el widget
                widgetManager.updateWidget(widgetId, widgetConfig);

                // Renderizar los widgets para reflejar los cambios inmediatamente
                const dashboardElement = document.getElementById('dashboard');
                if (dashboardElement) {
                    widgetManager.renderWidgets(dashboardElement);
                }

                // Cerrar y eliminar el modal clonado
                closeModal(editWidgetModal, true, () => {
                    // Eliminar el modal del DOM después de cerrarlo
                    if (editWidgetModal.parentNode) {
                        editWidgetModal.parentNode.removeChild(editWidgetModal);
                    }
                });

                // Desactivar modo de edición
                this.deactivateEditMode();
            });
        }

        // Configurar el evento del botón de duplicar
        if (duplicateWidgetBtn) {
            // Eliminar cualquier manejador de eventos existente
            const newDuplicateBtn = duplicateWidgetBtn.cloneNode(true);
            if (duplicateWidgetBtn.parentNode) {
                duplicateWidgetBtn.parentNode.replaceChild(newDuplicateBtn, duplicateWidgetBtn);
            }

            // Añadir el nuevo manejador de eventos
            newDuplicateBtn.addEventListener('click', () => {
                console.log('Botón de duplicar clickeado');
                const widgetId = parseInt(editWidgetModal.dataset.widgetId);
                if (!widgetId) return;

                // Duplicar el widget
                widgetManager.duplicateWidget(widgetId)
                    .then(newWidget => {
                        if (!newWidget) return;

                        // Actualizar el ID del widget en el modal para que ahora se refiera al nuevo widget
                        editWidgetModal.dataset.widgetId = newWidget.id;

                        // Actualizar el título del modal para indicar que ahora se está editando el widget duplicado
                        const modalTitle = editWidgetModal.querySelector('h2');
                        if (modalTitle) {
                            modalTitle.textContent = 'Editar Widget (Duplicado)';
                        }

                        // Actualizar el nombre en el formulario para reflejar que es un duplicado
                        const nameInput = editWidgetModal.querySelector('#edit-widget-name');
                        if (nameInput) {
                            nameInput.value = newWidget.name;
                        }

                        // Renderizar los widgets para reflejar los cambios inmediatamente
                        const dashboardElement = document.getElementById('dashboard');
                        if (dashboardElement) {
                            widgetManager.renderWidgets(dashboardElement);
                        }
                    })
                    .catch(error => {
                        console.error("Error al duplicar widget:", error);
                    });
            });
        }

        // Configurar el evento del botón de eliminar
        if (deleteWidgetFromModalBtn) {
            // Eliminar cualquier manejador de eventos existente
            const newDeleteBtn = deleteWidgetFromModalBtn.cloneNode(true);
            if (deleteWidgetFromModalBtn.parentNode) {
                deleteWidgetFromModalBtn.parentNode.replaceChild(newDeleteBtn, deleteWidgetFromModalBtn);
            }

            // Añadir el nuevo manejador de eventos
            newDeleteBtn.addEventListener('click', () => {
                console.log('Botón de eliminar clickeado');
                const widgetId = parseInt(editWidgetModal.dataset.widgetId);
                if (!widgetId) return;

                // Pedir confirmación
                dialogManager.confirmDelete('¿Estás seguro de que deseas eliminar este widget?').then(confirmed => {
                    if (confirmed) {
                        // Eliminar el widget
                        widgetManager.deleteWidget(widgetId);

                        // Renderizar los widgets para reflejar los cambios inmediatamente
                        const dashboardElement = document.getElementById('dashboard');
                        if (dashboardElement) {
                            widgetManager.renderWidgets(dashboardElement);
                        }

                        // Cerrar y eliminar el modal clonado
                        closeModal(editWidgetModal, true, () => {
                            // Eliminar el modal del DOM después de cerrarlo
                            if (editWidgetModal.parentNode) {
                                editWidgetModal.parentNode.removeChild(editWidgetModal);
                            }
                        });

                        // Desactivar modo de edición
                        this.deactivateEditMode();
                    }
                });
            });
        }

        // Configurar el botón de cierre
        const closeBtn = editWidgetModal.querySelector('.close-btn');
        if (closeBtn) {
            // Eliminar cualquier manejador de eventos existente
            const newCloseBtn = closeBtn.cloneNode(true);
            if (closeBtn.parentNode) {
                closeBtn.parentNode.replaceChild(newCloseBtn, closeBtn);
            }

            // Añadir el nuevo manejador de eventos
            newCloseBtn.addEventListener('click', () => {
                console.log('Botón de cierre clickeado');
                closeModal(editWidgetModal, true, () => {
                    // Eliminar el modal del DOM después de cerrarlo
                    if (editWidgetModal.parentNode) {
                        editWidgetModal.parentNode.removeChild(editWidgetModal);
                    }
                });

                // Desactivar modo de edición
                this.deactivateEditMode();
            });
        }

        // Añadir el modal clonado al body
        document.body.appendChild(editWidgetModal);

        // Preparar el modal para que sea arrastrable
        const modalContent = editWidgetModal.querySelector('.modal-content');
        if (modalContent) {
            // Centrar el modal
            modalContent.style.top = '50%';
            modalContent.style.left = '50%';
            modalContent.style.transform = 'translate(-50%, -50%)';
            modalContent.style.zIndex = '1001'; // Asegurar que esté por encima del fondo del modal

            // Buscar el encabezado para usarlo como "agarradera"
            const handle = modalContent.querySelector('h2');

            // Hacer el modal arrastrable
            if (typeof makeDraggable === 'function') {
                makeDraggable(modalContent, handle);
            }
        }

        // Mostrar el modal
        openModal(editWidgetModal);
    }

    //Handle doble clic sobre un widget*/
    handleWidgetDoubleClick(e) {
        e.stopPropagation();
        e.preventDefault();
        // Evitar editar si se hace doble clic en el manejador de redimensionamiento
        if (e.target.classList.contains('resize-handle') || e.target.closest('.resize-handle')) {
            return;
        }

        // Evitar editar si se hace doble clic en el botón de datos de la gráfica de períodos
        if (e.target.classList.contains('period-data-button') || e.target.closest('.period-data-button')) {
            return;
        }

        //Bucar el nodo .widget que es quien tiene el id del widget
        let widgetElement;
        if (!e.target.dataset.widgetId) {
            widgetElement = e.target.closest('.widget');
        } else
            widgetElement = e.target;


        // Obtener el ID del widget
        const widgetId = parseInt(widgetElement.dataset.widgetId);

        // Abrir el diálogo de edición
        dashboardManager.openWidgetEditDialog(widgetId);
    }
}
