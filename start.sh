#!/bin/bash

# Script para iniciar el servidor IPRA_I

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Cargar configuración
if [ ! -f "config.conf" ]; then
    error "Archivo config.conf no encontrado. Ejecuta configure.sh primero."
fi

source config.conf

# Configurar entorno regional
if [ -n "$LC_NUMERIC" ]; then
    export LC_NUMERIC="$LC_NUMERIC"
    log "Configuración regional: LC_NUMERIC=$LC_NUMERIC"
fi

# Función para verificar si el puerto está disponible
check_port() {
    local port="$1"
    
    if command -v netstat &> /dev/null; then
        if netstat -tuln | grep -q ":$port "; then
            return 1
        fi
    elif command -v ss &> /dev/null; then
        if ss -tuln | grep -q ":$port "; then
            return 1
        fi
    fi
    
    return 0
}

# Función para verificar dependencias
check_dependencies() {
    log "Verificando dependencias..."
    
    # Verificar Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js no está instalado. Ejecuta install.sh primero."
    fi
    
    # Verificar dependencias del backend
    if [ ! -d "backend/node_modules" ]; then
        error "Dependencias del backend no instaladas. Ejecuta install.sh primero."
    fi
    
    # Verificar archivo del servidor
    if [ ! -f "backend/server.js" ]; then
        error "Archivo del servidor no encontrado: backend/server.js"
    fi
    
    log "Dependencias verificadas correctamente"
}

# Función para crear directorios de logs
create_log_dirs() {
    mkdir -p "$LOG_DIR"
    mkdir -p "backend/logs"
    log "Directorios de logs creados"
}

# Función para iniciar con systemd
start_with_systemd() {
    log "Iniciando servicio con systemd..."
    
    if ! systemctl is-enabled ipra-i &>/dev/null; then
        warn "Servicio systemd no está habilitado. Ejecuta install.sh como root."
        return 1
    fi
    
    if systemctl is-active ipra-i &>/dev/null; then
        warn "El servicio ya está ejecutándose"
        systemctl status ipra-i --no-pager
        return 0
    fi
    
    sudo systemctl start ipra-i
    
    # Esperar un momento para que inicie
    sleep 2
    
    if systemctl is-active ipra-i &>/dev/null; then
        log "Servicio iniciado correctamente"
        systemctl status ipra-i --no-pager
        return 0
    else
        error "Error al iniciar el servicio"
        return 1
    fi
}

# Función para iniciar manualmente
start_manually() {
    log "Iniciando servidor manualmente..."
    
    # Verificar si ya está ejecutándose
    if ! check_port "$SERVER_PORT"; then
        warn "El puerto $SERVER_PORT ya está en uso"
        echo -n -e "${YELLOW}¿Continuar de todas formas? (y/N): ${NC}"
        read continue_anyway
        if [[ ! "$continue_anyway" =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # Cambiar al directorio del backend
    cd backend
    
    # Iniciar servidor
    log "Iniciando servidor en puerto $SERVER_PORT..."
    log "Presiona Ctrl+C para detener el servidor"
    log "URL: http://$SERVER_HOST:$SERVER_PORT"
    
    # Ejecutar servidor
    node server.js
}

# Función para iniciar en background
start_background() {
    log "Iniciando servidor en background..."
    
    # Verificar si ya está ejecutándose
    if ! check_port "$SERVER_PORT"; then
        warn "El puerto $SERVER_PORT ya está en uso"
        return 1
    fi
    
    # Crear archivo PID
    local pid_file="$LOG_DIR/ipra-i.pid"
    
    # Cambiar al directorio del backend
    cd backend
    
    # Iniciar servidor en background
    nohup node server.js > "../$LOG_DIR/server.log" 2>&1 &
    local server_pid=$!
    
    # Guardar PID
    echo $server_pid > "../$pid_file"
    
    # Esperar un momento para verificar que inició correctamente
    sleep 2
    
    if kill -0 $server_pid 2>/dev/null; then
        log "Servidor iniciado en background (PID: $server_pid)"
        log "URL: http://$SERVER_HOST:$SERVER_PORT"
        log "Logs: $LOG_DIR/server.log"
        log "Para detener: ./stop.sh"
    else
        error "Error al iniciar el servidor"
        rm -f "../$pid_file"
        return 1
    fi
}

# Función para mostrar ayuda
show_help() {
    echo "Uso: $0 [OPCIONES]"
    echo ""
    echo "Opciones:"
    echo "  -h, --help       Mostrar esta ayuda"
    echo "  -s, --systemd    Iniciar con systemd (requiere permisos sudo)"
    echo "  -b, --background Iniciar en background"
    echo "  -f, --foreground Iniciar en primer plano (por defecto)"
    echo ""
    echo "Ejemplos:"
    echo "  $0               # Iniciar en primer plano"
    echo "  $0 -b            # Iniciar en background"
    echo "  $0 -s            # Iniciar con systemd"
    echo ""
}

# Función principal
main() {
    log "=== INICIANDO SERVIDOR IPRA_I ==="
    
    # Verificar configuración
    if [ -z "$SERVER_PORT" ]; then
        error "SERVER_PORT no configurado en config.conf"
    fi
    
    check_dependencies
    create_log_dirs
    
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        -s|--systemd)
            if start_with_systemd; then
                log "Servidor iniciado con systemd"
            else
                warn "No se pudo iniciar con systemd, intentando manualmente..."
                start_manually
            fi
            ;;
        -b|--background)
            start_background
            ;;
        -f|--foreground|"")
            start_manually
            ;;
        *)
            error "Opción desconocida: $1. Usa -h para ayuda."
            ;;
    esac
}

# Ejecutar función principal
main "$@"
