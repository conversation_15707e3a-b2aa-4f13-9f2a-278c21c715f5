#!/bin/bash

# Script para verificar el estado del servidor IPRA_I

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para logging
log() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARN] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

status_ok() {
    echo -e "${GREEN}✓ $1${NC}"
}

status_fail() {
    echo -e "${RED}✗ $1${NC}"
}

status_warn() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

# Cargar configuración
if [ ! -f "config.conf" ]; then
    error "Archivo config.conf no encontrado. Ejecuta configure.sh primero."
    exit 1
fi

source config.conf

# Función para verificar configuración
check_config() {
    echo -e "${BLUE}=== CONFIGURACIÓN ===${NC}"
    
    if [ -n "$SERVER_PORT" ]; then
        status_ok "Puerto configurado: $SERVER_PORT"
    else
        status_fail "Puerto no configurado"
    fi
    
    if [ -n "$SERVER_HOST" ]; then
        status_ok "Host configurado: $SERVER_HOST"
    else
        status_fail "Host no configurado"
    fi
    
    if [ -n "$LC_NUMERIC" ]; then
        status_ok "Configuración regional: $LC_NUMERIC"
    else
        status_warn "Configuración regional no establecida"
    fi
    
    echo ""
}

# Función para verificar dependencias
check_dependencies() {
    echo -e "${BLUE}=== DEPENDENCIAS ===${NC}"
    
    # Node.js
    if command -v node &> /dev/null; then
        local node_version=$(node --version)
        status_ok "Node.js: $node_version"
    else
        status_fail "Node.js no instalado"
    fi
    
    # npm
    if command -v npm &> /dev/null; then
        local npm_version=$(npm --version)
        status_ok "npm: $npm_version"
    else
        status_fail "npm no instalado"
    fi
    
    # Dependencias del backend
    if [ -d "backend/node_modules" ]; then
        status_ok "Dependencias del backend instaladas"
    else
        status_fail "Dependencias del backend no instaladas"
    fi
    
    # Dependencias del frontend
    if [ -d "node_modules" ]; then
        status_ok "Dependencias del frontend instaladas"
    else
        status_fail "Dependencias del frontend no instaladas"
    fi
    
    # WeasyPrint
    if [ -d "venv" ]; then
        if source venv/bin/activate && python -c "import weasyprint" 2>/dev/null; then
            status_ok "WeasyPrint disponible"
            deactivate
        else
            status_warn "WeasyPrint no funciona correctamente"
            deactivate 2>/dev/null || true
        fi
    else
        status_warn "Entorno virtual Python no encontrado"
    fi
    
    echo ""
}

# Función para verificar archivos
check_files() {
    echo -e "${BLUE}=== ARCHIVOS ===${NC}"
    
    # Archivos principales
    local files=(
        "backend/server.js:Servidor backend"
        "js/frontend.js:Cliente WebSocket"
        "index.html:Página principal"
        "config.conf:Configuración"
    )
    
    for file_info in "${files[@]}"; do
        local file=$(echo "$file_info" | cut -d':' -f1)
        local desc=$(echo "$file_info" | cut -d':' -f2)
        
        if [ -f "$file" ]; then
            status_ok "$desc: $file"
        else
            status_fail "$desc no encontrado: $file"
        fi
    done
    
    # Directorios
    local dirs=(
        "$BUILD_DIR:Directorio de build"
        "$LOG_DIR:Directorio de logs"
        "$DOCS_DIR:Directorio de documentación"
    )
    
    for dir_info in "${dirs[@]}"; do
        local dir=$(echo "$dir_info" | cut -d':' -f1)
        local desc=$(echo "$dir_info" | cut -d':' -f2)
        
        if [ -d "$dir" ]; then
            status_ok "$desc: $dir"
        else
            status_warn "$desc no existe: $dir"
        fi
    done
    
    # Archivos de build
    if [ -f "$BUILD_DIR/$COMBINED_JS_FILE" ]; then
        local js_size=$(du -h "$BUILD_DIR/$COMBINED_JS_FILE" | cut -f1)
        status_ok "JavaScript combinado: $js_size"
    else
        status_warn "JavaScript combinado no encontrado"
    fi
    
    if [ -f "$BUILD_DIR/$COMBINED_CSS_FILE" ]; then
        local css_size=$(du -h "$BUILD_DIR/$COMBINED_CSS_FILE" | cut -f1)
        status_ok "CSS combinado: $css_size"
    else
        status_warn "CSS combinado no encontrado"
    fi
    
    echo ""
}

# Función para verificar procesos
check_processes() {
    echo -e "${BLUE}=== PROCESOS ===${NC}"
    
    local running=false
    
    # Verificar systemd
    if systemctl is-enabled ipra-i &>/dev/null; then
        if systemctl is-active ipra-i &>/dev/null; then
            status_ok "Servicio systemd: activo"
            running=true
        else
            status_warn "Servicio systemd: inactivo"
        fi
    else
        status_warn "Servicio systemd no configurado"
    fi
    
    # Verificar por PID
    local pid_file="$LOG_DIR/ipra-i.pid"
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
            status_ok "Proceso PID: $pid (ejecutándose)"
            running=true
        else
            status_warn "Archivo PID existe pero proceso no ejecutándose"
        fi
    else
        status_warn "Archivo PID no encontrado"
    fi
    
    # Verificar por puerto
    local port_used=false
    if command -v netstat &> /dev/null; then
        if netstat -tuln | grep -q ":$SERVER_PORT "; then
            port_used=true
        fi
    elif command -v ss &> /dev/null; then
        if ss -tuln | grep -q ":$SERVER_PORT "; then
            port_used=true
        fi
    fi
    
    if [ "$port_used" = true ]; then
        status_ok "Puerto $SERVER_PORT: en uso"
        running=true
    else
        status_warn "Puerto $SERVER_PORT: libre"
    fi
    
    # Buscar procesos Node.js relacionados
    local node_processes=$(pgrep -f "node.*server.js" 2>/dev/null || true)
    if [ -n "$node_processes" ]; then
        status_ok "Procesos Node.js encontrados: $node_processes"
        running=true
    else
        status_warn "No se encontraron procesos Node.js del servidor"
    fi
    
    # Estado general
    if [ "$running" = true ]; then
        status_ok "Estado general: EJECUTÁNDOSE"
    else
        status_fail "Estado general: DETENIDO"
    fi
    
    echo ""
}

# Función para verificar conectividad
check_connectivity() {
    echo -e "${BLUE}=== CONECTIVIDAD ===${NC}"
    
    local url="http://$SERVER_HOST:$SERVER_PORT"
    
    # Verificar endpoint de salud
    if command -v curl &> /dev/null; then
        if curl -s --connect-timeout 5 "$url/health" > /dev/null 2>&1; then
            status_ok "Endpoint de salud accesible: $url/health"
            
            # Obtener información del endpoint
            local health_info=$(curl -s "$url/health" 2>/dev/null || echo "{}")
            if echo "$health_info" | grep -q "ok"; then
                status_ok "Servidor respondiendo correctamente"
            else
                status_warn "Servidor responde pero con estado incierto"
            fi
        else
            status_fail "Endpoint de salud no accesible: $url/health"
        fi
    elif command -v wget &> /dev/null; then
        if wget -q --timeout=5 --tries=1 -O /dev/null "$url/health" 2>/dev/null; then
            status_ok "Endpoint de salud accesible: $url/health"
        else
            status_fail "Endpoint de salud no accesible: $url/health"
        fi
    else
        status_warn "curl/wget no disponible, no se puede verificar conectividad HTTP"
    fi
    
    # Verificar WebSocket (básico)
    if command -v nc &> /dev/null; then
        if nc -z "$SERVER_HOST" "$SERVER_PORT" 2>/dev/null; then
            status_ok "Puerto WebSocket accesible: $SERVER_HOST:$SERVER_PORT"
        else
            status_fail "Puerto WebSocket no accesible: $SERVER_HOST:$SERVER_PORT"
        fi
    else
        status_warn "netcat no disponible, no se puede verificar puerto WebSocket"
    fi
    
    echo ""
}

# Función para verificar logs
check_logs() {
    echo -e "${BLUE}=== LOGS ===${NC}"
    
    # Log del servidor
    local server_log="$LOG_DIR/server.log"
    if [ -f "$server_log" ]; then
        local log_size=$(du -h "$server_log" | cut -f1)
        local log_lines=$(wc -l < "$server_log")
        status_ok "Log del servidor: $server_log ($log_size, $log_lines líneas)"
        
        # Verificar errores recientes
        local recent_errors=$(tail -100 "$server_log" | grep -i error | wc -l)
        if [ "$recent_errors" -gt 0 ]; then
            status_warn "Errores recientes en log: $recent_errors"
        else
            status_ok "Sin errores recientes en log"
        fi
    else
        status_warn "Log del servidor no encontrado: $server_log"
    fi
    
    # Logs de systemd
    if systemctl is-enabled ipra-i &>/dev/null; then
        local systemd_errors=$(journalctl -u ipra-i --since "1 hour ago" --no-pager -q | grep -i error | wc -l)
        if [ "$systemd_errors" -gt 0 ]; then
            status_warn "Errores en systemd (última hora): $systemd_errors"
        else
            status_ok "Sin errores en systemd (última hora)"
        fi
    fi
    
    echo ""
}

# Función para mostrar resumen
show_summary() {
    echo -e "${BLUE}=== RESUMEN ===${NC}"
    
    local url="http://$SERVER_HOST:$SERVER_PORT"
    
    echo "Configuración:"
    echo "  URL: $url"
    echo "  WebSocket: ws://$SERVER_HOST:$SERVER_PORT"
    echo "  Regional: $LC_NUMERIC"
    echo ""
    
    echo "Directorios:"
    echo "  Build: $BUILD_DIR"
    echo "  Logs: $LOG_DIR"
    echo "  Docs: $DOCS_DIR"
    echo ""
    
    echo "Comandos útiles:"
    echo "  Iniciar: ./start.sh"
    echo "  Detener: ./stop.sh"
    echo "  Logs: ./logs.sh"
    echo "  Build: npm run build"
    echo ""
}

# Función para mostrar ayuda
show_help() {
    echo "Uso: $0 [OPCIONES]"
    echo ""
    echo "Opciones:"
    echo "  -h, --help       Mostrar esta ayuda"
    echo "  -q, --quick      Verificación rápida (solo procesos)"
    echo "  -f, --full       Verificación completa (por defecto)"
    echo "  -s, --summary    Solo mostrar resumen"
    echo ""
    echo "Ejemplos:"
    echo "  $0               # Verificación completa"
    echo "  $0 -q            # Verificación rápida"
    echo "  $0 -s            # Solo resumen"
    echo ""
}

# Función para verificación rápida
quick_check() {
    echo -e "${BLUE}=== VERIFICACIÓN RÁPIDA ===${NC}"
    
    check_processes
    
    # URL del servidor
    local url="http://$SERVER_HOST:$SERVER_PORT"
    echo "URL del servidor: $url"
    echo ""
}

# Función principal
main() {
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        -q|--quick)
            quick_check
            ;;
        -s|--summary)
            show_summary
            ;;
        -f|--full|"")
            log "=== ESTADO DEL SERVIDOR IPRA_I ==="
            echo ""
            check_config
            check_dependencies
            check_files
            check_processes
            check_connectivity
            check_logs
            show_summary
            ;;
        *)
            error "Opción desconocida: $1. Usa -h para ayuda."
            exit 1
            ;;
    esac
}

# Ejecutar función principal
main "$@"
