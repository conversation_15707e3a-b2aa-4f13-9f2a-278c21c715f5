/* Estilos adicionales para el tema default */

/* Estilos para diálogos */
.theme-default .dialog-content {
    background-color: #fff;
    border: 1px solid #ddd;
}

.theme-default .dialog-header {
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
}

.theme-default .dialog-header h3 {
    color: #333;
}

.theme-default .dialog-header .close-btn:hover {
    color: #e53935;
}

.theme-default .primary-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

.theme-default .primary-btn:hover {
    background-color: #45a049;
}

.theme-default .secondary-btn {
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

.theme-default .secondary-btn:hover {
    background-color: #e9e9e9;
}

.theme-default .menu-button {
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
}

/* Estilo del borde para el tema Default */
.theme-default .menu-border {
    border: 2px solid var(--primary-color);
    box-shadow: 0 2px 5px var(--shadow-color);
}

/* Estilo para el botón de logout en tema Default */
.theme-default .logout-btn {
    color: var(--primary-color);
}

.theme-default .logout-btn:hover {
    background-color: #f0f0f0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.theme-default .logout-btn:active {
    background-color: var(--primary-color);
    color: white;
}

.theme-default .entity-table {
    border: 2px solid var(--primary-color);
    box-shadow: 0 2px 5px var(--shadow-color);
}

.theme-default .entity-table th {
    background-color: var(--primary-color);
    color: white;
    border-bottom: 1px solid #eee;
    border-right: 1px solid #eee;
    text-transform: none;
    padding: 15px 10px;
}

.theme-default .entity-table th:last-child {
    border-right: none;
}

.theme-default .entity-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.theme-default .entity-table tr:hover td {
    background-color: #f0f0f0;
}

.theme-default .entity-table td {
    border-right: 1px solid #eee;
    border-bottom: 1px solid #eee;
    padding: 12px 10px;
}

.theme-default .entity-table td:last-child {
    border-right: none;
}

.theme-default .entity-action-menu {
    background-color: #fff;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 8px var(--shadow-color);
    border-radius: 4px;
}

.theme-default .entity-action-menu button {
    padding: 10px 15px;
    transition: all 0.2s ease;
    color: #2E7D32;
    /* Verde más oscuro para el texto */
    background-color: white;
}

.theme-default .entity-action-menu button:hover {
    background-color: #2E7D32;
    /* Verde más oscuro para el hover */
    color: white;
}

.theme-default .entity-action-menu button:active {
    background-color: #388E3C;
    /* Verde intermedio para el active */
    color: white;
}

.theme-default .entity-action-menu .danger-option {
    color: var(--error-color);
}

.theme-default .entity-action-menu .danger-option:hover {
    background-color: var(--error-color);
    color: white;
}

.theme-default .entity-action-menu .danger-option:active {
    background-color: rgba(var(--error-color-rgb), 0.7);
    color: white;
}

.theme-default .entity-action-menu button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.theme-default .entity-action-menu button.disabled:hover,
.theme-default .entity-action-menu button.disabled:active {
    background-color: white;
    color: #2E7D32;
    /* Verde más oscuro para mantener consistencia */
}

.theme-default .entity-action-menu .danger-option.disabled:hover,
.theme-default .entity-action-menu .danger-option.disabled:active {
    background-color: white;
    color: var(--error-color);
}

.theme-default .entity-table .checkbox-cell {
    width: 40px;
    vertical-align: middle;
    text-align: center;
    padding-left: 0;
    padding-right: 0;
}

.theme-default .entity-table .action-cell {
    width: 40px;
    vertical-align: middle;
    text-align: center;
    padding-left: 0;
    padding-right: 0;
}

/* Estilos para checkboxes en tema Default */
.theme-default .form-group input[type="checkbox"] {
    margin-right: 0;
}