@echo off
REM Script para verificar el estado del servidor IPRA_I (Windows)

setlocal enabledelayedexpansion

REM Colores para Windows
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "NC=[0m"

REM Función para logging
:log
echo %GREEN%[INFO] %~1%NC%
goto :eof

:warn
echo %YELLOW%[WARN] %~1%NC%
goto :eof

:error
echo %RED%[ERROR] %~1%NC%
goto :eof

:status_ok
echo %GREEN%✓ %~1%NC%
goto :eof

:status_fail
echo %RED%✗ %~1%NC%
goto :eof

:status_warn
echo %YELLOW%⚠ %~1%NC%
goto :eof

REM Cargar configuración
set "CONFIG_FILE=%~dp0config.conf"
if not exist "%CONFIG_FILE%" (
    call :error "Archivo config.conf no encontrado. Ejecuta scripts\configure.bat primero."
    exit /b 1
)

REM Leer configuración
for /f "tokens=1,2 delims==" %%a in ('type "%CONFIG_FILE%" ^| findstr /v "^#" ^| findstr "="') do (
    set "%%a=%%b"
)

REM Procesar argumentos
if "%~1"=="-h" goto :show_help
if "%~1"=="--help" goto :show_help
if "%~1"=="-q" goto :quick_check
if "%~1"=="--quick" goto :quick_check
if "%~1"=="-s" goto :show_summary
if "%~1"=="--summary" goto :show_summary

REM Verificación completa por defecto
call :log "=== ESTADO DEL SERVIDOR IPRA_I ==="
echo.

call :check_config
call :check_dependencies
call :check_files
call :check_processes
call :check_connectivity
call :show_summary

goto :eof

:check_config
echo %BLUE%=== CONFIGURACIÓN ===%NC%

if not "%SERVER_PORT%"=="" (
    call :status_ok "Puerto configurado: %SERVER_PORT%"
) else (
    call :status_fail "Puerto no configurado"
)

if not "%SERVER_HOST%"=="" (
    call :status_ok "Host configurado: %SERVER_HOST%"
) else (
    call :status_fail "Host no configurado"
)

if not "%LC_NUMERIC%"=="" (
    call :status_ok "Configuración regional: %LC_NUMERIC%"
) else (
    call :status_warn "Configuración regional no establecida"
)

echo.
goto :eof

:check_dependencies
echo %BLUE%=== DEPENDENCIAS ===%NC%

REM Node.js
node --version >nul 2>&1
if not errorlevel 1 (
    for /f %%i in ('node --version') do call :status_ok "Node.js: %%i"
) else (
    call :status_fail "Node.js no instalado"
)

REM npm
npm --version >nul 2>&1
if not errorlevel 1 (
    for /f %%i in ('npm --version') do call :status_ok "npm: %%i"
) else (
    call :status_fail "npm no instalado"
)

REM Dependencias del backend
if exist "backend\node_modules" (
    call :status_ok "Dependencias del backend instaladas"
) else (
    call :status_fail "Dependencias del backend no instaladas"
)

REM Dependencias del frontend
if exist "node_modules" (
    call :status_ok "Dependencias del frontend instaladas"
) else (
    call :status_fail "Dependencias del frontend no instaladas"
)

REM WeasyPrint
if exist "venv" (
    call venv\Scripts\activate.bat
    python -c "import weasyprint" >nul 2>&1
    if not errorlevel 1 (
        call :status_ok "WeasyPrint disponible"
    ) else (
        call :status_warn "WeasyPrint no funciona correctamente"
    )
    call venv\Scripts\deactivate.bat
) else (
    call :status_warn "Entorno virtual Python no encontrado"
)

echo.
goto :eof

:check_files
echo %BLUE%=== ARCHIVOS ===%NC%

REM Archivos principales
if exist "backend\server.js" (
    call :status_ok "Servidor backend: backend\server.js"
) else (
    call :status_fail "Servidor backend no encontrado: backend\server.js"
)

if exist "js\frontend.js" (
    call :status_ok "Cliente WebSocket: js\frontend.js"
) else (
    call :status_fail "Cliente WebSocket no encontrado: js\frontend.js"
)

if exist "index.html" (
    call :status_ok "Página principal: index.html"
) else (
    call :status_fail "Página principal no encontrada: index.html"
)

if exist "%CONFIG_FILE%" (
    call :status_ok "Configuración: %CONFIG_FILE%"
) else (
    call :status_fail "Configuración no encontrada: %CONFIG_FILE%"
)

REM Directorios
if exist "%BUILD_DIR%" (
    call :status_ok "Directorio de build: %BUILD_DIR%"
) else (
    call :status_warn "Directorio de build no existe: %BUILD_DIR%"
)

if exist "%LOG_DIR%" (
    call :status_ok "Directorio de logs: %LOG_DIR%"
) else (
    call :status_warn "Directorio de logs no existe: %LOG_DIR%"
)

if exist "%DOCS_DIR%" (
    call :status_ok "Directorio de documentación: %DOCS_DIR%"
) else (
    call :status_warn "Directorio de documentación no existe: %DOCS_DIR%"
)

REM Archivos de build
if exist "%BUILD_DIR%\%COMBINED_JS_FILE%" (
    for %%F in ("%BUILD_DIR%\%COMBINED_JS_FILE%") do (
        set "SIZE=%%~zF"
        set /a "SIZE_KB=!SIZE!/1024"
        call :status_ok "JavaScript combinado: !SIZE_KB!KB"
    )
) else (
    call :status_warn "JavaScript combinado no encontrado"
)

if exist "%BUILD_DIR%\%COMBINED_CSS_FILE%" (
    for %%F in ("%BUILD_DIR%\%COMBINED_CSS_FILE%") do (
        set "SIZE=%%~zF"
        set /a "SIZE_KB=!SIZE!/1024"
        call :status_ok "CSS combinado: !SIZE_KB!KB"
    )
) else (
    call :status_warn "CSS combinado no encontrado"
)

echo.
goto :eof

:check_processes
echo %BLUE%=== PROCESOS ===%NC%

set "RUNNING=false"

REM Verificar por PID
set "PID_FILE=%LOG_DIR%\ipra-i.pid"
if exist "%PID_FILE%" (
    set /p "SERVER_PID=" < "%PID_FILE%"
    if not "!SERVER_PID!"=="" (
        tasklist /FI "PID eq !SERVER_PID!" 2>nul | findstr "!SERVER_PID!" >nul
        if not errorlevel 1 (
            call :status_ok "Proceso PID: !SERVER_PID! (ejecutándose)"
            set "RUNNING=true"
        ) else (
            call :status_warn "Archivo PID existe pero proceso no ejecutándose"
        )
    )
) else (
    call :status_warn "Archivo PID no encontrado"
)

REM Verificar por puerto
netstat -an | findstr ":%SERVER_PORT% " >nul 2>&1
if not errorlevel 1 (
    call :status_ok "Puerto %SERVER_PORT%: en uso"
    set "RUNNING=true"
) else (
    call :status_warn "Puerto %SERVER_PORT%: libre"
)

REM Buscar procesos Node.js relacionados
for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq node.exe" /FO CSV ^| findstr "node.exe"') do (
    set "NODE_PID=%%~i"
    if not "!NODE_PID!"=="" (
        call :status_ok "Procesos Node.js encontrados: !NODE_PID!"
        set "RUNNING=true"
    )
)

REM Estado general
if "%RUNNING%"=="true" (
    call :status_ok "Estado general: EJECUTÁNDOSE"
) else (
    call :status_fail "Estado general: DETENIDO"
)

echo.
goto :eof

:check_connectivity
echo %BLUE%=== CONECTIVIDAD ===%NC%

set "URL=http://%SERVER_HOST%:%SERVER_PORT%"

REM Verificar endpoint de salud con curl si está disponible
curl --version >nul 2>&1
if not errorlevel 1 (
    curl -s --connect-timeout 5 "%URL%/health" >nul 2>&1
    if not errorlevel 1 (
        call :status_ok "Endpoint de salud accesible: %URL%/health"
        
        REM Obtener información del endpoint
        for /f %%i in ('curl -s "%URL%/health" 2^>nul ^| findstr "ok"') do (
            call :status_ok "Servidor respondiendo correctamente"
        )
    ) else (
        call :status_fail "Endpoint de salud no accesible: %URL%/health"
    )
) else (
    call :status_warn "curl no disponible, no se puede verificar conectividad HTTP"
)

echo.
goto :eof

:quick_check
echo %BLUE%=== VERIFICACIÓN RÁPIDA ===%NC%

call :check_processes

REM URL del servidor
set "URL=http://%SERVER_HOST%:%SERVER_PORT%"
echo URL del servidor: %URL%
echo.
goto :eof

:show_summary
echo %BLUE%=== RESUMEN ===%NC%

set "URL=http://%SERVER_HOST%:%SERVER_PORT%"

echo Configuración:
echo   URL: %URL%
echo   WebSocket: ws://%SERVER_HOST%:%SERVER_PORT%
echo   Regional: %LC_NUMERIC%
echo.

echo Directorios:
echo   Build: %BUILD_DIR%
echo   Logs: %LOG_DIR%
echo   Docs: %DOCS_DIR%
echo.

echo Comandos útiles:
echo   Iniciar: scripts\start.bat
echo   Detener: scripts\stop.bat
echo   Logs: scripts\logs.bat
echo   Build: npm run build
echo.
goto :eof

:show_help
echo Uso: %~nx0 [OPCIONES]
echo.
echo Opciones:
echo   -h, --help       Mostrar esta ayuda
echo   -q, --quick      Verificación rápida (solo procesos)
echo   -f, --full       Verificación completa (por defecto)
echo   -s, --summary    Solo mostrar resumen
echo.
echo Ejemplos:
echo   %~nx0               # Verificación completa
echo   %~nx0 -q            # Verificación rápida
echo   %~nx0 -s            # Solo resumen
echo.
goto :eof

endlocal
