<!DOCTYPE html>
<html lang="es">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iPRA - Prototipo</title>
    <style>
        /* Configuración de páginas */
        @page {
            size: A4;
            margin: 2.5cm 2cm;

            @bottom-center {
                content: counter(page);
                font-family: 'Montserrat', sans-serif;
                font-size: 10pt;
                color: #555;
                margin-top: 0.5cm;
            }
        }

        /* Página de portada */
        @page :first {
            margin: 0;
            background-image: url('dashboard1.jpg');
            background-size: cover;
            background-position: center;

            @bottom-center {
                content: none;
            }
        }

        /* Estilos generales */
        html,
        body {
            margin: 0;
            padding: 0;
            font-family: 'Montserrat', 'Arial', sans-serif;
            line-height: 1.3;
            color: #333;
            font-size: 11pt;
        }

        /* Portada */
        .cover {
            height: 29.7cm;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            text-align: center;
            margin-top: -5cm;
            background: linear-gradient(to bottom, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.4) 70%, rgba(0, 0, 0, 0.7) 100%);
        }

        .cover h1 {
            font-size: 72pt;
            margin: 0;
            padding: 20px 40px;
            font-weight: 800;
            letter-spacing: 2px;
            text-transform: uppercase;
            background-color: rgba(0, 0, 0, 0.6);
            border-radius: 10px;
            width: 60%;
            min-width: 500px;
        }

        .cover p {
            font-size: 36pt;
            margin-top: 0.5cm;
            font-weight: 500;
            background-color: rgba(0, 0, 0, 0.6);
            padding: 15px 40px;
            border-radius: 5px;
            width: 60%;
            min-width: 500px;
        }

        .copyright {
            position: absolute;
            bottom: 2cm;
            font-size: 10pt;
            opacity: 0.8;
            width: 100%;
            text-align: center;
        }

        /* Contenido */
        .content-wrapper {
            margin: 0 auto;
        }

        .toc-page {
            page-break-after: always;
            margin-top: 2cm;
        }

        .toc-title {
            text-align: center;
            font-size: 24pt;
            color: #2c3e50;
            margin-bottom: 1.5cm;
            font-weight: 600;
        }

        /* Encabezados */
        h1 {
            color: #2c3e50;
            font-size: 22pt;
            margin-top: 1.2cm;
            margin-bottom: 0.7cm;
            border-bottom: 3px solid #3498db;
            padding-bottom: 0.3cm;
            font-weight: 600;
        }

        h2 {
            color: #2980b9;
            font-size: 18pt;
            margin-top: 1cm;
            margin-bottom: 0.5cm;
            border-bottom: 1px solid #bdc3c7;
            padding-bottom: 0.2cm;
            font-weight: 500;
        }

        h3 {
            color: #3498db;
            font-size: 14pt;
            margin-top: 0.8cm;
            margin-bottom: 0.4cm;
            font-weight: 500;
        }

        h4 {
            color: #16a085;
            font-size: 12pt;
            margin-top: 0.6cm;
            margin-bottom: 0.3cm;
            font-weight: 500;
        }

        h5 {
            color: #1abc9c;
            font-size: 11pt;
            margin-top: 0.5cm;
            margin-bottom: 0.2cm;
            font-weight: 500;
            font-style: italic;
        }

        /* Texto y enlaces */
        p {
            margin-bottom: 0.5cm;
            text-align: justify;
            hyphens: auto;
        }

        a {
            color: #3498db;
            text-decoration: none;
        }

        /* Listas */
        ul,
        ol {
            margin-bottom: 0.6cm;
            padding-left: 1cm;
        }

        li {
            margin-bottom: 0.3cm;
        }

        /* Tablas */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 0.8cm 0;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 0.3cm;
        }

        th {
            background-color: #f2f8fd;
            color: #2c3e50;
            font-weight: 600;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        /* Imágenes */
        img {
            max-width: 100%;
            height: auto;
            margin: 0.5cm auto;
            display: block;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        /* Código */
        code {
            font-family: 'Consolas', 'Monaco', monospace;
            background-color: #f5f5f5;
            padding: 0.1cm 0.2cm;
            border-radius: 3px;
            font-size: 0.9em;
            color: #e74c3c;
        }

        pre {
            background-color: #f8f8f8;
            padding: 0.5cm;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 0.7cm 0;
            border-left: 3px solid #3498db;
            font-size: 0.9em;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        /* Índice */
        .toc ol {
            padding-left: 1.2cm;
        }

        .toc li {
            margin-bottom: 0.15cm;
            line-height: 1.2;
        }

        .toc a {
            text-decoration: none;
            color: #2980b9;
        }

        .toc ol ol {
            margin-top: 0.1cm;
            margin-bottom: 0.1cm;
        }

        /* Secciones especiales */
        .note {
            background-color: #eef7fa;
            border-left: 4px solid #3498db;
            padding: 0.5cm;
            margin: 0.7cm 0;
            border-radius: 0 5px 5px 0;
        }

        .warning {
            background-color: #fff5eb;
            border-left: 4px solid #e67e22;
            padding: 0.5cm;
            margin: 0.7cm 0;
            border-radius: 0 5px 5px 0;
        }
    </style>
</head>

<body>
    <!-- Esta sección será reemplazada con el contenido real -->
</body>

</html>