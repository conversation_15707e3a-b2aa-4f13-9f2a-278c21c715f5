/* Accordion styles for widget configuration */
:root {
    --accordion-bg-color: var(--card-bg-color, #ffffff);
    --accordion-border-color: var(--border-color, #cccccc);
    --accordion-header-bg: var(--primary-color, #4a6fa5);
    --accordion-header-color: var(--primary-text-color, #ffffff);
    --accordion-header-hover-bg: var(--primary-hover-color, #3a5f95);
    --accordion-content-bg: var(--card-bg-color, #ffffff);
    --accordion-shadow: var(--card-shadow, 0 2px 5px rgba(0, 0, 0, 0.1));
    --accordion-icon-color: var(--primary-text-color, #ffffff);
}

/* Contenedor principal del acordeón */
.form-fields-accordion {
    max-height: 550px;
    /* Aumentado de 500px a 550px para aprovechar el espacio adicional */
    overflow-y: auto;
    /* Scroll vertical cuando sea necesario */
}

/* Estructura base del acordeón */
.accordion-section {
    border: 1px solid var(--accordion-border-color);
    margin-bottom: 0.5rem;
    border-radius: 4px;
    overflow: hidden;
    transition: opacity 0.3s ease-in-out;
}

/* Checkbox oculto que controla el estado */
.accordion-toggle {
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0;
    z-index: -1;
}

/* Cabecera del acordeón */
.accordion-header {
    display: flex;
    align-items: center;
    padding: 0.6rem 1rem;
    /* Reducido de 0.75rem a 0.6rem */
    background-color: var(--accordion-header-bg);
    color: var(--accordion-header-color);
    cursor: pointer;
    transition: background-color 0.2s ease;
    user-select: none;
}

.accordion-header:hover {
    background-color: var(--accordion-header-hover-bg);
}

/* Título dentro de la cabecera */
.accordion-header .title {
    flex-grow: 1;
    font-weight: 500;
    margin: 0 0.5rem;
}

/* Iconos en la cabecera */
.accordion-header .material-icons {
    font-family: sans-serif;
    font-size: 0;
    position: relative;
    width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-style: normal;
}

/* Icono de zona */
.accordion-header .zone-icon::before {
    font-size: 18px;
    position: absolute;
    content: "⚙";
}

.accordion-header[data-zone-id="posicion"] .zone-icon::before {
    content: "⌖";
}

.accordion-header[data-zone-id="series"] .zone-icon::before {
    content: "⊞";
}

/* Estilos específicos para la sección de Series */
.accordion-section:has(.accordion-header[data-zone-id="series"]) {
    position: relative;
    z-index: 10;
    /* Mayor z-index para la sección de Series */
}

/* Icono de toggle */
.accordion-header .toggle-icon {
    margin-left: auto;
}

.accordion-header .toggle-icon::before {
    font-size: 18px;
    position: absolute;
    content: "▸";
}

.accordion-toggle:not(:checked)+.accordion-header .toggle-icon::before {
    content: "▾";
}

/* Contenido del acordeón */
.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-in-out, padding 0.3s ease-in-out;
    padding: 0 1rem;
    background-color: var(--accordion-content-bg);
    box-sizing: border-box;
    position: relative;
    /* Asegurar que el contenido se muestre correctamente */
    z-index: 0;
    /* Valor base para el z-index */
}

.accordion-toggle:not(:checked)~.accordion-content {
    max-height: 400px;
    /* Aumentado de 350px a 400px para aprovechar el espacio adicional */
    overflow-y: auto;
    /* Añadir scroll vertical cuando sea necesario */
    padding: 0.8rem;
    padding-bottom: 0;
    /* Reducido de 1rem a 0.8rem */
    scrollbar-width: thin;
    /* Para Firefox */
    transition: max-height 0.3s ease-in-out, padding 0.3s ease-in-out, opacity 0.3s ease-in-out;
    opacity: 1;
    z-index: 1;
    /* Aumentar z-index cuando está abierto */
}

/* Estilo para la barra de desplazamiento en navegadores WebKit */
.accordion-content::-webkit-scrollbar {
    width: 6px;
}

.accordion-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.accordion-content::-webkit-scrollbar-thumb {
    background: var(--accordion-header-bg);
    border-radius: 3px;
}

.accordion-content::-webkit-scrollbar-thumb:hover {
    background: var(--accordion-header-hover-bg);
}

/* Estilos de los campos dentro del acordeón */
.accordion-content .field-group {
    margin-bottom: 0.7rem;
    /* Reducido de 1rem a 0.7rem */
}

.accordion-content .field-group:last-child {
    margin-bottom: 0;
}

.accordion-content label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.accordion-content input[type="text"],
.accordion-content input[type="number"],
.accordion-content select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--accordion-border-color);
    border-radius: 4px;
    font-size: 0.9rem;
}

.accordion-content input[type="checkbox"] {
    margin-right: 0.5rem;
}