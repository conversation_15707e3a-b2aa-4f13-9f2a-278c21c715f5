#!/bin/bash

# Script para detener el servidor IPRA_I

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Cargar configuración
if [ ! -f "config.conf" ]; then
    error "Archivo config.conf no encontrado. Ejecuta configure.sh primero."
fi

source config.conf

# Función para detener con systemd
stop_systemd() {
    log "Deteniendo servicio con systemd..."
    
    if ! systemctl is-enabled ipra-i &>/dev/null; then
        warn "Servicio systemd no está habilitado"
        return 1
    fi
    
    if ! systemctl is-active ipra-i &>/dev/null; then
        log "El servicio ya está detenido"
        return 0
    fi
    
    sudo systemctl stop ipra-i
    
    # Esperar un momento para que se detenga
    sleep 2
    
    if ! systemctl is-active ipra-i &>/dev/null; then
        log "Servicio detenido correctamente"
        return 0
    else
        error "Error al detener el servicio"
        return 1
    fi
}

# Función para detener por PID
stop_by_pid() {
    local pid_file="$LOG_DIR/ipra-i.pid"
    
    if [ ! -f "$pid_file" ]; then
        warn "Archivo PID no encontrado: $pid_file"
        return 1
    fi
    
    local pid=$(cat "$pid_file")
    
    if [ -z "$pid" ]; then
        warn "PID vacío en archivo: $pid_file"
        rm -f "$pid_file"
        return 1
    fi
    
    log "Deteniendo proceso PID: $pid"
    
    # Verificar si el proceso existe
    if ! kill -0 "$pid" 2>/dev/null; then
        warn "Proceso PID $pid no existe"
        rm -f "$pid_file"
        return 1
    fi
    
    # Intentar detener gracefully
    kill -TERM "$pid" 2>/dev/null
    
    # Esperar hasta 10 segundos para que se detenga
    local count=0
    while [ $count -lt 10 ]; do
        if ! kill -0 "$pid" 2>/dev/null; then
            log "Proceso detenido correctamente"
            rm -f "$pid_file"
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done
    
    # Si no se detuvo, forzar
    warn "Proceso no se detuvo gracefully, forzando..."
    kill -KILL "$pid" 2>/dev/null
    
    # Verificar que se detuvo
    sleep 1
    if ! kill -0 "$pid" 2>/dev/null; then
        log "Proceso forzado a detenerse"
        rm -f "$pid_file"
        return 0
    else
        error "No se pudo detener el proceso PID $pid"
        return 1
    fi
}

# Función para detener por puerto
stop_by_port() {
    local port="$SERVER_PORT"
    
    log "Buscando procesos en puerto $port..."
    
    # Buscar procesos usando el puerto
    local pids=""
    
    if command -v lsof &> /dev/null; then
        pids=$(lsof -ti:$port 2>/dev/null || true)
    elif command -v netstat &> /dev/null; then
        pids=$(netstat -tlnp 2>/dev/null | grep ":$port " | awk '{print $7}' | cut -d'/' -f1 | grep -v '-' || true)
    elif command -v ss &> /dev/null; then
        pids=$(ss -tlnp 2>/dev/null | grep ":$port " | sed 's/.*pid=\([0-9]*\).*/\1/' || true)
    fi
    
    if [ -z "$pids" ]; then
        log "No se encontraron procesos en puerto $port"
        return 1
    fi
    
    log "Procesos encontrados en puerto $port: $pids"
    
    # Detener cada proceso
    for pid in $pids; do
        if [ -n "$pid" ] && [ "$pid" != "-" ]; then
            log "Deteniendo proceso PID: $pid"
            
            # Verificar si es nuestro proceso Node.js
            if ps -p "$pid" -o comm= 2>/dev/null | grep -q "node"; then
                # Intentar detener gracefully
                kill -TERM "$pid" 2>/dev/null || true
                
                # Esperar hasta 5 segundos
                local count=0
                while [ $count -lt 5 ]; do
                    if ! kill -0 "$pid" 2>/dev/null; then
                        log "Proceso $pid detenido correctamente"
                        break
                    fi
                    sleep 1
                    count=$((count + 1))
                done
                
                # Si no se detuvo, forzar
                if kill -0 "$pid" 2>/dev/null; then
                    warn "Forzando detención del proceso $pid"
                    kill -KILL "$pid" 2>/dev/null || true
                fi
            else
                warn "Proceso $pid no parece ser Node.js, omitiendo"
            fi
        fi
    done
    
    return 0
}

# Función para detener todos los métodos
stop_all() {
    log "Intentando detener servidor por todos los métodos..."
    
    local stopped=false
    
    # Intentar systemd primero
    if stop_systemd; then
        stopped=true
    fi
    
    # Intentar por PID
    if stop_by_pid; then
        stopped=true
    fi
    
    # Intentar por puerto
    if stop_by_port; then
        stopped=true
    fi
    
    if [ "$stopped" = true ]; then
        log "Servidor detenido correctamente"
    else
        warn "No se encontraron procesos del servidor ejecutándose"
    fi
}

# Función para limpiar archivos temporales
cleanup() {
    log "Limpiando archivos temporales..."
    
    # Limpiar archivo PID
    local pid_file="$LOG_DIR/ipra-i.pid"
    if [ -f "$pid_file" ]; then
        rm -f "$pid_file"
        log "Archivo PID eliminado"
    fi
    
    # Limpiar archivos de lock si existen
    if [ -d "$LOG_DIR" ]; then
        find "$LOG_DIR" -name "*.lock" -delete 2>/dev/null || true
    fi
    
    log "Limpieza completada"
}

# Función para verificar estado después de detener
verify_stopped() {
    log "Verificando que el servidor esté detenido..."
    
    # Verificar puerto
    if command -v netstat &> /dev/null; then
        if netstat -tuln | grep -q ":$SERVER_PORT "; then
            warn "El puerto $SERVER_PORT todavía está en uso"
            return 1
        fi
    elif command -v ss &> /dev/null; then
        if ss -tuln | grep -q ":$SERVER_PORT "; then
            warn "El puerto $SERVER_PORT todavía está en uso"
            return 1
        fi
    fi
    
    # Verificar systemd
    if systemctl is-enabled ipra-i &>/dev/null; then
        if systemctl is-active ipra-i &>/dev/null; then
            warn "El servicio systemd todavía está activo"
            return 1
        fi
    fi
    
    log "Servidor detenido completamente"
    return 0
}

# Función para mostrar ayuda
show_help() {
    echo "Uso: $0 [OPCIONES]"
    echo ""
    echo "Opciones:"
    echo "  -h, --help       Mostrar esta ayuda"
    echo "  -s, --systemd    Detener solo servicio systemd"
    echo "  -p, --pid        Detener solo por archivo PID"
    echo "  -P, --port       Detener solo por puerto"
    echo "  -a, --all        Detener por todos los métodos (por defecto)"
    echo "  -f, --force      Forzar detención (SIGKILL)"
    echo "  -c, --cleanup    Solo limpiar archivos temporales"
    echo ""
    echo "Ejemplos:"
    echo "  $0               # Detener por todos los métodos"
    echo "  $0 -s            # Detener solo systemd"
    echo "  $0 -p            # Detener solo por PID"
    echo "  $0 -f            # Forzar detención"
    echo ""
}

# Función para forzar detención
force_stop() {
    log "Forzando detención del servidor..."
    
    # Buscar todos los procesos Node.js que puedan ser nuestro servidor
    local pids=$(pgrep -f "node.*server.js" 2>/dev/null || true)
    
    if [ -z "$pids" ]; then
        # Buscar por puerto
        if command -v lsof &> /dev/null; then
            pids=$(lsof -ti:$SERVER_PORT 2>/dev/null || true)
        fi
    fi
    
    if [ -n "$pids" ]; then
        log "Forzando detención de procesos: $pids"
        for pid in $pids; do
            kill -KILL "$pid" 2>/dev/null || true
        done
        sleep 1
        log "Procesos forzados a detenerse"
    else
        log "No se encontraron procesos para forzar"
    fi
    
    cleanup
}

# Función principal
main() {
    log "=== DETENIENDO SERVIDOR IPRA_I ==="
    
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        -s|--systemd)
            stop_systemd
            ;;
        -p|--pid)
            stop_by_pid
            ;;
        -P|--port)
            stop_by_port
            ;;
        -f|--force)
            force_stop
            ;;
        -c|--cleanup)
            cleanup
            ;;
        -a|--all|"")
            stop_all
            cleanup
            verify_stopped
            ;;
        *)
            error "Opción desconocida: $1. Usa -h para ayuda."
            ;;
    esac
    
    log "Operación de detención completada"
}

# Ejecutar función principal
main "$@"
