/**
 * Gestor del perfil de usuario
 * Permite al usuario editar su propio perfil
 */
class UserProfileManager {
    constructor() {
        this.currentUser = null;
        app.userProfileManager = this;
    }

    /**
     * Inicializa el gestor de perfil de usuario
     */
    init() {
        this.currentUser = authManager.user;
    }

    /**
     * Muestra el formulario de edición del perfil de usuario
     */
    showUserProfileForm() {
        // Verificar que hay un usuario autenticado
        if (!this.currentUser) {
            this.currentUser = authManager.user;
            if (!this.currentUser) {
                showNotification('No hay un usuario autenticado', 'error');
                return;
            }
        }

        // Crear modal para editar el perfil
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.id = 'user-profile-modal';

        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';
        modalContent.style.maxWidth = '430px'; // Ancho máximo para compatibilidad con móviles

        // Cabecera del modal
        const modalHeader = document.createElement('div');
        modalHeader.className = 'modal-header';
        // Aplicar estilos para alinear correctamente el título y el botón de cierre
        modalHeader.style.display = 'flex';
        modalHeader.style.justifyContent = 'space-between';
        modalHeader.style.alignItems = 'center';
        modalHeader.style.marginBottom = '15px';

        const title = document.createElement('h2');
        title.textContent = 'Personalizar perfil';
        title.style.margin = '0'; // Eliminar márgenes para mejor alineación
        title.style.paddingRight = '0'; // Eliminar padding derecho

        const closeBtn = document.createElement('span');
        closeBtn.className = 'close-btn';
        closeBtn.innerHTML = '&times;';
        closeBtn.title = 'Cerrar';
        // Asegurar que el botón de cierre esté alineado verticalmente
        closeBtn.style.float = 'none';
        closeBtn.style.display = 'flex';
        closeBtn.style.alignItems = 'center';
        closeBtn.style.justifyContent = 'center';
        closeBtn.style.lineHeight = '1';

        modalHeader.appendChild(title);
        modalHeader.appendChild(closeBtn);
        modalContent.appendChild(modalHeader);

        // Contenedor del formulario
        const formContainer = document.createElement('div');
        formContainer.id = 'user-profile-form-container';

        // Crear formulario
        const form = document.createElement('form');
        form.id = 'user-profile-form';
        form.dataset.userId = this.currentUser.id;

        // Añadir campos del formulario
        const fields = [{
                name: 'login',
                label: 'Login',
                type: 'text',
                required: true,
                readonly: true,
            },
            {
                name: 'nombre',
                label: 'Nombre',
                type: 'text',
                required: true
            },
            {
                name: 'email',
                label: 'Email',
                type: 'email',
                required: true
            }
            // Empresa no se muestra en el formulario pero se mantiene al grabar
        ];

        fields.forEach(field => {
            const formGroup = document.createElement('div');
            formGroup.className = 'form-group';

            const label = document.createElement('label');
            label.setAttribute('for', `profile-${field.name}`);
            label.textContent = field.label;

            const input = document.createElement('input');
            input.type = field.type;
            input.id = `profile-${field.name}`;
            input.name = field.name;
            input.value = this.currentUser[field.name] !== undefined ? this.currentUser[field.name] : '';

            if (field.required) {
                input.required = true;
            }

            if (field.readonly) {
                input.readOnly = true;
                input.disabled = true;
                input.style.backgroundColor = '#f0f0f0';
            }

            formGroup.appendChild(label);
            formGroup.appendChild(input);

            // Añadir mensaje de error
            const errorMessage = document.createElement('p');
            errorMessage.className = 'error-message';
            errorMessage.id = `error-${field.name}`;
            formGroup.appendChild(errorMessage);

            form.appendChild(formGroup);
        });

        // Añadir botón para cambiar contraseña
        const passwordButtonContainer = document.createElement('div');
        passwordButtonContainer.className = 'form-group';
        passwordButtonContainer.style.marginTop = '20px';

        const passwordButton = document.createElement('button');
        passwordButton.type = 'button';
        passwordButton.className = 'secondary-btn';
        passwordButton.textContent = 'Clave';
        passwordButton.title = 'Cambiar contraseña';
        passwordButton.addEventListener('click', () => {
            this.showChangePasswordDialog();
        });

        passwordButtonContainer.appendChild(passwordButton);
        form.appendChild(passwordButtonContainer);

        formContainer.appendChild(form);
        modalContent.appendChild(formContainer);

        // Botones de acción
        const buttonGroup = document.createElement('div');
        buttonGroup.className = 'button-group';

        const saveButton = document.createElement('button');
        saveButton.type = 'button';
        saveButton.className = 'primary-btn';
        saveButton.textContent = 'Guardar';
        saveButton.title = 'Guardar cambios';
        saveButton.addEventListener('click', () => {
            this.saveUserProfile();
        });

        const cancelButton = document.createElement('button');
        cancelButton.type = 'button';
        cancelButton.className = 'secondary-btn';
        cancelButton.textContent = 'Cancelar';
        cancelButton.title = 'Cancelar y cerrar';
        cancelButton.addEventListener('click', () => {
            closeModal(modal);
        });

        buttonGroup.appendChild(saveButton);
        buttonGroup.appendChild(cancelButton);
        modalContent.appendChild(buttonGroup);

        // Añadir evento de cierre
        closeBtn.addEventListener('click', () => {
            closeModal(modal);
        });

        // Añadir modal al DOM y mostrarlo
        modal.appendChild(modalContent);
        document.body.appendChild(modal);
        openModal(modal);

        // Hacer el modal arrastrable por el título
        makeDraggable(modalContent, modalHeader);
    }

    /**
     * Muestra un diálogo para cambiar la contraseña del usuario
     */
    showChangePasswordDialog() {
        // Crear diálogo para cambiar contraseña
        dialogManager.custom({
            title: 'Cambiar contraseña',
            content: `
                <div class="form-group">
                    <label for="current-password">Contraseña actual:</label>
                    <input type="password" id="current-password" required>
                    <p class="error-message" id="error-current-password"></p>
                </div>
                <div class="form-group">
                    <label for="new-password">Nueva contraseña:</label>
                    <input type="password" id="new-password" required>
                    <p class="error-message" id="error-new-password"></p>
                </div>
                <div class="form-group">
                    <label for="confirm-password">Confirmar contraseña:</label>
                    <input type="password" id="confirm-password" required>
                    <p class="error-message" id="error-confirm-password"></p>
                </div>
            `,
            buttons: [{
                    text: 'Cambiar',
                    type: 'primary',
                    action: (dialog) => {
                        // Validar contraseña actual
                        const currentPassword = document.getElementById('current-password').value;
                        const newPassword = document.getElementById('new-password').value;
                        const confirmPassword = document.getElementById('confirm-password').value;

                        // Limpiar mensajes de error
                        document.getElementById('error-current-password').textContent = '';
                        document.getElementById('error-new-password').textContent = '';
                        document.getElementById('error-confirm-password').textContent = '';

                        let isValid = true;

                        // Validar contraseña actual
                        if (currentPassword !== this.currentUser.clave) {
                            document.getElementById('error-current-password').textContent = 'Contraseña incorrecta';
                            isValid = false;
                        }

                        // Validar que la nueva contraseña no esté vacía
                        if (!newPassword) {
                            document.getElementById('error-new-password').textContent = 'La contraseña no puede estar vacía';
                            isValid = false;
                        }

                        // Validar que las contraseñas coincidan
                        if (newPassword !== confirmPassword) {
                            document.getElementById('error-confirm-password').textContent = 'Las contraseñas no coinciden';
                            isValid = false;
                        }

                        if (!isValid) return false; // No cerrar el diálogo

                        // Cambiar contraseña
                        const updatedUser = {
                            ...this.currentUser,
                            clave: newPassword
                        };
                        dbManager.saveUser(updatedUser)
                            .then(() => {
                                // Actualizar usuario en authManager y en este gestor
                                authManager.user = updatedUser;
                                this.currentUser = updatedUser;
                                showNotification('Contraseña cambiada correctamente', 'success');
                                dialog.close();
                            })
                            .catch(error => {
                                console.error('Error al cambiar contraseña:', error);
                                showNotification('Error al cambiar contraseña', 'error');
                            });

                        return false; // No cerrar el diálogo automáticamente
                    }
                },
                {
                    text: 'Cancelar',
                    type: 'secondary',
                    action: () => true // Cerrar el diálogo
                }
            ]
        });
    }

    /**
     * Guarda los cambios en el perfil del usuario
     */
    saveUserProfile() {
        const form = document.getElementById('user-profile-form');
        if (!form) return;

        // Validar formulario
        if (!this.validateUserProfileForm()) {
            return;
        }

        // Obtener datos del formulario
        const userId = parseInt(form.dataset.userId, 10);

        if (userId !== this.currentUser.id) {
            showNotification('Error: No se puede editar otro usuario', 'error');
            return;
        }

        // Obtener valores del formulario
        const login = document.getElementById('profile-login').value;
        const nombre = document.getElementById('profile-nombre').value;
        const email = document.getElementById('profile-email').value;

        // Crear objeto con los datos actualizados
        const updatedUser = {
            ...this.currentUser,
            login,
            nombre,
            email
        };

        // Guardar cambios
        dbManager.saveUser(updatedUser)
            .then(() => {
                // Actualizar usuario en authManager y en este gestor
                authManager.user = updatedUser;
                this.currentUser = updatedUser;
                showNotification('Perfil actualizado correctamente', 'success');

                // Cerrar modal
                const modal = document.getElementById('user-profile-modal');
                if (modal) {
                    closeModal(modal);
                }
            })
            .catch(error => {
                console.error('Error al guardar perfil:', error);
                showNotification(`Error al guardar: ${error.message}`, 'error');
            });
    }

    /**
     * Valida el formulario de perfil de usuario
     * @returns {boolean} - True si el formulario es válido
     */
    validateUserProfileForm() {
        const form = document.getElementById('user-profile-form');
        if (!form) return false;

        let isValid = true;

        // Validar cada campo
        const fields = [{
                name: 'login',
                label: 'Login',
                required: true
            },
            {
                name: 'nombre',
                label: 'Nombre',
                required: true
            },
            {
                name: 'email',
                label: 'Email',
                required: true,
                type: 'email'
            }
        ];

        fields.forEach(field => {
            const input = document.getElementById(`profile-${field.name}`);
            const errorElement = document.getElementById(`error-${field.name}`);

            if (!input || !errorElement) return;

            let errorMessage = '';

            // Validar campo requerido
            if (field.required && !input.value.trim()) {
                errorMessage = `El campo ${field.label} es obligatorio`;
            }

            // Validar email
            if (field.type === 'email' && input.value.trim() && !validateEmail(input.value.trim())) {
                errorMessage = 'El formato de email no es válido';
            }

            // Actualizar mensaje de error y validez
            errorElement.textContent = errorMessage;

            if (errorMessage) {
                input.classList.add('invalid');
                isValid = false;
            } else {
                input.classList.remove('invalid');
            }
        });

        return isValid;
    }
}

// Crear instancia del gestor de perfil de usuario
const userProfileManager = new UserProfileManager();