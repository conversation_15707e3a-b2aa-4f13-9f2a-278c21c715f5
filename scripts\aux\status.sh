#!/bin/bash

# Script para verificar el estado del servidor IPRA_I (Linux) - MOVIDO A AUX

# Obtener directorio del script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Cargar configuración
CONFIG_FILE="$SCRIPT_DIR/../config.conf"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "ERROR: Archivo config.conf no encontrado. Edita scripts/config.conf primero."
    exit 1
fi

source "$CONFIG_FILE"

echo "=== ESTADO IPRA_I ==="
echo "Puerto: $SERVER_PORT"
echo "Host: $SERVER_HOST"

# Verificar si está ejecutándose
if netstat -tuln 2>/dev/null | grep -q ":$SERVER_PORT "; then
    echo "Estado: EJECUTÁNDOSE"
elif ss -tuln 2>/dev/null | grep -q ":$SERVER_PORT "; then
    echo "Estado: EJECUTÁNDOSE"
else
    echo "Estado: DETENIDO"
fi

echo "URL: http://$SERVER_HOST:$SERVER_PORT"
echo
