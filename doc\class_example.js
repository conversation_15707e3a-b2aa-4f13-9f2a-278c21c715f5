/**
 * Ejemplo de cómo las clases deben registrarse en el objeto app
 * Este archivo es solo un ejemplo y no debe incluirse en la aplicación
 */

/**
 * Ejemplo de clase AuthManager
 */
class AuthManager {
    constructor() {
        // Registrar la instancia en el objeto app
        app.authManager = this;
        
        // Inicialización de la clase
        this.isAuthenticated = false;
        this.user = null;
        this.companyId = null;
        
        console.log('AuthManager registrado en app.authManager');
    }
    
    init() {
        // Código de inicialización
        console.log('AuthManager inicializado');
    }
    
    // Otros métodos de la clase...
}

/**
 * Ejemplo de clase WidgetManager
 */
class WidgetManager {
    constructor() {
        // Registrar la instancia en el objeto app
        app.widgetManager = this;
        
        // Inicialización de la clase
        this.widgets = [];
        
        console.log('WidgetManager registrado en app.widgetManager');
    }
    
    createWidget(config) {
        // Código para crear un widget
        return new Promise((resolve) => {
            // Simulación de creación asíncrona
            setTimeout(() => {
                this.widgets.push(config);
                resolve();
            }, 100);
        });
    }
    
    // Otros métodos de la clase...
}

/**
 * Ejemplo de clase DashboardManager
 */
class DashboardManager {
    constructor() {
        // Registrar la instancia en el objeto app
        app.dashboardManager = this;
        
        // Inicialización de la clase
        this.dashboards = [];
        this.currentDashboardId = null;
        this.dashboard = null;
        
        console.log('DashboardManager registrado en app.dashboardManager');
    }
    
    init(companyId) {
        // Código de inicialización
        console.log(`DashboardManager inicializado para la empresa ${companyId}`);
    }
    
    // Otros métodos de la clase...
}

/**
 * Ejemplo de clase EntityManager
 */
class EntityManager {
    constructor() {
        // Registrar la instancia en el objeto app
        app.entityManager = this;
        
        // Inicialización de la clase
        this.entities = [];
        this.entityType = null;
        
        console.log('EntityManager registrado en app.entityManager');
    }
    
    init(entityType) {
        // Código de inicialización
        this.entityType = entityType;
        console.log(`EntityManager inicializado para el tipo ${entityType}`);
    }
    
    // Otros métodos de la clase...
}

/**
 * Ejemplo de clase DialogManager
 */
class DialogManager {
    constructor() {
        // Registrar la instancia en el objeto app
        app.dialogManager = this;
        
        console.log('DialogManager registrado en app.dialogManager');
    }
    
    showConfirmDialog(message, onConfirm, onCancel) {
        // Código para mostrar un diálogo de confirmación
        console.log(`Mostrando diálogo de confirmación: ${message}`);
    }
    
    // Otros métodos de la clase...
}

/**
 * Ejemplo de clase ThemeManager
 */
class ThemeManager {
    constructor() {
        // Registrar la instancia en el objeto app
        app.themeManager = this;
        
        console.log('ThemeManager registrado en app.themeManager');
    }
    
    changeTheme(theme) {
        // Código para cambiar el tema
        console.log(`Cambiando al tema: ${theme}`);
    }
    
    // Otros métodos de la clase...
}

/**
 * Ejemplo de clase MainMenuManager
 */
class MainMenuManager {
    constructor() {
        // Registrar la instancia en el objeto app
        app.mainMenuManager = this;
        
        console.log('MainMenuManager registrado en app.mainMenuManager');
    }
    
    init() {
        // Código de inicialización
        console.log('MainMenuManager inicializado');
    }
    
    // Otros métodos de la clase...
}

// Nota: Este archivo es solo un ejemplo y no debe incluirse en la aplicación
// Las clases reales deben modificarse para registrarse en el objeto app
