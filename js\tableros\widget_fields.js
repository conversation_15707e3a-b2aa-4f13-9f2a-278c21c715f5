/**
 * Módulo para la gestión de campos de widgets
 * Este módulo centraliza la lógica para obtener los campos a mostrar según el tipo de widget
 */

class WidgetFieldsManager {
    constructor() {
        // Registrar instancia en app
        app["widgetFieldsManager"] = this;
    }

    /**
     * Obtiene los campos a mostrar según el tipo de widget
     * @param {string} widgetType - Tipo de widget
     * @param {boolean} isEdit - Indica si es para edición (true) o creación (false)
     * @returns {Object} - Objeto con información de los campos
     */
    getWidgetFields(widgetType, isEdit = false) {
        const prefix = isEdit ? 'edit-' : '';

        // Definir las zonas disponibles
        const zones = {
            base: {
                name: "base",
                icon: "settings",
                title: 'Base'
            },
            color: {
                name: "color",
                icon: "⚈",
                title: 'Color'
            },
            reglas: {
                name: "reglas",
                icon: "rule",
                title: '<PERSON><PERSON>'
            },
            series: {
                name: "series",
                icon: "stacked_line_chart",
                title: 'Series'
            },
            posicion: {
                name: "posicion",
                icon: "place",
                title: 'Posición'
            }
        };

        // Definir los campos comunes para todos los tipos de widgets
        const commonFields = {
            containerId: `${prefix}${widgetType}-widget-fields`,
            zones: zones,
            fields: [
                // Campo de título (zona "Base")
                {
                    type: 'input',
                    id: `${prefix}widget-title`,
                    label: 'Título:',
                    paramName: 'title',
                    zone: 'base'
                },
                // Campos de posición (zona "Posición")
                {
                    type: 'number',
                    id: `${prefix}widget-x`,
                    label: 'Posición X:',
                    paramName: 'x',
                    min: 0,
                    defaultValue: 0,
                    zone: 'posicion'
                },
                {
                    type: 'number',
                    id: `${prefix}widget-y`,
                    label: 'Posición Y:',
                    paramName: 'y',
                    min: 1,
                    zone: 'posicion'
                },
                {
                    type: 'number',
                    id: `${prefix}widget-width`,
                    label: 'Ancho:',
                    paramName: 'width',
                    min: 1,
                    zone: 'posicion'
                },
                {
                    type: 'number',
                    id: `${prefix}widget-height`,
                    label: 'Alto:',
                    paramName: 'height',
                    min: 1,
                    zone: 'posicion'
                },
                // Campos de color (zona "Color")
                {
                    type: 'color',
                    id: `${prefix}widget-bg-color`,
                    label: 'Color de fondo:',
                    paramName: 'bgColor',
                    zone: 'color',
                    reset: 'resetWidgetBgColor' // Añadimos la propiedad reset
                },
                {
                    type: 'color',
                    id: `${prefix}widget-text-color`,
                    label: 'Color de texto:',
                    paramName: 'textColor',
                    zone: 'color',
                    reset: 'resetWidgetTextColor' // Añadimos la propiedad reset
                },
                {
                    type: 'color',
                    id: `${prefix}widget-border-color`,
                    label: 'Color de borde:',
                    paramName: 'borderColor',
                    zone: 'color',
                    reset: 'resetWidgetBorderColor' // Añadimos la propiedad reset
                },
                // Regla 1
                {
                    type: 'number',
                    id: `${prefix}widget-regla1-valor`,
                    label: 'Val:',
                    paramName: 'regla1Valor',
                    zone: 'reglas'
                },
                {
                    type: 'color',
                    id: `${prefix}widget-regla1-bg-color`,
                    label: 'Fondo:',
                    paramName: 'regla1BgColor',
                    zone: 'reglas'
                },
                {
                    type: 'color',
                    id: `${prefix}widget-regla1-text-color`,
                    label: 'Texto:',
                    paramName: 'regla1TextColor',
                    zone: 'reglas'
                },
                {
                    type: 'color',
                    id: `${prefix}widget-regla1-border-color`,
                    label: 'Borde:',
                    paramName: 'regla1BorderColor',
                    zone: 'reglas'
                },
                // Regla 2
                {
                    type: 'number',
                    id: `${prefix}widget-regla2-valor`,
                    label: 'Val:',
                    paramName: 'regla2Valor',
                    zone: 'reglas'
                },
                {
                    type: 'color',
                    id: `${prefix}widget-regla2-bg-color`,
                    label: 'Fondo:',
                    paramName: 'regla2BgColor',
                    zone: 'reglas'
                },
                {
                    type: 'color',
                    id: `${prefix}widget-regla2-text-color`,
                    label: 'Texto:',
                    paramName: 'regla2TextColor',
                    zone: 'reglas'
                },
                {
                    type: 'color',
                    id: `${prefix}widget-regla2-border-color`,
                    label: 'Borde:',
                    paramName: 'regla2BorderColor',
                    zone: 'reglas'
                }
            ]
        };

        // Añadir campos específicos según el tipo de widget
        switch (widgetType) {
            case 'text':
                // Eliminar la zona de reglas para widgets de texto
                delete commonFields.zones.reglas;

                // Añadir textarea para el contenido
                commonFields.fields.push({
                    type: 'textarea',
                    id: `${prefix}text-content`,
                    label: 'Texto:',
                    paramName: 'text',
                    zone: 'base',
                    class: 'textarea-widget'
                });
                break;
            case 'value':
                commonFields.fields.push({
                    type: 'select',
                    id: `${prefix}value-type`,
                    label: 'Valor a mostrar:',
                    paramName: 'valueType',
                    dataSource: 'getValueTypes',
                    zone: 'base',
                    class: 'es_data_source'
                });
                break;
            case 'gauge':
                commonFields.fields.push({
                    type: 'select',
                    id: `${prefix}gauge-type`,
                    label: 'Tipo de gauge:',
                    paramName: 'gaugeType',
                    dataSource: 'getGaugeTypes',
                    zone: 'base',
                    class: 'es_data_source'
                });
                break;
            case 'percentage-gauge':
                commonFields.fields.push({
                    type: 'select',
                    id: `${prefix}percentage-type`,
                    label: 'Tipo de porcentaje:',
                    paramName: 'percentageType',
                    dataSource: 'getPercentageTypes',
                    zone: 'base',
                    class: 'es_data_source'
                });
                break;
            case 'chart':
                commonFields.fields.push({
                    type: 'select',
                    id: `${prefix}chart-type`,
                    label: 'Valor a mostrar:',
                    paramName: 'chartType',
                    dataSource: 'getValueTypes',
                    zone: 'base',
                    class: 'es_data_source'
                });
                commonFields.fields.push({
                    type: 'select',
                    id: `${prefix}chart-display-type`,
                    label: 'Tipo de gráfica:',
                    paramName: 'chartDisplayType',
                    options: [{
                            value: 'line',
                            label: 'Línea'
                        },
                        {
                            value: 'bar',
                            label: 'Barras'
                        },
                        {
                            value: 'pie',
                            label: 'Circular'
                        },
                        {
                            value: 'doughnut',
                            label: 'Anillo'
                        },
                        {
                            value: 'radar',
                            label: 'Radar'
                        },
                        {
                            value: 'polarArea',
                            label: 'Área polar'
                        }
                    ],
                    zone: 'base'
                });
                commonFields.fields.push({
                    type: 'number',
                    id: `${prefix}chart-points`,
                    label: 'Número de puntos:',
                    paramName: 'chartPoints',
                    min: 5,
                    max: 500,
                    defaultValue: 10,
                    zone: 'base'
                });

                // Campo personalizado para la sección de series con selector y botón de añadir en la misma fila
                commonFields.fields.push({
                    type: 'custom',
                    id: `${prefix}chart-series-container`,
                    label: 'Valor para serie:',
                    paramName: 'chartSeriesContainer',
                    zone: 'series',
                    html: `
                        <div class="series-selector-container">
                            <select id="${prefix}chart-series-type" class="es_data_source" data-param-name="chartSeriesType">
                                <option value="">Seleccionar valor</option>
                                ${dataService.getValueTypes().map(type => `<option value="${type.id}">${type.name}</option>`).join('')}
                            </select>
                            <button id="${prefix}chart-add-series" class="add-series-btn" title="Añadir serie" aria-label="Añadir serie" data-param-name="addSeries">+</button>
                        </div>
                    `
                });

                // Añadir un campo informativo para mostrar las series actuales
                commonFields.fields.push({
                    type: 'html',
                    id: `${prefix}chart-series-info`,
                    label: 'Series actuales:',
                    html: '<div id="current-series-list" class="current-series-list">No hay series añadidas</div>',
                    zone: 'series'
                });
                break;
            case 'latest':
                commonFields.fields.push({
                    type: 'select',
                    id: `${prefix}latest-type`,
                    label: 'Valor a mostrar:',
                    paramName: 'latestType',
                    dataSource: 'getValueTypes',
                    zone: 'base',
                    class: 'es_data_source'
                });
                commonFields.fields.push({
                    type: 'number',
                    id: `${prefix}latest-count`,
                    label: 'Número de datos a mostrar:',
                    paramName: 'latestCount',
                    min: 1,
                    max: 20,
                    defaultValue: 5,
                    zone: 'base'
                });
                commonFields.fields.push({
                    type: 'checkbox',
                    id: `${prefix}latest-include-unit`,
                    label: 'Incluir unidad:',
                    paramName: 'includeUnit',
                    defaultValue: true,
                    zone: 'base'
                });
                commonFields.fields.push({
                    type: 'checkbox',
                    id: `${prefix}latest-include-full-time`,
                    label: 'Incluir hora completa:',
                    paramName: 'includeFullTime',
                    defaultValue: false,
                    zone: 'base'
                });
                commonFields.fields.push({
                    type: 'checkbox',
                    id: `${prefix}latest-include-minutes-seconds`,
                    label: 'Incluir minutos y segundos:',
                    paramName: 'includeMinutesSeconds',
                    defaultValue: false,
                    zone: 'base'
                });
                break;
            case 'period-chart':
                commonFields.fields.push({
                    type: 'select',
                    id: `${prefix}period-chart-type`,
                    label: 'Valor a mostrar:',
                    paramName: 'periodChartType',
                    dataSource: 'getValueTypes',
                    zone: 'base',
                    class: 'es_data_source'
                });
                commonFields.fields.push({
                    type: 'number',
                    id: `${prefix}period-chart-points`,
                    label: 'Número de períodos:',
                    paramName: 'periodChartPoints',
                    min: 3,
                    max: 30,
                    defaultValue: 10,
                    zone: 'base'
                });
                commonFields.fields.push({
                    type: 'number',
                    id: `${prefix}period-chart-width`,
                    label: 'Ancho del período (segundos):',
                    paramName: 'periodChartWidth',
                    min: 5,
                    max: 60,
                    defaultValue: 15,
                    zone: 'base'
                });
                break;
            case 'latest-by-periods':
                commonFields.fields.push({
                    type: 'select',
                    id: `${prefix}latest-by-periods-type`,
                    label: 'Valor a mostrar:',
                    paramName: 'latestByPeriodsType',
                    dataSource: 'getValueTypes',
                    zone: 'base',
                    class: 'es_data_source'
                });
                commonFields.fields.push({
                    type: 'number',
                    id: `${prefix}latest-by-periods-points`,
                    label: 'Número de períodos:',
                    paramName: 'latestByPeriodsPoints',
                    min: 3,
                    max: 30,
                    defaultValue: 10,
                    zone: 'base'
                });
                commonFields.fields.push({
                    type: 'number',
                    id: `${prefix}latest-by-periods-width`,
                    label: 'Ancho del período (segundos):',
                    paramName: 'latestByPeriodsWidth',
                    min: 5,
                    max: 60,
                    defaultValue: 15,
                    zone: 'base'
                });
                commonFields.fields.push({
                    type: 'checkbox',
                    id: `${prefix}latest-by-periods-include-unit`,
                    label: 'Incluir unidad:',
                    paramName: 'includeUnit',
                    defaultValue: true,
                    zone: 'base'
                });
                break;
        }

        return commonFields;
    }

    /**
     * Llena los campos del formulario con los datos del widget
     * @param {Object} widget - Widget cuyos datos se usarán para llenar el formulario
     * @param {boolean} isEdit - Indica si es para edición (true) o creación (false)
     * @param {HTMLElement} container - Contenedor donde buscar los elementos (opcional)
     */
    fillWidgetFields(widget, isEdit = true, container = null) {
        //esta rutina la implementa widget_fields_accordion.js o cualquier otro gestor de UI que queramos,
        //por ejemplo podríamos querer usar pestañas en vez de acordeón.
    }

    /**
     * Obtiene los parámetros del widget a partir de los campos del formulario
     * @param {string} widgetType - Tipo de widget
     * @param {boolean} isEdit - Indica si es para edición (true) o creación (false)
     * @param {HTMLElement} container - Contenedor donde buscar los elementos (opcional)
     * @returns {Object} - Objeto con los parámetros del widget
     */
    getWidgetParams(widgetType, isEdit = true, container = null) {
        const prefix = isEdit ? 'edit-' : '';
        const fields = this.getWidgetFields(widgetType, isEdit).fields;
        const params = {};

        console.log(`Obteniendo parámetros para widget de tipo ${widgetType} (isEdit: ${isEdit})`);
        console.log('Campos definidos:', fields);
        console.log('Contenedor:', container);

        // Función auxiliar para obtener el elemento según el contexto
        const getElement = (id) => {
            if (container) {
                // Si se proporciona un contenedor, buscar dentro de él usando el ID original
                // Esto funciona incluso si los IDs no se han modificado al clonar el contenedor
                return container.querySelector(`#${id}`);
            } else {
                // Si no hay contenedor, usar document.getElementById
                return document.getElementById(id);
            }
        };

        fields.forEach(field => {
            const element = getElement(field.id);
            console.log(`Buscando elemento con ID ${field.id}:`, element);

            if (!element) {
                console.warn(`No se encontró el elemento con ID ${field.id}`);

                // Para widgets de texto, asegurar que siempre haya un valor por defecto
                if (widgetType === 'text' && field.paramName === 'text' && !params[field.paramName]) {
                    console.log('Estableciendo texto por defecto para widget de texto');
                    params[field.paramName] = '(contenido del widget)';
                }

                return;
            }

            if (field.type === 'number') {
                params[field.paramName] = parseInt(element.value);
                console.log(`Parámetro numérico ${field.paramName} = ${params[field.paramName]}`);
            } else if (field.type === 'checkbox') {
                params[field.paramName] = element.checked;
                console.log(`Parámetro checkbox ${field.paramName} = ${params[field.paramName]}`);
            } else {
                if (element.dataset.defecto !== undefined)
                    params[field.paramName] = element.dataset.defecto
                else
                    params[field.paramName] = element.value;
                console.log(`Parámetro ${field.paramName} = ${params[field.paramName]}`);
            }
        });

        // Para widgets de texto, asegurar que siempre haya un valor por defecto
        if (widgetType === 'text' && !params['text']) {
            console.log('Estableciendo texto por defecto para widget de texto');
            params['text'] = '(contenido del widget)';
        }

        console.log('Parámetros finales:', params);
        return params;
    }
}

// Acción para resetear el color de fondo
app.acciones.resetWidgetBgColor = function () {
    // Obtener el input de color correspondiente
    const bgColorInput = document.querySelector('[name="backgroundColor"]');
    if (!bgColorInput) return;

    // Mostrar el color de fondo de widgets del dashboard actual
    const widgetBgColor = app.dashboardManager.dashboard.widgetBgColor || '#ffffff';

    // Actualizar el valor y los datos del input
    bgColorInput.value = widgetBgColor;
    bgColorInput.dataset.reset = 'true';
    bgColorInput.dataset.defecto = 'true';
    bgColorInput.dataset.value = ''; // Limpiar el valor personalizado

    console.log('Reset de color de fondo:', {
        value: bgColorInput.value,
        dataset: {
            reset: bgColorInput.dataset.reset,
            defecto: bgColorInput.dataset.defecto,
            value: bgColorInput.dataset.value
        }
    });

    // Disparar un evento de cambio para que se actualice la vista previa
    bgColorInput.dispatchEvent(new Event('change'));
};

// En app.js o donde tengas definidas tus acciones
app.acciones.resetWidgetColor = function (evento) {
    // Obtenemos el botón que ha sido pulsado
    const resetButton = evento.target;

    // Obtenemos qué propiedad estamos reseteando
    const colorProperty = resetButton.dataset.colorProperty;
    if (!colorProperty) {
        console.error('No se pudo determinar qué propiedad de color resetear');
        return;
    }

    // Buscamos el input de color dentro del mismo grupo
    const formGroup = resetButton.closest('.color-field-container');
    const colorInput = formGroup ? formGroup.querySelector('input[type="color"]') : null;
    if (!colorInput) {
        console.error('No se pudo encontrar el input de color');
        return;
    }

    // Determinamos el color por defecto según la propiedad
    let defaultColor = '#ffffff'; // Default genérico

    switch (colorProperty) {
        case 'bgColor':
        case 'backgroundColor':
            defaultColor = app.dashboardManager.dashboard.widgetBgColor || '#ffffff';
            break;
        case 'textColor':
        case 'fontColor':
            defaultColor = app.dashboardManager.dashboard.widgetTextColor || '#000000';
            break;
        case 'borderColor':
            // Determinar color según tema
            const theme = document.documentElement.classList.contains('theme-tron') ? 'tron' :
                (document.documentElement.classList.contains('theme-neumorphic') ? 'neumorphic' :
                    (document.documentElement.classList.contains('theme-azul_nuboso') ? 'azul_nuboso' : 'default'));

            defaultColor = theme === 'tron' ? '#00a2ff' :
                theme === 'neumorphic' ? '#cbd5e0' :
                theme === 'azul_nuboso' ? '#4a6fa5' : '#cccccc';
            break;
    }

    // Actualizamos el valor del input
    colorInput.value = defaultColor;
    colorInput.dataset.defecto = 'true'

    // Disparar un evento de cambio para que se actualice la vista previa
    // colorInput.dispatchEvent(new Event('change'));
};


// Usamos la misma función para todos los resets de color
app.acciones.resetWidgetTextColor = app.acciones.resetWidgetBgColor;
app.acciones.resetWidgetBorderColor = app.acciones.resetWidgetBgColor;

// Crear instancia del gestor de campos de widgets
const widgetFieldsManager = new WidgetFieldsManager();