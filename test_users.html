<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Usuarios</title>
</head>
<body>
    <h1>Test Usuarios en IndexedDB</h1>
    <div id="output"></div>
    
    <script src="js/database_innerdb.js"></script>
    <script>
        const output = document.getElementById('output');
        
        function log(message) {
            console.log(message);
            output.innerHTML += '<p>' + message + '</p>';
        }
        
        async function testUsers() {
            try {
                log('Inicializando base de datos...');
                await dbManager.init();
                log('✅ Base de datos inicializada');
                
                log('Inicializando datos de prueba...');
                await dbManager.initializetestdata();
                log('✅ Datos de prueba inicializados');
                
                log('Obteniendo todos los usuarios...');
                const users = await dbManager.getAllUsers();
                log(`✅ Encontrados ${users.length} usuarios`);
                
                // Mostrar los primeros 10 usuarios
                for (let i = 0; i < Math.min(10, users.length); i++) {
                    const user = users[i];
                    log(`Usuario ${i+1}: login="${user.login}", clave="${user.clave}", empresa=${user.empresaId}, tipo=${user.tipo}`);
                }
                
                log('Probando búsqueda específica del usuario admin...');
                const adminUser = await dbManager.getUserByLogin('admin');
                if (adminUser) {
                    log(`✅ Usuario admin encontrado: login="${adminUser.login}", clave="${adminUser.clave}", empresa=${adminUser.empresaId}`);
                } else {
                    log('❌ Usuario admin NO encontrado');
                }
                
                log('Probando búsqueda de empresa 1...');
                const company1 = await dbManager.getCompany(1);
                if (company1) {
                    log(`✅ Empresa 1 encontrada: nombre="${company1.nombre}", activo=${company1.activo}`);
                } else {
                    log('❌ Empresa 1 NO encontrada');
                }
                
                log('🎉 Todas las pruebas completadas');
                
            } catch (error) {
                log('❌ Error: ' + error.message);
                console.error('Error completo:', error);
            }
        }
        
        // Ejecutar pruebas cuando la página esté cargada
        document.addEventListener('DOMContentLoaded', testUsers);
    </script>
</body>
</html>
