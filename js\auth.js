/**
 * Módulo para la gestión de autenticación
 */
class AuthManager {
    constructor() {
        this._isAuthenticated = false;
        this.user = null;
        this.companyId = null;
        app.authManager = this;
    }

    /**
     * Verifica si el usuario está autenticado
     * @returns {boolean} - true si el usuario está autenticado
     */
    isAuthenticated() {
        return this._isAuthenticated;
    }

    /**
     * Inicializa el gestor de autenticación
     */
    init() {
        // Verificar que dbManager existe antes de usarlo
        if (typeof dbManager === 'undefined') {
            console.error('Error: dbManager no está definido. Asegúrate de que database.js se carga antes que auth.js');
            // Intentar inicializar de todos modos para no bloquear la aplicación
            this.checkSession();
            this.setupAuthEvents();
            return;
        }

        // Inicializar la base de datos
        dbManager.init()
            .then(() => dbManager.initializetestdata())
            .then(() => {
                // Comprobar si hay una sesión guardada
                this.checkSession();

                // Configurar eventos de autenticación
                this.setupAuthEvents();
            })
            .catch(error => {
                console.error('Error al inicializar la autenticación:', error);
            });
    }

    /**
     * Configura los eventos relacionados con la autenticación
     */
    setupAuthEvents() {
        const loginBtn = document.getElementById('login-btn');
        if (loginBtn) {
            loginBtn.addEventListener('click', this.handleLogin.bind(this));
        }

        /* const logoutBtn = document.getElementById('logout-btn');
         if (logoutBtn) {
             logoutBtn.addEventListener('click', this.handleLogout.bind(this));
         }*/

        // Configurar eventos de teclado para los campos de login
        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');

        if (usernameInput) {
            usernameInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    passwordInput.focus();
                }
            });
        }

        if (passwordInput) {
            passwordInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.handleLogin();
                }
            });
        }
    }

    /**
     * Maneja el evento de inicio de sesión
     */
    handleLogin() {
        const login = document.getElementById('username').value;
        const elem_password = document.getElementById('password');
        const password = elem_password.value;
        const errorElement = document.getElementById('login-error');

        // Validar credenciales contra la base de datos
        dbManager.getUserByLogin(login)
            .then(user => {
                if (user && user.clave === password) {
                    // Obtener la empresa del usuario
                    return dbManager.getCompany(user.empresaId)
                        .then(company => {
                            if (company && company.activo) {
                                this.login(user, user.empresaId);
                                errorElement.textContent = '';
                            } else {
                                // Mostrar notificación en lugar de mensaje en la pantalla
                                showNotification('La empresa no está activa', 'error');
                                errorElement.textContent = '';
                            }
                            return null;
                        });
                } else {
                    // Mostrar notificación en lugar de mensaje en la pantalla
                    showNotification('Usuario o contraseña incorrectos', 'error');
                    errorElement.textContent = '';
                    return null;
                }
            })
            .catch(error => {
                console.error('Error al iniciar sesión:', error);
                // Mostrar notificación en lugar de mensaje en la pantalla
                showNotification('Error al iniciar sesión', 'error');
                errorElement.textContent = '';
            });

        elem_password.value = '';
    }

    /**
     * Maneja el evento de cierre de sesión
     */
    handleLogout() {
        this.logout();
    }

    /**
     * Inicia sesión con un usuario
     * @param {Object} user - Usuario
     * @param {number} companyId - ID de la empresa
     */
    login(user, companyId) {
        this._isAuthenticated = true;
        this.user = user;
        this.companyId = companyId;

        // Guardar sesión en localStorage
        localStorage.setItem('ipram_session', JSON.stringify({
            isAuthenticated: true,
            user: user,
            companyId: companyId
        }));

        //Menú: cada menú puede ser para un usaurio concreto
        window.mainMenuManager = new MainMenuManager();
        window.mainMenuManager.init();
        window.mainMenuManager.loadMenu();

        // Inicializar el dashboard después del login exitoso
        window.dashboardManager = new DashboardManager();

        // Inicializar con la empresa del usuario y esperar a que termine
        window.dashboardManager.init(companyId).then(() => {
            // Mostrar la aplicación solo después de que se hayan cargado los tableros
            this.showApp();
        });
    }
    /**
     * Cierra la sesión actual
     */
    logout() {
        this._isAuthenticated = false;
        this.user = null;
        this.companyId = null;

        // Eliminar sesión de localStorage
        localStorage.removeItem('ipram_session');
        themeManager.loadSavedTheme();

        // Limpiar la pila de UI antes de mostrar la pantalla de login
        emptyUIStack();

        // Ocultar todos los contenedores para evitar superposiciones
        hideAllContainers();

        // Mostrar pantalla de login
        this.showLogin();
    }

    /**
     * Comprueba si hay una sesión guardada
     */
    checkSession() {
        const session = localStorage.getItem('ipram_session');
        if (session) {
            const {
                isAuthenticated,
                user,
                companyId
            } = JSON.parse(session);

            if (isAuthenticated && user && companyId) {
                // Verificar que el usuario y la empresa existen y están activos
                dbManager.getUserByLogin(user.login)
                    .then(dbUser => {
                        if (dbUser) {
                            return dbManager.getCompany(companyId)
                                .then(company => {
                                    if (company && company.activo) {
                                        this.login(dbUser, companyId);
                                    } else {
                                        this.showLogin();
                                    }
                                });
                        } else {
                            this.showLogin();
                        }
                    })
                    .catch(error => {
                        console.error('Error al verificar la sesión:', error);
                        this.showLogin();
                    });
                return;
            }
        }

        // Si no hay sesión válida, mostrar login
        this.showLogin();
    }

    /**
     * Muestra la pantalla de login
     */
    showLogin() {
        // Usar showContainer que ya implementa la animación
        showContainer('login-container');
        updateUrlHash('login');

        // Dar foco a la caja de login después de la animación
        const loginContainer = document.getElementById('login-container');
        if (loginContainer) {
            // Si el contenedor tiene la clase de animación, esperar a que termine
            if (loginContainer.classList.contains('container-entering')) {
                loginContainer.addEventListener('animationend', function focusHandler() {
                    const usernameInput = document.getElementById('username');
                    if (usernameInput) {
                        usernameInput.focus();
                    }
                    // Eliminar el listener para evitar duplicados
                    loginContainer.removeEventListener('animationend', focusHandler);
                });
            } else {
                // Si no hay animación, dar foco inmediatamente
                requestAnimationFrame(() => {
                    const usernameInput = document.getElementById('username');
                    if (usernameInput) {
                        usernameInput.focus();
                    }
                });
            }
        }
    }

    /**
     * Muestra la aplicación
     */
    showApp() {
        const fullHash = window.location.hash.substring(1); // Eliminar el # del inicio

        if (!fullHash || fullHash === 'login') {
            navigateTo('main-menu');
            return;
        }

        // Delegar la navegación a navigateToHash que ya contiene toda la lógica necesaria
        navigateToHash(fullHash);
    }
}

// Crear instancia del gestor de autenticación
const authManager = new AuthManager();