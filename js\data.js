/**
 * Módulo para la generación de datos aleatorios que simulan una API REST
 */
class DataService {
    constructor() {
        this.data = {};
        this.dataHistory = {}; // Histórico de datos para las gráficas
        this.gaugeTypes = [{
                id: 'temperature',
                name: 'Temperatura',
                min: -10,
                max: 40,
                unit: '°C'
            },
            {
                id: 'pressure',
                name: 'Presión',
                min: 980,
                max: 1050,
                unit: 'hPa'
            },
            {
                id: 'wind_speed',
                name: 'Velocidad del viento',
                min: 0,
                max: 100,
                unit: 'km/h'
            },
            {
                id: 'humidity',
                name: '<PERSON><PERSON>',
                min: 0,
                max: 100,
                unit: '%'
            }
        ];

        this.percentageTypes = [{
                id: 'battery',
                name: 'Bater<PERSON>'
            },
            {
                id: 'memory',
                name: 'Memoria'
            },
            {
                id: 'cpu',
                name: 'CPU'
            },
            {
                id: 'disk',
                name: 'Disco'
            }
        ];

        this.valueTypes = [{
                id: 'temperature',
                name: 'Temperatura',
                unit: '°C'
            },
            {
                id: 'pressure',
                name: 'Presión',
                unit: 'hPa'
            },
            {
                id: 'wind_speed',
                name: 'Velocidad del viento',
                unit: 'km/h'
            },
            {
                id: 'wind_direction',
                name: 'Dirección del viento',
                unit: '°'
            },
            {
                id: 'humidity',
                name: 'Humedad',
                unit: '%'
            },
            {
                id: 'rainfall',
                name: 'Precipitación',
                unit: 'mm'
            }
        ];

        // Inicializar histórico de datos
        this.valueTypes.forEach(type => {
            this.dataHistory[type.id] = {
                timestamps: [],
                values: [],
                maxItems: 50 // Máximo número de puntos a almacenar
            };
        });

        this.percentageTypes.forEach(type => {
            this.dataHistory[type.id] = {
                timestamps: [],
                values: [],
                maxItems: 50
            };
        });

        // Inicializar datos
        this.generateRandomData();

        // Actualizar datos cada 5 segundos
        setInterval(() => this.generateRandomData(), 5000);
    }

    /**
     * Genera datos aleatorios para todos los tipos de valores
     */
    generateRandomData() {
        // Generar valores para los tipos de valores
        this.valueTypes.forEach(type => {
            let value;
            switch (type.id) {
                case 'temperature':
                    value = this.getRandomNumber(-10, 40, 1);
                    break;
                case 'pressure':
                    value = this.getRandomNumber(980, 1050, 0);
                    break;
                case 'wind_speed':
                    value = this.getRandomNumber(0, 100, 1);
                    break;
                case 'wind_direction':
                    value = this.getRandomNumber(0, 360, 0);
                    break;
                case 'humidity':
                    value = this.getRandomNumber(0, 100, 0);
                    break;
                case 'rainfall':
                    value = this.getRandomNumber(0, 50, 1);
                    break;
                default:
                    value = 0;
            }

            this.data[type.id] = {
                value: value,
                unit: type.unit
            };

            // Actualizar histórico de datos
            this.updateDataHistory(type.id, value);
        });

        // Generar valores para los tipos de porcentaje
        this.percentageTypes.forEach(type => {
            this.data[type.id] = {
                value: this.getRandomNumber(0, 100, 0),
                unit: '%'
            };

            // Actualizar histórico de datos
            this.updateDataHistory(type.id, this.data[type.id].value);
        });

        // Disparar evento de actualización de datos
        const event = new CustomEvent('dataUpdated', {
            detail: this.data
        });
        document.dispatchEvent(event);
        // console.log('Evento dataUpdated emitido con datos:', this.data);
    }

    /**
     * Actualiza el histórico de datos para un tipo de dato
     * @param {string} typeId - ID del tipo de dato
     * @param {number} value - Valor actual del tipo de dato
     */
    updateDataHistory(typeId, value) {
        const history = this.dataHistory[typeId];
        const now = new Date().getTime();

        // Agregar nuevo punto al histórico
        history.timestamps.push(now);
        history.values.push(value);

        // Mantener solo el máximo número de puntos permitidos
        if (history.timestamps.length > history.maxItems) {
            history.timestamps.shift();
            history.values.shift();
        }
    }

    /**
     * Genera un número aleatorio entre min y max con la precisión especificada
     * @param {number} min - Valor mínimo
     * @param {number} max - Valor máximo
     * @param {number} decimals - Número de decimales
     * @returns {number} - Número aleatorio
     */
    getRandomNumber(min, max, decimals) {
        const random = Math.random() * (max - min) + min;
        return Number(random.toFixed(decimals));
    }

    /**
     * Obtiene los tipos de gauge disponibles
     * @returns {Array} - Array de tipos de gauge
     */
    getGaugeTypes() {
        return this.gaugeTypes;
    }

    /**
     * Obtiene los tipos de porcentaje disponibles
     * @returns {Array} - Array de tipos de porcentaje
     */
    getPercentageTypes() {
        return this.percentageTypes;
    }

    /**
     * Obtiene los tipos de valores disponibles
     * @returns {Array} - Array de tipos de valores
     */
    getValueTypes() {
        return this.valueTypes;
    }

    /**
     * Obtiene el valor actual de un tipo de dato
     * @param {string} typeId - ID del tipo de dato
     * @returns {Object} - Objeto con el valor y la unidad
     */
    getValue(typeId) {
        return this.data[typeId] || {
            value: 0,
            unit: ''
        };
    }

    /**
     * Obtiene el histórico de datos de un tipo de dato
     * @param {string} typeId - ID del tipo de dato
     * @param {number} maxPoints - Número máximo de puntos a devolver (los más recientes)
     * @returns {Object} - Objeto con los timestamps y valores del histórico
     */
    getDataHistory(typeId, maxPoints = 10) {
        const history = this.dataHistory[typeId];
        if (!history) return {
            timestamps: [],
            values: []
        };

        // Si se solicitan menos puntos de los que hay, devolver solo los más recientes
        if (maxPoints < history.timestamps.length) {
            const startIndex = history.timestamps.length - maxPoints;
            return {
                timestamps: history.timestamps.slice(startIndex),
                values: history.values.slice(startIndex)
            };
        }

        return {
            timestamps: [...history.timestamps],
            values: [...history.values]
        };
    }
}

// Crear instancia del servicio de datos
const dataService = new DataService();