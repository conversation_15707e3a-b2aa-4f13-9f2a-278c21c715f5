#!/bin/bash

# Script de configuración para IPRA_I
# Permite configurar puerto, configuración regional y otras opciones antes de la instalación

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para logging
log() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARN] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

# Función para leer input con valor por defecto
read_with_default() {
    local prompt="$1"
    local default="$2"
    local var_name="$3"
    
    echo -n -e "${BLUE}$prompt [$default]: ${NC}"
    read input
    
    if [ -z "$input" ]; then
        eval "$var_name='$default'"
    else
        eval "$var_name='$input'"
    fi
}

# Función para validar puerto
validate_port() {
    local port="$1"
    
    if ! [[ "$port" =~ ^[0-9]+$ ]]; then
        return 1
    fi
    
    if [ "$port" -lt 1 ] || [ "$port" -gt 65535 ]; then
        return 1
    fi
    
    return 0
}

# Función para verificar si el puerto está en uso
check_port_available() {
    local port="$1"
    
    if command -v netstat &> /dev/null; then
        if netstat -tuln | grep -q ":$port "; then
            return 1
        fi
    elif command -v ss &> /dev/null; then
        if ss -tuln | grep -q ":$port "; then
            return 1
        fi
    fi
    
    return 0
}

# Función para configurar puerto
configure_port() {
    log "=== CONFIGURACIÓN DEL PUERTO ==="
    
    local current_port=""
    if [ -f "config.conf" ]; then
        current_port=$(grep "^SERVER_PORT=" config.conf 2>/dev/null | cut -d'=' -f2 || echo "")
    fi
    
    local default_port="${current_port:-3000}"
    
    while true; do
        read_with_default "Puerto del servidor" "$default_port" "SERVER_PORT"
        
        if validate_port "$SERVER_PORT"; then
            if check_port_available "$SERVER_PORT"; then
                log "Puerto $SERVER_PORT configurado correctamente"
                break
            else
                warn "El puerto $SERVER_PORT parece estar en uso"
                echo -n -e "${YELLOW}¿Continuar de todas formas? (y/N): ${NC}"
                read continue_anyway
                if [[ "$continue_anyway" =~ ^[Yy]$ ]]; then
                    break
                fi
            fi
        else
            error "Puerto inválido. Debe ser un número entre 1 y 65535"
        fi
    done
}

# Función para configurar región
configure_locale() {
    log "=== CONFIGURACIÓN REGIONAL ==="
    
    local current_locale=""
    if [ -f "config.conf" ]; then
        current_locale=$(grep "^LC_NUMERIC=" config.conf 2>/dev/null | cut -d'=' -f2 || echo "")
    fi
    
    local default_locale="${current_locale:-es_ES}"
    
    echo "Configuraciones regionales disponibles:"
    echo "  es_ES - España (números con coma decimal: 1,23)"
    echo "  en_US - Estados Unidos (números con punto decimal: 1.23)"
    echo "  fr_FR - Francia (números con coma decimal: 1,23)"
    echo "  de_DE - Alemania (números con coma decimal: 1,23)"
    echo ""
    
    read_with_default "Configuración regional" "$default_locale" "LC_NUMERIC"
    
    log "Configuración regional establecida: $LC_NUMERIC"
}

# Función para configurar directorios
configure_directories() {
    log "=== CONFIGURACIÓN DE DIRECTORIOS ==="
    
    local current_build_dir=""
    local current_docs_dir=""
    local current_log_dir=""
    
    if [ -f "config.conf" ]; then
        current_build_dir=$(grep "^BUILD_DIR=" config.conf 2>/dev/null | cut -d'=' -f2 || echo "")
        current_docs_dir=$(grep "^DOCS_DIR=" config.conf 2>/dev/null | cut -d'=' -f2 || echo "")
        current_log_dir=$(grep "^LOG_DIR=" config.conf 2>/dev/null | cut -d'=' -f2 || echo "")
    fi
    
    read_with_default "Directorio de build" "${current_build_dir:-build}" "BUILD_DIR"
    read_with_default "Directorio de documentación" "${current_docs_dir:-docs_generated}" "DOCS_DIR"
    read_with_default "Directorio de logs" "${current_log_dir:-logs}" "LOG_DIR"
    
    log "Directorios configurados:"
    log "  Build: $BUILD_DIR"
    log "  Documentación: $DOCS_DIR"
    log "  Logs: $LOG_DIR"
}

# Función para configurar archivos de build
configure_build_files() {
    log "=== CONFIGURACIÓN DE ARCHIVOS DE BUILD ==="
    
    local current_js_file=""
    local current_css_file=""
    
    if [ -f "config.conf" ]; then
        current_js_file=$(grep "^COMBINED_JS_FILE=" config.conf 2>/dev/null | cut -d'=' -f2 || echo "")
        current_css_file=$(grep "^COMBINED_CSS_FILE=" config.conf 2>/dev/null | cut -d'=' -f2 || echo "")
    fi
    
    read_with_default "Archivo JavaScript combinado" "${current_js_file:-aplicacion.js}" "COMBINED_JS_FILE"
    read_with_default "Archivo CSS combinado" "${current_css_file:-aplicacion.css}" "COMBINED_CSS_FILE"
    
    log "Archivos de build configurados:"
    log "  JavaScript: $COMBINED_JS_FILE"
    log "  CSS: $COMBINED_CSS_FILE"
}

# Función para configurar WebSockets
configure_websockets() {
    log "=== CONFIGURACIÓN DE WEBSOCKETS ==="
    
    local current_host=""
    local current_reconnect_initial=""
    local current_reconnect_max=""
    
    if [ -f "config.conf" ]; then
        current_host=$(grep "^SERVER_HOST=" config.conf 2>/dev/null | cut -d'=' -f2 || echo "")
        current_reconnect_initial=$(grep "^WEBSOCKET_RECONNECT_INITIAL=" config.conf 2>/dev/null | cut -d'=' -f2 || echo "")
        current_reconnect_max=$(grep "^WEBSOCKET_RECONNECT_MAX=" config.conf 2>/dev/null | cut -d'=' -f2 || echo "")
    fi
    
    read_with_default "Host del servidor" "${current_host:-localhost}" "SERVER_HOST"
    read_with_default "Tiempo inicial de reconexión (ms)" "${current_reconnect_initial:-1000}" "WEBSOCKET_RECONNECT_INITIAL"
    read_with_default "Tiempo máximo de reconexión (ms)" "${current_reconnect_max:-30000}" "WEBSOCKET_RECONNECT_MAX"
    
    log "WebSockets configurado:"
    log "  Host: $SERVER_HOST"
    log "  Reconexión inicial: ${WEBSOCKET_RECONNECT_INITIAL}ms"
    log "  Reconexión máxima: ${WEBSOCKET_RECONNECT_MAX}ms"
}

# Función para configurar logging
configure_logging() {
    log "=== CONFIGURACIÓN DE LOGGING ==="
    
    local current_log_level=""
    
    if [ -f "config.conf" ]; then
        current_log_level=$(grep "^LOG_LEVEL=" config.conf 2>/dev/null | cut -d'=' -f2 || echo "")
    fi
    
    echo "Niveles de log disponibles:"
    echo "  debug - Información detallada para desarrollo"
    echo "  info  - Información general (recomendado)"
    echo "  warn  - Solo advertencias y errores"
    echo "  error - Solo errores"
    echo ""
    
    read_with_default "Nivel de log" "${current_log_level:-info}" "LOG_LEVEL"
    
    log "Nivel de log configurado: $LOG_LEVEL"
}

# Función para guardar configuración
save_config() {
    log "=== GUARDANDO CONFIGURACIÓN ==="
    
    # Crear backup si existe configuración anterior
    if [ -f "config.conf" ]; then
        cp config.conf "config.conf.backup.$(date +%Y%m%d_%H%M%S)"
        log "Backup de configuración anterior creado"
    fi
    
    # Generar nueva configuración
    cat > config.conf << EOF
# Configuración centralizada del proyecto IPRA_I
# Generado automáticamente el $(date)
# Todos los scripts deben hacer: source config.conf

# === CONFIGURACIÓN DEL SERVIDOR ===
# Puerto donde correrá el servidor Node.js
SERVER_PORT=$SERVER_PORT

# Host del servidor (normalmente localhost para desarrollo)
SERVER_HOST=$SERVER_HOST

# === CONFIGURACIÓN REGIONAL ===
# Configuración de números (es_ES usa coma decimal, en_US usa punto)
# Valores: es_ES, en_US, etc.
LC_NUMERIC=$LC_NUMERIC

# === CONFIGURACIÓN DE BUILD ===
# Directorio donde se generarán los archivos minificados
BUILD_DIR=$BUILD_DIR

# Nombre del archivo JavaScript combinado
COMBINED_JS_FILE=$COMBINED_JS_FILE

# Nombre del archivo CSS combinado  
COMBINED_CSS_FILE=$COMBINED_CSS_FILE

# === CONFIGURACIÓN DE DOCUMENTACIÓN ===
# Directorio para documentación generada
DOCS_DIR=$DOCS_DIR

# === CONFIGURACIÓN DE WEBSOCKETS ===
# Tiempo de reconexión inicial en milisegundos
WEBSOCKET_RECONNECT_INITIAL=$WEBSOCKET_RECONNECT_INITIAL

# Tiempo máximo de reconexión en milisegundos
WEBSOCKET_RECONNECT_MAX=$WEBSOCKET_RECONNECT_MAX

# === CONFIGURACIÓN DE LOGS ===
# Nivel de log (debug, info, warn, error)
LOG_LEVEL=$LOG_LEVEL

# Directorio de logs
LOG_DIR=$LOG_DIR
EOF
    
    log "Configuración guardada en config.conf"
}

# Función para mostrar resumen
show_summary() {
    log "=== RESUMEN DE CONFIGURACIÓN ==="
    echo ""
    echo -e "${BLUE}Servidor:${NC}"
    echo "  Host: $SERVER_HOST"
    echo "  Puerto: $SERVER_PORT"
    echo ""
    echo -e "${BLUE}Regional:${NC}"
    echo "  Configuración numérica: $LC_NUMERIC"
    echo ""
    echo -e "${BLUE}Directorios:${NC}"
    echo "  Build: $BUILD_DIR"
    echo "  Documentación: $DOCS_DIR"
    echo "  Logs: $LOG_DIR"
    echo ""
    echo -e "${BLUE}Archivos de build:${NC}"
    echo "  JavaScript: $COMBINED_JS_FILE"
    echo "  CSS: $COMBINED_CSS_FILE"
    echo ""
    echo -e "${BLUE}WebSockets:${NC}"
    echo "  Reconexión inicial: ${WEBSOCKET_RECONNECT_INITIAL}ms"
    echo "  Reconexión máxima: ${WEBSOCKET_RECONNECT_MAX}ms"
    echo ""
    echo -e "${BLUE}Logging:${NC}"
    echo "  Nivel: $LOG_LEVEL"
    echo ""
}

# Función para configuración interactiva
interactive_config() {
    log "=== CONFIGURACIÓN INTERACTIVA DE IPRA_I ==="
    echo ""
    
    configure_port
    echo ""
    configure_locale
    echo ""
    configure_directories
    echo ""
    configure_build_files
    echo ""
    configure_websockets
    echo ""
    configure_logging
    echo ""
    
    show_summary
    
    echo -n -e "${YELLOW}¿Guardar esta configuración? (Y/n): ${NC}"
    read save_confirm
    
    if [[ ! "$save_confirm" =~ ^[Nn]$ ]]; then
        save_config
        log "Configuración completada. Ejecuta './install.sh' para instalar."
    else
        warn "Configuración cancelada"
        exit 1
    fi
}

# Función para configuración rápida
quick_config() {
    local port="${1:-3000}"
    local locale="${2:-es_ES}"
    
    log "=== CONFIGURACIÓN RÁPIDA ==="
    
    if ! validate_port "$port"; then
        error "Puerto inválido: $port"
    fi
    
    SERVER_PORT="$port"
    SERVER_HOST="localhost"
    LC_NUMERIC="$locale"
    BUILD_DIR="build"
    COMBINED_JS_FILE="aplicacion.js"
    COMBINED_CSS_FILE="aplicacion.css"
    DOCS_DIR="docs_generated"
    WEBSOCKET_RECONNECT_INITIAL="1000"
    WEBSOCKET_RECONNECT_MAX="30000"
    LOG_LEVEL="info"
    LOG_DIR="logs"
    
    show_summary
    save_config
    
    log "Configuración rápida completada"
}

# Función para mostrar ayuda
show_help() {
    echo "Uso: $0 [OPCIONES]"
    echo ""
    echo "Opciones:"
    echo "  -h, --help              Mostrar esta ayuda"
    echo "  -i, --interactive       Configuración interactiva (por defecto)"
    echo "  -q, --quick [PUERTO] [LOCALE]  Configuración rápida"
    echo "  --show                  Mostrar configuración actual"
    echo ""
    echo "Ejemplos:"
    echo "  $0                      # Configuración interactiva"
    echo "  $0 -q 8080 en_US        # Configuración rápida: puerto 8080, inglés"
    echo "  $0 --show               # Mostrar configuración actual"
    echo ""
}

# Función para mostrar configuración actual
show_current_config() {
    if [ ! -f "config.conf" ]; then
        warn "No existe archivo de configuración"
        exit 1
    fi
    
    log "=== CONFIGURACIÓN ACTUAL ==="
    echo ""
    
    source config.conf
    
    echo -e "${BLUE}Archivo:${NC} config.conf"
    echo -e "${BLUE}Servidor:${NC} $SERVER_HOST:$SERVER_PORT"
    echo -e "${BLUE}Regional:${NC} $LC_NUMERIC"
    echo -e "${BLUE}Build:${NC} $BUILD_DIR"
    echo -e "${BLUE}Logs:${NC} $LOG_DIR ($LOG_LEVEL)"
    echo ""
}

# Función principal
main() {
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        --show)
            show_current_config
            ;;
        -q|--quick)
            quick_config "$2" "$3"
            ;;
        -i|--interactive|"")
            interactive_config
            ;;
        *)
            error "Opción desconocida: $1. Usa -h para ayuda."
            ;;
    esac
}

# Ejecutar función principal
main "$@"
