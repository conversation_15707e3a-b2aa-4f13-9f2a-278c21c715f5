@echo off
REM Script de configuración simple para IPRA_I (Windows)
REM Configuración rápida con valores por defecto

setlocal enabledelayedexpansion

echo [INFO] === CONFIGURACIÓN SIMPLE IPRA_I ===
echo.

REM Configuración por defecto
set "SERVER_PORT=3000"
set "SERVER_HOST=localhost"
set "LC_NUMERIC=es_ES"
set "BUILD_DIR=build"
set "COMBINED_JS_FILE=aplicacion.js"
set "COMBINED_CSS_FILE=aplicacion.css"
set "DOCS_DIR=docs_generated"
set "WEBSOCKET_RECONNECT_INITIAL=1000"
set "WEBSOCKET_RECONNECT_MAX=30000"
set "LOG_LEVEL=info"
set "LOG_DIR=logs"

REM Solo preguntar puerto
set /p "INPUT_PORT=Puerto del servidor (Enter para 3000): "
if not "!INPUT_PORT!"=="" set "SERVER_PORT=!INPUT_PORT!"

echo.
echo [INFO] Configuración aplicada:
echo   Puerto: %SERVER_PORT%
echo   Host: %SERVER_HOST%
echo   Regional: %LC_NUMERIC%
echo   Build: %BUILD_DIR%
echo.

REM Guardar configuración
set "CONFIG_FILE=%~dp0config.conf"

(
echo # Configuración centralizada del proyecto IPRA_I
echo # Generado automáticamente el %date% %time%
echo.
echo SERVER_PORT=%SERVER_PORT%
echo SERVER_HOST=%SERVER_HOST%
echo LC_NUMERIC=%LC_NUMERIC%
echo BUILD_DIR=%BUILD_DIR%
echo COMBINED_JS_FILE=%COMBINED_JS_FILE%
echo COMBINED_CSS_FILE=%COMBINED_CSS_FILE%
echo DOCS_DIR=%DOCS_DIR%
echo WEBSOCKET_RECONNECT_INITIAL=%WEBSOCKET_RECONNECT_INITIAL%
echo WEBSOCKET_RECONNECT_MAX=%WEBSOCKET_RECONNECT_MAX%
echo LOG_LEVEL=%LOG_LEVEL%
echo LOG_DIR=%LOG_DIR%
) > "%CONFIG_FILE%"

echo [INFO] Configuración guardada en %CONFIG_FILE%
echo [INFO] Ejecuta 'scripts\install.bat' para continuar.
echo.
pause

endlocal
