



















































# Architecture and Code Organization
- The application uses a global 'app' object where each class registers its instance during construction (e.g., 'app["widgetManager"] = this;').
- UI elements are managed with functions like openPopup/closePopup, openModal/closeModal, closeContainer() and a UI stack managed by pushToUIStack(), popFromUIStack() and peekUIStack().
- User prefers organizing code in separate JS files by functionality, using event delegation pattern, and implementing dot notation in data-accion attributes.
- User prefers using document.querySelector with class selectors instead of ID selectors for elements that might be cloned or duplicated.
- User prefers centralizing DOM node removal in popFromUIStack function to ensure all cloned nodes are properly removed from the DOM when windows/dialogs are closed.
- The codebase has both users.js and usuarios.js files that appear to serve the same purpose, suggesting potential code duplication.

# UI Elements and Navigation
- UI element types include 'popup', 'modal', and 'container', with corresponding close functions.
- Multiple modals can be open simultaneously by cloning nodes from the HTML rather than reusing the same modal elements.
- User prefers entity edit/create forms and modal screens to be draggable/movable with touch or mouse.
- User prefers replacing JavaScript alerts/confirms with custom themed dialog components that have close buttons, can be dragged by title, and include animations.
- User prefers deletion confirmation dialogs to have a reddish tint, fully red border and red-tinted delete buttons to visually indicate the destructive nature of the action.
- Implement URL hash-based navigation (#menu, #empresas, #login, #tablero) with special handling for login and main screens.
- User prefers dynamic URL hash management where entity handlers register their hash mappings when initialized.
- Error handling should show dialog messages instead of console.log, and serious errors should trigger logout and return to login screen.
- When no current user is detected, the application should redirect to the login screen instead of throwing errors.
- When there's an error retrieving users, show an error message and navigate to the main screen instead of logging out.
- When navigating from main menu to login screen via logout icon, the login screen and main menu remain overlapped instead of properly replacing each other.
- User prefers close buttons in forms to be aligned with the title at the top-right corner, not lower in the form.
- In entity forms, textarea fields should occupy the full width of the form.
- When a dashboard is loaded from the dashboard list (via double-click or other methods), it should be treated as a container that hides previous content rather than being superimposed on the list.
- User prefers form focus to automatically go to the first field when loaded.
- User prefers the dashboard configuration screen to have a cancel button (icon only) and a close button (X) in the top-right corner at the same level as the title, while keeping the rest of the current design.
- User prefers popup windows to have both a cancel button and a close (X) button for exiting.
- User prefers delete buttons to match selected button style.
- User prefers the dashboard menu to close when clicking outside it.
- User prefers dashboard screens to be cloned with changed IDs to prevent empty loading.

# Styling and Themes
- User prefers a futuristic Tron-style interface theme and wants multiple themes including Tron and neumorphic.
- New styles should be added to styles2.css which loads after styles.css, with separate secondary CSS files for each theme.
- User prefers generic CSS rules to be moved to styles2.css and theme-specific rules to their corresponding theme files.
- User prefers more visible UI elements across all themes: confirmation dialogs, company tables with dark headers and visible borders, wider scroll bars, and clearly visible navigation arrows.
- User prefers flat and minimalist icons over realistic ones for UI elements.

# Mobile and Responsive Design
- Application should work on mobile devices with both mouse and touch support, including navigation arrows for scrolling.
- User prefers compact mobile headers with hamburger menu icons (three dots) instead of text buttons, and replacing large logout buttons with flat X icons.
- User prefers swipe gestures to only work in blank areas of the interface, not in text input fields where it could interfere with text selection.
- User prefers mobile UI optimizations: smaller buttons with only icons arranged in a single row with distributed width.
- On mobile entity lists, hide the menu icon and make the back icon visible.

# Dashboard and Widget Management
- User wants multiple dashboards with ability to create, delete, list and switch between dashboards, with persistence in localStorage.
- Widgets should have customizable properties including text content, type, value, individual color settings, and position:absolute for proper movement.
- User prefers default heights for widgets (64px for text widgets, 150px for others) and wants a resize handle in the bottom-right corner.
- Dashboards belong to companies, with specific access rules: admin users from company 1 can create dashboards for any company, while regular users can only view/edit dashboards from their own company, with permissions based on user type (Admin/Editor/Viewer).
- In dashboard entities, company ID field should be set as editable=false to hide it in edit forms, and dashboards should include a 'Observ' textarea field and an 'Activo' checkbox field (defaulting to true).
- Dashboard creation forms should close and load the new dashboard after saving.
- Dashboard configuration screens should be displayed as modals on top of the dashboard, appearing above it and centered on screen.
- User prefers modal forms (like add/edit widget forms) to appear above the dashboard, centered on screen, rather than inside the dashboard area.
- User prefers widget add/edit forms to appear outside and above the dashboard rather than inside it, and wants widget edit modals to be movable.
- In dashboard widget edit forms, 'RESTABLECER' buttons should change text to 'DEFECTO', the widget name box should be hidden, and the delete button should have a red tint.
- Double-clicking a widget should show its edit form; text color changes should affect the widget; border/background color changes should override dashboard settings; and DEFECTO buttons should apply the dashboard's border and color configuration.
- User prefers widget colors to use 'defecto' as a value (not null) that inherits from dashboard colors, allowing widgets to automatically update when dashboard colors change, and when editing widgets only changed colors should be saved while others remain as they were.
- In widget edit forms: color buttons (text/border/background) should affect only their specific property; 'DEFECTO' buttons should set only their specific color to 'defecto'; when saving, only modified colors should be saved while others remain unchanged; and when opening the form, colors should properly display either the widget's specific color or the dashboard's default.

# Entity Management
- EntityManager should support multiple simultaneous entity screens with a navigateTo() method that creates new instances.
- User prefers init() methods to return Promises for asynchronous operations, and navigateTo should wait for Promise resolution before calling show().
- Entity tables should have fixed columns for selection checkboxes and action buttons, filterable/sortable data, configurable columns, and consistent styling across themes.
- Entity screens should support unique normalized names, circular navigation between records with gesture support, contextual options menu with New/Delete buttons, and state-aware UI.
- User prefers password fields (type='password') to be hidden in column configuration screens and should not be displayed in listings. Password fields should not appear in entity forms at all; password editing should only be handled through a specific dedicated option.
- User prefers a 'tipo' field with three roles: 'Administrador' (can edit/delete/create users), 'Editar Tableros' (can create/edit/delete dashboards), and 'Visor Tableros' (can only view dashboards).
- Users should be associated with companies, with a 'View users' option in the company row action menu that shows users filtered by company ID.
- The 'usuarios' hash should load users from the current user's company, and going back from 'usuarios' should return to the companies screen, with admin users of company 1 seeing all companies while other users only see their own company.
- When a user is of type 'admin', they see the Companies button on the main screen, but users not from company 1 can only see and edit their own company, while users from company 1 can see all companies.
- When the user is not from company 1, the menu option in the main screen should be 'Empresa' (singular) instead of 'Empresas' (plural).
- Entity lists with double-click behavior should be handled by specialized methods like getDobleClickRow() in EntityHandlers.
- User prefers double-clicking on company rows to show the dashboard screen instead of the company form, similar to how the tableros entity implements handleRowDobleClick().

# Database Optimization
- For efficiency with large datasets, use specific database queries like dbManager.getCompanyById(id) instead of retrieving all records and filtering. Use the existing function to retrieve a company by ID instead of creating a new one called getCompanyById, as this will be important when implementing the REST API with a real database.