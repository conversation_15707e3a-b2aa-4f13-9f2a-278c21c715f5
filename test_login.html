<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login</title>
</head>
<body>
    <h1>Test Login Específico</h1>
    <div>
        <input type="text" id="testLogin" placeholder="Login" value="admin">
        <input type="password" id="testPassword" placeholder="Password" value="super">
        <button onclick="testLoginFunction()">Test Login</button>
    </div>
    <div id="output"></div>
    
    <script src="js/database_innerdb.js"></script>
    <script>
        const output = document.getElementById('output');
        
        function log(message) {
            console.log(message);
            output.innerHTML += '<p>' + message + '</p>';
        }
        
        async function testLoginFunction() {
            try {
                const login = document.getElementById('testLogin').value;
                const password = document.getElementById('testPassword').value;
                
                log(`Probando login: "${login}" con password: "${password}"`);
                
                log('Inicializando base de datos...');
                await dbManager.init();
                log('✅ Base de datos inicializada');
                
                log('Inicializando datos de prueba...');
                await dbManager.initializetestdata();
                log('✅ Datos de prueba inicializados');
                
                log(`Buscando usuario con login: "${login}"`);
                const user = await dbManager.getUserByLogin(login);
                
                if (user) {
                    log(`✅ Usuario encontrado: ${JSON.stringify(user, null, 2)}`);
                    
                    if (user.clave === password) {
                        log('✅ Password correcto');
                        
                        log(`Verificando empresa ${user.empresaId}...`);
                        const company = await dbManager.getCompany(user.empresaId);
                        
                        if (company) {
                            log(`✅ Empresa encontrada: ${JSON.stringify(company, null, 2)}`);
                            
                            if (company.activo) {
                                log('🎉 LOGIN EXITOSO - Todo correcto');
                            } else {
                                log('❌ Empresa no está activa');
                            }
                        } else {
                            log('❌ Empresa no encontrada');
                        }
                    } else {
                        log(`❌ Password incorrecto. Esperado: "${password}", Encontrado: "${user.clave}"`);
                    }
                } else {
                    log('❌ Usuario no encontrado');
                    
                    // Listar todos los usuarios para debug
                    log('Listando todos los usuarios para debug:');
                    const allUsers = await dbManager.getAllUsers();
                    allUsers.forEach((u, index) => {
                        log(`Usuario ${index + 1}: login="${u.login}", clave="${u.clave}"`);
                    });
                }
                
            } catch (error) {
                log('❌ Error: ' + error.message);
                console.error('Error completo:', error);
            }
        }
        
        // Auto-ejecutar al cargar
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(testLoginFunction, 1000);
        });
    </script>
</body>
</html>
