const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const chokidar = require('chokidar');

// Leer configuración
function loadConfig() {
    const configPath = path.join(__dirname, 'config.conf');
    const config = {};
    
    if (fs.existsSync(configPath)) {
        const configContent = fs.readFileSync(configPath, 'utf8');
        configContent.split('\n').forEach(line => {
            line = line.trim();
            if (line && !line.startsWith('#') && line.includes('=')) {
                const [key, value] = line.split('=', 2);
                config[key.trim()] = value.trim();
            }
        });
    }
    
    return {
        buildDir: config.BUILD_DIR || 'build',
        combinedJsFile: config.COMBINED_JS_FILE || 'aplicacion.js',
        combinedCssFile: config.COMBINED_CSS_FILE || 'aplicacion.css',
        lcNumeric: config.LC_NUMERIC || 'es_ES'
    };
}

// Configurar entorno regional
function setupLocale(config) {
    if (config.lcNumeric) {
        process.env.LC_NUMERIC = config.lcNumeric;
        console.log(`Configuración regional establecida: LC_NUMERIC=${config.lcNumeric}`);
    }
}

// Archivos JavaScript en orden de dependencia
const jsFiles = [
    'js/utiles/html.js',
    'js/auth.js',
    'js/database_innerdb.js',
    'js/entity_manager.js',
    'js/user_profile.js',
    'js/temas/themes.js',
    'js/temas/menu_border.js',
    'js/tableros/widget_fields.js',
    'js/tableros/form_fields_accordion.js',
    'js/tableros/widget_fields_accordion.js',
    'js/tableros/color-picker-handlers.js',
    'js/tableros/dashboard_selection_manager.js',
    'js/tableros/widgets.js',
    'js/tableros/dashboard.js',
    'js/tableros/dashboard_loader.js',
    'js/entidades/companies.js',
    'js/entidades/usuarios.js',
    'js/entidades/tableros.js',
    'js/main_menu.js',
    'js/hash_navigation.js',
    'js/navigation.js',
    'js/dialogs.js',
    'js/mobile.js',
    'js/frontend.js',
    'js/app.js'
];

// Archivos CSS en orden de dependencia
const cssFiles = [
    'css/styles.css',
    'css/styles2.css',
    'css/responsive.css',
    'css/accordion.css',
    'css/series_buttons.css',
    'css/widget_rules.css',
    'css/widget_rules_new.css',
    'css/themes/tron.css',
    'css/themes/menta.css',
    'css/themes/main.css'
];

function ensureBuildDir(buildDir) {
    if (!fs.existsSync(buildDir)) {
        fs.mkdirSync(buildDir, { recursive: true });
        console.log(`Directorio de build creado: ${buildDir}`);
    }
}

function buildJS(config) {
    console.log('Construyendo JavaScript...');
    
    const buildDir = config.buildDir;
    const outputFile = path.join(buildDir, config.combinedJsFile);
    
    ensureBuildDir(buildDir);
    
    let combinedContent = '';
    let processedFiles = 0;
    
    // Agregar header con información de build
    combinedContent += `/* IPRA_I - Aplicación combinada y minificada */\n`;
    combinedContent += `/* Generado: ${new Date().toISOString()} */\n`;
    combinedContent += `/* Configuración regional: ${config.lcNumeric} */\n\n`;
    
    jsFiles.forEach(file => {
        if (fs.existsSync(file)) {
            console.log(`  Procesando: ${file}`);
            const content = fs.readFileSync(file, 'utf8');
            combinedContent += `\n/* === ${file} === */\n`;
            combinedContent += content;
            combinedContent += '\n';
            processedFiles++;
        } else {
            console.warn(`  Archivo no encontrado: ${file}`);
        }
    });
    
    console.log(`  ${processedFiles} archivos JavaScript procesados`);
    
    // Minificar con UglifyJS
    try {
        console.log('  Minificando JavaScript...');
        const tempFile = path.join(buildDir, 'temp_combined.js');
        fs.writeFileSync(tempFile, combinedContent);
        
        execSync(`npx uglifyjs "${tempFile}" -o "${outputFile}" --compress --mangle`, {
            stdio: 'inherit'
        });
        
        // Limpiar archivo temporal
        fs.unlinkSync(tempFile);
        
        const stats = fs.statSync(outputFile);
        console.log(`  JavaScript minificado guardado: ${outputFile} (${Math.round(stats.size / 1024)}KB)`);
        
    } catch (error) {
        console.error('Error al minificar JavaScript:', error.message);
        // Guardar sin minificar como fallback
        fs.writeFileSync(outputFile, combinedContent);
        console.log(`  JavaScript guardado sin minificar: ${outputFile}`);
    }
}

function buildCSS(config) {
    console.log('Construyendo CSS...');
    
    const buildDir = config.buildDir;
    const outputFile = path.join(buildDir, config.combinedCssFile);
    
    ensureBuildDir(buildDir);
    
    let combinedContent = '';
    let processedFiles = 0;
    
    // Agregar header con información de build
    combinedContent += `/* IPRA_I - Estilos combinados y minificados */\n`;
    combinedContent += `/* Generado: ${new Date().toISOString()} */\n\n`;
    
    cssFiles.forEach(file => {
        if (fs.existsSync(file)) {
            console.log(`  Procesando: ${file}`);
            const content = fs.readFileSync(file, 'utf8');
            combinedContent += `\n/* === ${file} === */\n`;
            combinedContent += content;
            combinedContent += '\n';
            processedFiles++;
        } else {
            console.warn(`  Archivo no encontrado: ${file}`);
        }
    });
    
    console.log(`  ${processedFiles} archivos CSS procesados`);
    
    // Minificar con clean-css
    try {
        console.log('  Minificando CSS...');
        const tempFile = path.join(buildDir, 'temp_combined.css');
        fs.writeFileSync(tempFile, combinedContent);
        
        execSync(`npx cleancss "${tempFile}" -o "${outputFile}"`, {
            stdio: 'inherit'
        });
        
        // Limpiar archivo temporal
        fs.unlinkSync(tempFile);
        
        const stats = fs.statSync(outputFile);
        console.log(`  CSS minificado guardado: ${outputFile} (${Math.round(stats.size / 1024)}KB)`);
        
    } catch (error) {
        console.error('Error al minificar CSS:', error.message);
        // Guardar sin minificar como fallback
        fs.writeFileSync(outputFile, combinedContent);
        console.log(`  CSS guardado sin minificar: ${outputFile}`);
    }
}

function buildHTML(config) {
    console.log('Construyendo HTML...');
    
    const buildDir = config.buildDir;
    const sourceFile = 'index.html';
    const outputFile = path.join(buildDir, 'index.html');
    
    if (!fs.existsSync(sourceFile)) {
        console.warn(`  Archivo HTML no encontrado: ${sourceFile}`);
        return;
    }
    
    ensureBuildDir(buildDir);
    
    let htmlContent = fs.readFileSync(sourceFile, 'utf8');
    
    // Reemplazar referencias a archivos individuales con archivos combinados
    htmlContent = htmlContent.replace(
        /<!-- JS FILES START -->[\s\S]*?<!-- JS FILES END -->/,
        `<!-- JS FILES START -->\n    <script src="${config.combinedJsFile}"></script>\n    <!-- JS FILES END -->`
    );
    
    htmlContent = htmlContent.replace(
        /<!-- CSS FILES START -->[\s\S]*?<!-- CSS FILES END -->/,
        `<!-- CSS FILES START -->\n    <link rel="stylesheet" href="${config.combinedCssFile}">\n    <!-- CSS FILES END -->`
    );
    
    // Minificar HTML (básico)
    htmlContent = htmlContent
        .replace(/\s+/g, ' ')
        .replace(/>\s+</g, '><')
        .trim();
    
    fs.writeFileSync(outputFile, htmlContent);
    
    const stats = fs.statSync(outputFile);
    console.log(`  HTML procesado guardado: ${outputFile} (${Math.round(stats.size / 1024)}KB)`);
}

function clean(config) {
    console.log('Limpiando directorio de build...');
    
    const buildDir = config.buildDir;
    
    if (fs.existsSync(buildDir)) {
        fs.rmSync(buildDir, { recursive: true, force: true });
        console.log(`  Directorio eliminado: ${buildDir}`);
    }
    
    console.log('Limpieza completada');
}

function watch(config) {
    console.log('Iniciando modo watch...');
    console.log('Presiona Ctrl+C para detener');
    
    // Build inicial
    buildAll(config);
    
    // Configurar watchers
    const jsWatcher = chokidar.watch(jsFiles.filter(f => fs.existsSync(f)));
    const cssWatcher = chokidar.watch(cssFiles.filter(f => fs.existsSync(f)));
    const htmlWatcher = chokidar.watch('index.html');
    
    jsWatcher.on('change', (path) => {
        console.log(`\nArchivo JS modificado: ${path}`);
        buildJS(config);
    });
    
    cssWatcher.on('change', (path) => {
        console.log(`\nArchivo CSS modificado: ${path}`);
        buildCSS(config);
    });
    
    htmlWatcher.on('change', (path) => {
        console.log(`\nArchivo HTML modificado: ${path}`);
        buildHTML(config);
    });
    
    console.log('Watching archivos para cambios...');
}

function buildAll(config) {
    console.log('=== INICIANDO BUILD COMPLETO ===');
    console.log(`Configuración: ${JSON.stringify(config, null, 2)}`);
    
    const startTime = Date.now();
    
    buildJS(config);
    buildCSS(config);
    buildHTML(config);
    
    const endTime = Date.now();
    console.log(`\n=== BUILD COMPLETADO EN ${endTime - startTime}ms ===`);
}

// Procesar argumentos de línea de comandos
function main() {
    const config = loadConfig();
    setupLocale(config);
    
    const args = process.argv.slice(2);
    
    if (args.includes('--clean')) {
        clean(config);
    } else if (args.includes('--js-only')) {
        buildJS(config);
    } else if (args.includes('--css-only')) {
        buildCSS(config);
    } else if (args.includes('--watch')) {
        watch(config);
    } else {
        buildAll(config);
    }
}

// Ejecutar si es el archivo principal
if (require.main === module) {
    main();
}

module.exports = {
    buildJS,
    buildCSS,
    buildHTML,
    buildAll,
    clean,
    watch,
    loadConfig
};
