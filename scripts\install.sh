#!/bin/bash

# Script de instalación para IPRA_I (Linux)
# Edita scripts/config.conf antes de ejecutar

set -e

# Obtener directorio del script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "=== INSTALACIÓN IPRA_I ==="

# Cargar configuración
if [ ! -f "$SCRIPT_DIR/config.conf" ]; then
    echo "ERROR: Archivo scripts/config.conf no encontrado."
    echo "Edita scripts/config.conf con tu configuración antes de ejecutar install.sh"
    exit 1
fi

source "$SCRIPT_DIR/config.conf"

echo "Puerto configurado: $SERVER_PORT"
echo "Configuración regional: $LC_NUMERIC"
echo

# Verificar si se ejecuta como root
if [ "$EUID" -ne 0 ]; then
    echo "AVISO: No se ejecuta como root. Algunas configuraciones pueden fallar."
    echo "Para configurar firewall y servicios: sudo scripts/install.sh"
fi

# Verificar Node.js
echo "Verificando Node.js..."
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js no está instalado"
    echo "Instala Node.js desde: https://nodejs.org/"
    exit 1
else
    echo "Node.js instalado: $(node --version)"
fi

cd "$PROJECT_ROOT"

# Crear directorios necesarios
echo "Creando directorios..."
mkdir -p "$BUILD_DIR"
mkdir -p "$DOCS_DIR"
mkdir -p "$LOG_DIR"
mkdir -p "backend/logs"

# Instalar dependencias del frontend
echo "Instalando dependencias del frontend..."
npm install
if [ $? -ne 0 ]; then
    echo "ERROR: Error al instalar dependencias del frontend"
    exit 1
fi

# Instalar dependencias del backend
echo "Instalando dependencias del backend..."
cd backend
npm install
if [ $? -ne 0 ]; then
    echo "ERROR: Error al instalar dependencias del backend"
    exit 1
fi
cd ..

# Configurar firewall (si es root)
if [ "$EUID" -eq 0 ]; then
    echo "Configurando firewall..."
    if command -v ufw &> /dev/null; then
        ufw allow $SERVER_PORT/tcp >/dev/null 2>&1
    elif command -v firewall-cmd &> /dev/null; then
        firewall-cmd --permanent --add-port=$SERVER_PORT/tcp >/dev/null 2>&1
        firewall-cmd --reload >/dev/null 2>&1
    fi
fi

# Hacer scripts ejecutables
chmod +x scripts/*.sh

# Build inicial
echo "Realizando build inicial..."
npm run build >/dev/null 2>&1

echo
echo "=== INSTALACIÓN COMPLETADA ==="
echo "Puerto: $SERVER_PORT"
echo "URL: http://localhost:$SERVER_PORT"
