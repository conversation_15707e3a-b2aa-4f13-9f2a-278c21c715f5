#!/bin/bash

# Script de instalación REAL para IPRA_I (Linux)
# Descarga e instala automáticamente Node.js y todo lo necesario

set -e

# Obtener directorio del script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "=== INSTALACIÓN AUTOMÁTICA IPRA_I ==="

# Cargar configuración
if [ ! -f "$SCRIPT_DIR/config.conf" ]; then
    echo "ERROR: Archivo scripts/config.conf no encontrado."
    echo "Edita scripts/config.conf con tu configuración antes de ejecutar install.sh"
    exit 1
fi

source "$SCRIPT_DIR/config.conf"

echo "Puerto configurado: $SERVER_PORT"
echo

# Crear directorio de instalación
INSTALL_DIR="$SCRIPT_DIR/install/linux"
mkdir -p "$INSTALL_DIR"

# Verificar si Node.js ya está instalado
if command -v node &> /dev/null; then
    echo "Node.js ya instalado: $(node --version)"
else
    echo "Instalando Node.js..."

    # Detectar distribución
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
    else
        echo "ERROR: No se pudo detectar el sistema operativo"
        exit 1
    fi

    # Instalar Node.js según la distribución
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]] || [[ "$OS" == *"Linux Mint"* ]]; then
        # Descargar script de instalación si no existe
        NODE_SCRIPT="$INSTALL_DIR/setup_lts.x"
        if [ ! -f "$NODE_SCRIPT" ]; then
            echo "Descargando script de instalación de Node.js..."
            curl -fsSL https://deb.nodesource.com/setup_lts.x -o "$NODE_SCRIPT"
        fi

        # Ejecutar instalación
        bash "$NODE_SCRIPT"
        apt-get install -y nodejs

    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]] || [[ "$OS" == *"Fedora"* ]]; then
        # Descargar script de instalación si no existe
        NODE_SCRIPT="$INSTALL_DIR/setup_lts.x"
        if [ ! -f "$NODE_SCRIPT" ]; then
            echo "Descargando script de instalación de Node.js..."
            curl -fsSL https://rpm.nodesource.com/setup_lts.x -o "$NODE_SCRIPT"
        fi

        # Ejecutar instalación
        bash "$NODE_SCRIPT"
        dnf install -y nodejs npm

    else
        echo "ERROR: Sistema operativo no soportado: $OS"
        exit 1
    fi

    echo "Node.js instalado: $(node --version)"
fi

cd "$PROJECT_ROOT"

# Crear directorios necesarios
echo "Creando directorios..."
mkdir -p "$BUILD_DIR"
mkdir -p "$DOCS_DIR"
mkdir -p "$LOG_DIR"
mkdir -p "backend/logs"

# Instalar dependencias del frontend (versiones fijas)
echo "Instalando dependencias del frontend..."
npm ci
if [ $? -ne 0 ]; then
    echo "ERROR: Error al instalar dependencias del frontend"
    echo "Verifica que existe package-lock.json"
    exit 1
fi

# Instalar dependencias del backend (versiones fijas)
echo "Instalando dependencias del backend..."
cd backend
npm ci
if [ $? -ne 0 ]; then
    echo "ERROR: Error al instalar dependencias del backend"
    echo "Verifica que existe backend/package-lock.json"
    exit 1
fi
cd ..

# Configurar firewall (si es root)
if [ "$EUID" -eq 0 ]; then
    echo "Configurando firewall..."
    if command -v ufw &> /dev/null; then
        ufw allow $SERVER_PORT/tcp >/dev/null 2>&1
        echo "Firewall configurado con UFW para puerto $SERVER_PORT"
    elif command -v firewall-cmd &> /dev/null; then
        firewall-cmd --permanent --add-port=$SERVER_PORT/tcp >/dev/null 2>&1
        firewall-cmd --reload >/dev/null 2>&1
        echo "Firewall configurado con firewalld para puerto $SERVER_PORT"
    elif command -v iptables &> /dev/null; then
        iptables -A INPUT -p tcp --dport $SERVER_PORT -j ACCEPT >/dev/null 2>&1
        echo "Firewall configurado con iptables para puerto $SERVER_PORT"
    else
        echo "AVISO: No se detectó firewall, configura manualmente el puerto $SERVER_PORT"
    fi
else
    echo "AVISO: Ejecuta con sudo para configurar firewall automáticamente"
fi

# Hacer scripts ejecutables
chmod +x scripts/*.sh

# Build inicial
echo "Realizando build inicial..."
npm run build >/dev/null 2>&1

echo
echo "=== INSTALACIÓN COMPLETADA ==="
echo "Puerto: $SERVER_PORT"
echo "URL: http://localhost:$SERVER_PORT"
echo
echo "Para iniciar: scripts/start.sh"
