#!/bin/bash
# Script de instalación desatendido para IPRA_I (Linux)

set -e

echo "=== INSTALACIÓN IPRA_I ==="

# Cargar configuración
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/config.conf"

echo "Puerto: $SERVER_PORT"

# Directorio de instalación
INSTALL_DIR="$SCRIPT_DIR/install/linux"
mkdir -p "$INSTALL_DIR"

# Verificar Node.js
if ! command -v node &> /dev/null; then
    echo "Instalando Node.js..."
    
    # Detectar distribución
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
    fi
    
    # Instalar según distribución
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]] || [[ "$OS" == *"Linux Mint"* ]]; then
        # Descargar script si no existe
        if [ ! -f "$INSTALL_DIR/setup_lts.x" ]; then
            curl -fsSL https://deb.nodesource.com/setup_lts.x -o "$INSTALL_DIR/setup_lts.x"
        fi
        bash "$INSTALL_DIR/setup_lts.x"
        apt-get install -y nodejs
        
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]] || [[ "$OS" == *"Fedora"* ]]; then
        # Descargar script si no existe
        if [ ! -f "$INSTALL_DIR/setup_lts.x" ]; then
            curl -fsSL https://rpm.nodesource.com/setup_lts.x -o "$INSTALL_DIR/setup_lts.x"
        fi
        bash "$INSTALL_DIR/setup_lts.x"
        dnf install -y nodejs npm
    fi
fi

# Cambiar al directorio del proyecto
cd "$(dirname "$SCRIPT_DIR")"

# Instalar dependencias
echo "Instalando dependencias..."
npm ci 2>/dev/null || npm install
cd backend
npm ci 2>/dev/null || npm install
cd ..

# Firewall
if [ "$EUID" -eq 0 ]; then
    if command -v ufw &> /dev/null; then
        ufw allow $SERVER_PORT/tcp >/dev/null 2>&1
    elif command -v firewall-cmd &> /dev/null; then
        firewall-cmd --permanent --add-port=$SERVER_PORT/tcp >/dev/null 2>&1
        firewall-cmd --reload >/dev/null 2>&1
    fi
fi

# Permisos
chmod +x scripts/*.sh

# Build
npm run build >/dev/null 2>&1

echo
echo "INSTALACIÓN COMPLETADA"
echo "URL: http://localhost:$SERVER_PORT"
