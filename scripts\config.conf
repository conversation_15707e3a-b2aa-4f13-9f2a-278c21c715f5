# Configuración centralizada del proyecto IPRA_I
# Todos los scripts deben hacer: source scripts/config.conf (Linux) o cargar desde scripts\config.conf (Windows)

# === CONFIGURACIÓN DEL SERVIDOR ===
# Puerto donde correrá el servidor Node.js
SERVER_PORT=3000

# Host del servidor (normalmente localhost para desarrollo)
SERVER_HOST=localhost

# === CONFIGURACIÓN REGIONAL ===
# Configuración de números (es_ES usa coma decimal, en_US usa punto)
# Valores: es_ES, en_US, etc.
LC_NUMERIC=es_ES

# === CONFIGURACIÓN DE BUILD ===
# Directorio donde se generarán los archivos minificados
BUILD_DIR=build

# Nombre del archivo JavaScript combinado
COMBINED_JS_FILE=aplicacion.js

# Nombre del archivo CSS combinado  
COMBINED_CSS_FILE=aplicacion.css

# === CONFIGURACIÓN DE DOCUMENTACIÓN ===
# Directorio para documentación generada
DOCS_DIR=docs_generated

# === CONFIGURACIÓN DE WEBSOCKETS ===
# Tiempo de reconexión inicial en milisegundos
WEBSOCKET_RECONNECT_INITIAL=1000

# Tiempo máximo de reconexión en milisegundos
WEBSOCKET_RECONNECT_MAX=30000

# === CONFIGURACIÓN DE LOGS ===
# Nivel de log (debug, info, warn, error)
LOG_LEVEL=info

# Directorio de logs
LOG_DIR=logs
