/**
 * Módulo para gestionar la presentación de campos de formulario en formato acordeón
 * Utiliza las funciones especializadas de uhtml para crear elementos HTML
 */
class FormFieldsAccordion {
    /**
     * Crea un acordeón para los campos de configuración de formularios
     * @param {HTMLElement} container - Contenedor donde se creará el acordeón
     * @param {Object} zones - Objeto con las zonas disponibles
     * @param {Array} fields - Array con los campos a mostrar
     * @param {boolean} isEdit - Indica si es para edición (true) o creación (false)
     * @returns {HTMLElement} - Elemento del acordeón creado
     */
    static createAccordion(container, zones, fields, isEdit = true) {
        // Crear el contenedor principal del acordeón
        const accordion = uhtml.crearElemento('div', {
            class: 'form-fields-accordion'
        });

        // Agrupar campos por zona
        for (let zone in zones) zones[zone].fields = [];

        // Agrupar los campos por zona
        fields.forEach(field => {
            //Sin zona existente no se muestra.
            if (!field.zone) return;
            if (!zones[field.zone]) return;
            //Añadir campo a la zona
            zones[field.zone].fields.push(field);
        });

        // Crear una sección de acordeón para cada zona que tenga campos
        Object.keys(zones).forEach((zoneKey, index) => {
            const zoneFields = zones[zoneKey].fields;
            const zone = zones[zoneKey];
            if (!zoneFields.length) return;
            const section = this.createAccordionSection(zone, zoneFields, isEdit, index === 0);
            accordion.appendChild(section);
        });

        // Añadir el acordeón al contenedor
        if (container) {
            container.appendChild(accordion);
        }



        return accordion;
    }

    /**
     * Crea una sección del acordeón
     * @param {Object} zone - Información de la zona
     * @param {Array} zoneFields - Campos de la zona
     * @param {boolean} isEdit - Indica si es para edición (true) o creación (false)
     * @returns {HTMLElement} - Elemento de la sección creada
     */
    static createAccordionSection(zone, zoneFields, isEdit, isFirst) {
        // Crear la sección del acordeón
        const section = uhtml.crearElemento('div', {
            class: 'accordion-section'
        });

        // Crear el checkbox oculto
        // Por defecto, todas las secciones están cerradas excepto la primera
        // Generar un ID único para el checkbox para evitar conflictos
        const uniqueId = `accordion-${zone.name.toLowerCase()}-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
        const toggle = uhtml.crearCheckbox({
            class: 'accordion-toggle',
            id: uniqueId,
            attrs: {
                checked: !isFirst // Solo la primera sección estará abierta (checked = false)
            }
        }, section);


        // Crear el encabezado de la sección como label
        const header = uhtml.crearElemento('label', {
            class: 'accordion-header',
            attrs: {
                'for': uniqueId, // Usar el ID único generado para el checkbox
                'data-zone-id': zone.name,
                'data-accion': 'toggleAccordion',
            }
        }, section);

        // Añadir icono si existe
        if (zone.icon) {
            uhtml.crearIcono(zone.icon, {
                class: 'zone-icon'
            }, header);
        }

        // Añadir título
        uhtml.crearElemento('span', {
            class: 'title',
            text: zone.title
        }, header);

        // Añadir icono de despliegue
        uhtml.crearIcono('expand_more', {
            class: 'toggle-icon'
        }, header);

        // Crear el contenido de la sección
        const content = uhtml.crearElemento('div', {
            class: 'accordion-content',
            attrs: {
                'data-zone-id': zone.name
            }
        }, section);

        // Verificar si es la zona de reglas para aplicar estilos especiales
        if (zone.name === 'reglas') {
            // Agrupar los campos por regla
            const regla1Fields = zoneFields.filter(field => field.paramName && field.paramName.startsWith('regla1'));
            const regla2Fields = zoneFields.filter(field => field.paramName && field.paramName.startsWith('regla2'));
            const otrosFields = zoneFields.filter(field =>
                !field.paramName ||
                (!field.paramName.startsWith('regla1') && !field.paramName.startsWith('regla2'))
            );

            // Crear los campos que no pertenecen a ninguna regla específica
            otrosFields.forEach(field => {
                this.createFieldElement(field, content, isEdit);
            });

            // Si hay campos para la regla 1, crear un contenedor especial
            if (regla1Fields.length > 0) {
                const regla1Container = document.createElement('div');
                regla1Container.className = 'reglas-container';
                content.appendChild(regla1Container);

                // Título de la regla 1
                const regla1Title = document.createElement('div');
                regla1Title.className = 'regla-titulo';
                regla1Title.textContent = 'Regla 1 - Valor crítico';
                regla1Container.appendChild(regla1Title);

                // Fila 1: Valor | Color Fondo
                const regla1Fila1 = document.createElement('div');
                regla1Container.appendChild(regla1Fila1);

                // Crear los campos de valor y color de fondo
                regla1Fields.forEach(field => {
                    if (field.paramName === 'regla1Valor' || field.paramName === 'regla1BgColor') {
                        this.createFieldElement(field, regla1Fila1, isEdit);
                    }
                });

                // Fila 2: Color Texto | Color Borde
                const regla1Fila2 = document.createElement('div');
                regla1Container.appendChild(regla1Fila2);

                // Crear los campos de color de texto y borde
                regla1Fields.forEach(field => {
                    if (field.paramName === 'regla1TextColor' || field.paramName === 'regla1BorderColor') {
                        this.createFieldElement(field, regla1Fila2, isEdit);
                    }
                });
            }

            // No añadimos separador entre reglas, ya que los recuadros proporcionan suficiente separación visual

            // Si hay campos para la regla 2, crear un contenedor especial
            if (regla2Fields.length > 0) {
                const regla2Container = document.createElement('div');
                regla2Container.className = 'reglas-container';
                content.appendChild(regla2Container);

                // Título de la regla 2
                const regla2Title = document.createElement('div');
                regla2Title.className = 'regla-titulo';
                regla2Title.textContent = 'Regla 2 - Valor de advertencia';
                regla2Container.appendChild(regla2Title);

                // Fila 1: Valor | Color Fondo
                const regla2Fila1 = document.createElement('div');
                regla2Container.appendChild(regla2Fila1);

                // Crear los campos de valor y color de fondo
                regla2Fields.forEach(field => {
                    if (field.paramName === 'regla2Valor' || field.paramName === 'regla2BgColor') {
                        this.createFieldElement(field, regla2Fila1, isEdit);
                    }
                });

                // Fila 2: Color Texto | Color Borde
                const regla2Fila2 = document.createElement('div');
                regla2Container.appendChild(regla2Fila2);

                // Crear los campos de color de texto y borde
                regla2Fields.forEach(field => {
                    if (field.paramName === 'regla2TextColor' || field.paramName === 'regla2BorderColor') {
                        this.createFieldElement(field, regla2Fila2, isEdit);
                    }
                });
            }
        } else {
            // Para otras zonas, crear los campos normalmente
            zoneFields.forEach(field => {
                this.createFieldElement(field, content, isEdit);
            });
        }

        return section;
    }

    static createFieldElement(field, container, isEdit) {
        let fieldGroup;

        // Usar las funciones específicas según el tipo de campo
        switch (field.type) {
            case 'checkbox':
                fieldGroup = uhtml.crearCheckboxGroup({
                    id: field.id,
                    label: field.label,
                    checked: field.defaultValue,
                    name: field.paramName || field.id,
                    'data-param-name': field.paramName
                }, container);
                break;

            case 'select':
                fieldGroup = uhtml.crearGrupoCampo({ // Cambiar aquí
                    id: field.id,
                    label: field.label,
                    type: 'select',
                    name: field.paramName || field.id,
                    'data-param-name': field.paramName,
                    options: field.options,
                    inputClass: field.class // Pasar la clase al input
                }, container);
                break;

            case 'number':
                fieldGroup = uhtml.crearGrupoCampo({ // Cambiar aquí
                    id: field.id,
                    label: field.label,
                    type: 'number',
                    name: field.paramName || field.id,
                    'data-param-name': field.paramName,
                    min: field.min,
                    max: field.max,
                    value: field.defaultValue
                }, container);
                break;

                // Caso para campos de color (añádelo a tu switch case o donde estés creando estos campos)
                // En el archivo donde se procese la creación de campos de formulario
                // (probablemente dashboard.js o similar)

                // En el switch case donde se crean los campos:
                // En form_fields_accordion.js, función createFieldElement
                // En form_fields_accordion.js, función createFieldElement
            case 'color':
                // Crear un contenedor específico para campos de color
                fieldGroup = document.createElement('div');
                fieldGroup.className = 'form-group color-field-container';
                fieldGroup.id = `form-group-${field.id}`;

                // Crear la etiqueta
                const label = document.createElement('label');
                label.htmlFor = field.id;
                label.textContent = field.label;
                fieldGroup.appendChild(label);

                // Crear un contenedor para el color picker y el botón reset
                const colorControls = document.createElement('div');
                colorControls.className = 'color-controls';

                // Crear el input de tipo color
                const colorInput = document.createElement('input');
                colorInput.type = 'color';
                colorInput.id = field.id;
                colorInput.name = field.paramName || field.id;
                colorInput.dataset.paramName = field.paramName;

                // Si hay un valor por defecto, establecerlo
                if (field.defaultValue) {
                    colorInput.value = field.defaultValue;
                }

                colorControls.appendChild(colorInput);

                // Si el campo tiene definida una acción de reset, añadimos el botón
                // En form_fields_accordion.js, al crear los botones de reset:
                if (field.reset) {
                    const resetButton = document.createElement('button');
                    resetButton.textContent = 'Reset';
                    resetButton.className = 'btn btn-sm reset-button';
                    resetButton.dataset.accion = 'resetWidgetColor';
                    resetButton.dataset.colorProperty = field.paramName || field.id; // Guardamos qué propiedad estamos reseteando
                    resetButton.type = 'button';
                    colorControls.appendChild(resetButton);
                }


                fieldGroup.appendChild(colorControls);
                container.appendChild(fieldGroup);
                break;

            case 'textarea':
                fieldGroup = document.createElement('div');
                fieldGroup.className = 'form-group';
                fieldGroup.id = `form-group-${field.id}`;

                // Crear la etiqueta
                const textareaLabel = document.createElement('label');
                textareaLabel.htmlFor = field.id;
                textareaLabel.textContent = field.label;
                fieldGroup.appendChild(textareaLabel);

                // Crear el textarea
                const textarea = document.createElement('textarea');
                textarea.id = field.id;
                textarea.name = field.paramName || field.id;
                textarea.dataset.paramName = field.paramName;

                // Añadir clases adicionales si existen
                if (field.class) {
                    textarea.className = field.class;
                }

                // Si hay un valor por defecto, establecerlo
                if (field.defaultValue) {
                    textarea.value = field.defaultValue;
                }

                fieldGroup.appendChild(textarea);
                container.appendChild(fieldGroup);
                break;

            case 'button':
                fieldGroup = document.createElement('div');
                fieldGroup.className = 'form-group button-field-container';
                fieldGroup.id = `form-group-${field.id}`;

                // Crear la etiqueta si existe
                if (field.label) {
                    const buttonLabel = document.createElement('label');
                    buttonLabel.htmlFor = field.id;
                    buttonLabel.textContent = field.label;
                    fieldGroup.appendChild(buttonLabel);
                }

                // Crear el botón
                const button = document.createElement('button');
                button.id = field.id;
                button.type = 'button';
                button.innerHTML = field.text || 'Aceptar';
                button.dataset.paramName = field.paramName;

                // Añadir clases adicionales si existen
                if (field.class) {
                    button.className = field.class;
                }

                // Añadir atributos personalizados si existen
                if (field.attrs) {
                    for (const [key, value] of Object.entries(field.attrs)) {
                        button.setAttribute(key, value);
                    }
                }

                fieldGroup.appendChild(button);
                container.appendChild(fieldGroup);
                break;

            case 'html':
                fieldGroup = document.createElement('div');
                fieldGroup.className = 'form-group html-field-container';
                fieldGroup.id = `form-group-${field.id}`;

                // Crear la etiqueta si existe
                if (field.label) {
                    const htmlLabel = document.createElement('label');
                    htmlLabel.htmlFor = field.id;
                    htmlLabel.textContent = field.label;
                    fieldGroup.appendChild(htmlLabel);
                }

                // Crear el contenedor HTML
                const htmlContainer = document.createElement('div');
                htmlContainer.id = field.id;
                htmlContainer.innerHTML = field.html || '';

                // Añadir clases adicionales si existen
                if (field.class) {
                    htmlContainer.className = field.class;
                }

                fieldGroup.appendChild(htmlContainer);
                container.appendChild(fieldGroup);
                break;

            case 'custom':
                fieldGroup = document.createElement('div');
                fieldGroup.className = 'form-group custom-field-container';
                fieldGroup.id = `form-group-${field.id}`;

                // Crear la etiqueta si existe
                if (field.label) {
                    const customLabel = document.createElement('label');
                    customLabel.htmlFor = field.id;
                    customLabel.textContent = field.label;
                    fieldGroup.appendChild(customLabel);
                }

                // Crear el contenedor personalizado
                const customContainer = document.createElement('div');
                customContainer.id = field.id;
                customContainer.innerHTML = field.html || '';

                // Añadir clases adicionales si existen
                if (field.class) {
                    customContainer.className = field.class;
                }

                fieldGroup.appendChild(customContainer);
                container.appendChild(fieldGroup);
                break;

            case 'input':
            default:
                fieldGroup = uhtml.crearGrupoCampo({ // Cambiar aquí
                    id: field.id,
                    label: field.label,
                    type: 'text',
                    name: field.paramName || field.id,
                    'data-param-name': field.paramName,
                    value: field.defaultValue
                }, container);
                break;
        }

        return fieldGroup;
    }
}


/**
 * Controla el comportamiento de las secciones de acordeón
 * @param {Event} event - El evento que desencadenó la acción
 */
app.acciones.toggleAccordion = function (event) {
    // Verificar que tenemos un evento válido
    if (!event || !event.target) {
        console.error('Error: Evento no válido en toggleAccordion');
        return;
    }

    // Obtener el elemento que disparó el evento (el label con data-accion)
    // Si el clic fue en un elemento hijo del header (como un icono o el texto),
    // necesitamos encontrar el header
    let header = event.target;

    // Si el elemento que recibió el clic no es el header, buscamos el header más cercano
    if (!header.classList.contains('accordion-header')) {
        header = event.target.closest('.accordion-header');
    }

    if (!header) {
        console.error('Error: No se pudo encontrar el encabezado del acordeón');
        return;
    }

    // Encontrar la sección del acordeón (padre del header)
    const section = header.parentElement;
    if (!section) {
        console.error('Error: No se pudo encontrar la sección del acordeón');
        return;
    }

    // Encontrar el checkbox de toggle
    // Usar el atributo 'for' del header para encontrar el checkbox correspondiente
    const toggleId = header.getAttribute('for');
    const toggle = toggleId ? section.querySelector(`#${toggleId}`) : section.querySelector('.accordion-toggle');
    if (!toggle) {
        console.error('Error: No se encontró el elemento .accordion-toggle en la sección');
        return;
    }

    // Encontrar el contenedor del acordeón
    const container = section.parentElement;
    if (!container) {
        console.error('Error: La sección del acordeón no tiene un elemento padre');
        return;
    }

    // Si la sección ya está abierta (toggle no está checked), la cerramos
    if (!toggle.checked) {
        // Cerrar la sección actual sin restricciones
        toggle.checked = true;

        // Mover la sección cerrada al principio del acordeón
        if (section !== container.firstChild) {
            // Pequeña animación para suavizar el movimiento
            section.style.transition = 'opacity 0.2s ease-in-out';
            section.style.opacity = '0.7';

            // Usar setTimeout para dar tiempo a la animación
            setTimeout(() => {
                // Mover al principio
                container.insertBefore(section, container.firstChild);

                // Restaurar la opacidad
                setTimeout(() => {
                    section.style.opacity = '1';
                }, 50);
            }, 200);
        }

        return;
    }

    // Primero cerramos todas las secciones
    const allToggles = container.querySelectorAll('.accordion-toggle');
    allToggles.forEach(otherToggle => {
        otherToggle.checked = true; // Marcamos todos para cerrarlos
    });

    // Luego abrimos solo la sección actual
    toggle.checked = false;

    // Mover la sección abierta al principio del acordeón
    if (section !== container.firstChild) {
        // Pequeña animación para suavizar el movimiento
        section.style.transition = 'opacity 0.2s ease-in-out';
        section.style.opacity = '0.7';

        // Usar setTimeout para dar tiempo a la animación
        setTimeout(() => {
            // Mover al principio
            container.insertBefore(section, container.firstChild);

            // Restaurar la opacidad y hacer scroll
            setTimeout(() => {
                section.style.opacity = '1';

                // Aseguramos que el contenido sea visible haciendo scroll si es necesario
                section.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 50);
        }, 200);
    } else {
        // Si ya está al principio, solo hacemos scroll
        section.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
};