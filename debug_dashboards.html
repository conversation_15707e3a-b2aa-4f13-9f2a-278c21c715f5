<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Tableros</title>
</head>
<body>
    <h1>Debug Tableros</h1>
    <button onclick="debugDashboards()">Debug Tableros</button>
    <div id="output"></div>
    
    <script src="js/database_innerdb.js"></script>
    <script>
        const output = document.getElementById('output');
        
        function log(message) {
            console.log(message);
            output.innerHTML += '<p>' + message + '</p>';
        }
        
        async function debugDashboards() {
            try {
                log('=== DEBUG TABLEROS ===');
                
                await dbManager.init();
                await dbManager.initializetestdata();
                
                log('Verificando tableros por empresa...');
                
                for (let i = 1; i <= 10; i++) {
                    const company = await dbManager.getCompany(i);
                    if (company) {
                        const dashboards = await dbManager.getDashboards(i);
                        log(`Empresa ${i} (${company.nombre}): ${dashboards.length} tableros`);
                        
                        dashboards.forEach(dashboard => {
                            log(`  - Tablero ID: ${dashboard.id}, Nombre: "${dashboard.name}"`);
                        });
                    }
                }
                
                log('Probando acceso directo a tableros...');
                for (let i = 1; i <= 5; i++) {
                    const result = await dbManager.findDashboardById(i);
                    if (result) {
                        log(`✅ Tablero ${i}: "${result.dashboard.name}" de empresa ${result.company.nombre}`);
                    } else {
                        log(`❌ Tablero ${i}: NO encontrado`);
                    }
                }
                
                log('=== FIN DEBUG ===');
                
            } catch (error) {
                log('❌ Error: ' + error.message);
                console.error('Error completo:', error);
            }
        }
        
        // Auto-ejecutar al cargar
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(debugDashboards, 1000);
        });
    </script>
</body>
</html>
