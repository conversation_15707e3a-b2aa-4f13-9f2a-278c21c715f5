const WebSocket = require('ws');
const express = require('express');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');

// Leer configuración
const configPath = path.join(__dirname, '..', 'config.conf');
let config = {};

if (fs.existsSync(configPath)) {
    const configContent = fs.readFileSync(configPath, 'utf8');
    configContent.split('\n').forEach(line => {
        line = line.trim();
        if (line && !line.startsWith('#') && line.includes('=')) {
            const [key, value] = line.split('=', 2);
            config[key.trim()] = value.trim();
        }
    });
}

const PORT = parseInt(config.SERVER_PORT) || 3000;
const HOST = config.SERVER_HOST || 'localhost';
const LOG_LEVEL = config.LOG_LEVEL || 'info';

// Configurar logs
const logLevels = { debug: 0, info: 1, warn: 2, error: 3 };
const currentLogLevel = logLevels[LOG_LEVEL] || 1;

function log(level, message, data = null) {
    if (logLevels[level] >= currentLogLevel) {
        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
        console.log(logMessage);
        if (data) {
            console.log(JSON.stringify(data, null, 2));
        }
    }
}

// Crear servidor Express
const app = express();
app.use(cors());
app.use(express.json());

// Crear servidor HTTP
const server = require('http').createServer(app);

// Crear servidor WebSocket
const wss = new WebSocket.Server({ server });

// Almacenar conexiones por dashboard
const dashboardConnections = new Map(); // dashboardId -> Set de conexiones
const userConnections = new Map(); // connectionId -> { ws, userId, userName, dashboardId }

// Endpoint de salud
app.get('/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        connections: userConnections.size,
        dashboards: dashboardConnections.size
    });
});

// Manejar conexiones WebSocket
wss.on('connection', (ws, req) => {
    const connectionId = uuidv4();
    log('info', `Nueva conexión WebSocket: ${connectionId}`);
    
    // Configurar conexión
    ws.connectionId = connectionId;
    ws.isAlive = true;
    
    // Heartbeat para detectar conexiones muertas
    ws.on('pong', () => {
        ws.isAlive = true;
    });
    
    ws.on('message', (data) => {
        try {
            const message = JSON.parse(data);
            handleMessage(ws, message);
        } catch (error) {
            log('error', `Error al procesar mensaje de ${connectionId}:`, error);
            sendError(ws, 'Mensaje inválido');
        }
    });
    
    ws.on('close', () => {
        handleDisconnection(connectionId);
    });
    
    ws.on('error', (error) => {
        log('error', `Error en conexión ${connectionId}:`, error);
        handleDisconnection(connectionId);
    });
});

function handleMessage(ws, message) {
    const { type, payload } = message;
    const connectionId = ws.connectionId;
    
    log('debug', `Mensaje recibido de ${connectionId}:`, { type, payload });
    
    switch (type) {
        case 'join_dashboard':
            handleJoinDashboard(ws, payload);
            break;
            
        case 'leave_dashboard':
            handleLeaveDashboard(ws, payload);
            break;
            
        case 'widget_update':
            handleWidgetUpdate(ws, payload);
            break;
            
        case 'widget_create':
            handleWidgetCreate(ws, payload);
            break;
            
        case 'widget_delete':
            handleWidgetDelete(ws, payload);
            break;
            
        case 'ping':
            sendMessage(ws, { type: 'pong', payload: { timestamp: Date.now() } });
            break;
            
        default:
            log('warn', `Tipo de mensaje desconocido: ${type}`);
            sendError(ws, `Tipo de mensaje desconocido: ${type}`);
    }
}

function handleJoinDashboard(ws, payload) {
    const { dashboardId, userId, userName } = payload;
    const connectionId = ws.connectionId;
    
    if (!dashboardId || !userId || !userName) {
        sendError(ws, 'Faltan datos requeridos para unirse al dashboard');
        return;
    }
    
    // Remover de dashboard anterior si existe
    const existingConnection = userConnections.get(connectionId);
    if (existingConnection && existingConnection.dashboardId) {
        handleLeaveDashboard(ws, { dashboardId: existingConnection.dashboardId });
    }
    
    // Agregar a nuevo dashboard
    if (!dashboardConnections.has(dashboardId)) {
        dashboardConnections.set(dashboardId, new Set());
    }
    
    dashboardConnections.get(dashboardId).add(connectionId);
    userConnections.set(connectionId, { ws, userId, userName, dashboardId });
    
    log('info', `Usuario ${userName} (${userId}) se unió al dashboard ${dashboardId}`);
    
    // Notificar a otros usuarios del dashboard
    broadcastToDashboard(dashboardId, {
        type: 'user_joined',
        payload: { userId, userName, connectionId }
    }, connectionId);
    
    // Enviar lista de usuarios actuales
    const currentUsers = getCurrentDashboardUsers(dashboardId);
    sendMessage(ws, {
        type: 'dashboard_joined',
        payload: { dashboardId, users: currentUsers }
    });
}

function handleLeaveDashboard(ws, payload) {
    const { dashboardId } = payload;
    const connectionId = ws.connectionId;
    const connection = userConnections.get(connectionId);
    
    if (!connection) return;
    
    const actualDashboardId = dashboardId || connection.dashboardId;
    if (!actualDashboardId) return;
    
    // Remover de dashboard
    if (dashboardConnections.has(actualDashboardId)) {
        dashboardConnections.get(actualDashboardId).delete(connectionId);
        
        // Limpiar dashboard vacío
        if (dashboardConnections.get(actualDashboardId).size === 0) {
            dashboardConnections.delete(actualDashboardId);
        }
    }
    
    // Notificar a otros usuarios
    if (connection.userId && connection.userName) {
        broadcastToDashboard(actualDashboardId, {
            type: 'user_left',
            payload: { 
                userId: connection.userId, 
                userName: connection.userName, 
                connectionId 
            }
        }, connectionId);
    }
    
    // Actualizar conexión
    connection.dashboardId = null;
    
    log('info', `Usuario ${connection.userName} dejó el dashboard ${actualDashboardId}`);
}

function handleWidgetUpdate(ws, payload) {
    const connection = userConnections.get(ws.connectionId);
    if (!connection || !connection.dashboardId) {
        sendError(ws, 'No estás conectado a ningún dashboard');
        return;
    }
    
    const { widgetId, changes, timestamp } = payload;
    
    if (!widgetId || !changes) {
        sendError(ws, 'Faltan datos requeridos para actualizar widget');
        return;
    }
    
    log('debug', `Widget ${widgetId} actualizado por ${connection.userName}`);
    
    // Broadcast a otros usuarios del dashboard
    broadcastToDashboard(connection.dashboardId, {
        type: 'widget_updated',
        payload: {
            widgetId,
            changes,
            timestamp: timestamp || Date.now(),
            userId: connection.userId,
            userName: connection.userName
        }
    }, ws.connectionId);
}

function handleWidgetCreate(ws, payload) {
    const connection = userConnections.get(ws.connectionId);
    if (!connection || !connection.dashboardId) {
        sendError(ws, 'No estás conectado a ningún dashboard');
        return;
    }
    
    const { widget, timestamp } = payload;
    
    if (!widget || !widget.id) {
        sendError(ws, 'Faltan datos requeridos para crear widget');
        return;
    }
    
    log('debug', `Widget ${widget.id} creado por ${connection.userName}`);
    
    // Broadcast a otros usuarios del dashboard
    broadcastToDashboard(connection.dashboardId, {
        type: 'widget_created',
        payload: {
            widget,
            timestamp: timestamp || Date.now(),
            userId: connection.userId,
            userName: connection.userName
        }
    }, ws.connectionId);
}

function handleWidgetDelete(ws, payload) {
    const connection = userConnections.get(ws.connectionId);
    if (!connection || !connection.dashboardId) {
        sendError(ws, 'No estás conectado a ningún dashboard');
        return;
    }
    
    const { widgetId, timestamp } = payload;
    
    if (!widgetId) {
        sendError(ws, 'Falta ID del widget para eliminar');
        return;
    }
    
    log('debug', `Widget ${widgetId} eliminado por ${connection.userName}`);
    
    // Broadcast a otros usuarios del dashboard
    broadcastToDashboard(connection.dashboardId, {
        type: 'widget_deleted',
        payload: {
            widgetId,
            timestamp: timestamp || Date.now(),
            userId: connection.userId,
            userName: connection.userName
        }
    }, ws.connectionId);
}

function handleDisconnection(connectionId) {
    const connection = userConnections.get(connectionId);
    if (!connection) return;
    
    log('info', `Desconexión: ${connection.userName} (${connectionId})`);
    
    // Remover de dashboard
    if (connection.dashboardId) {
        handleLeaveDashboard(connection.ws, { dashboardId: connection.dashboardId });
    }
    
    // Limpiar conexión
    userConnections.delete(connectionId);
}

function getCurrentDashboardUsers(dashboardId) {
    const connections = dashboardConnections.get(dashboardId);
    if (!connections) return [];
    
    return Array.from(connections).map(connectionId => {
        const connection = userConnections.get(connectionId);
        return connection ? {
            connectionId,
            userId: connection.userId,
            userName: connection.userName
        } : null;
    }).filter(Boolean);
}

function broadcastToDashboard(dashboardId, message, excludeConnectionId = null) {
    const connections = dashboardConnections.get(dashboardId);
    if (!connections) return;
    
    connections.forEach(connectionId => {
        if (connectionId === excludeConnectionId) return;
        
        const connection = userConnections.get(connectionId);
        if (connection && connection.ws.readyState === WebSocket.OPEN) {
            sendMessage(connection.ws, message);
        }
    });
}

function sendMessage(ws, message) {
    if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(message));
    }
}

function sendError(ws, error) {
    sendMessage(ws, {
        type: 'error',
        payload: { message: error, timestamp: Date.now() }
    });
}

// Heartbeat para limpiar conexiones muertas
const heartbeat = setInterval(() => {
    wss.clients.forEach((ws) => {
        if (!ws.isAlive) {
            log('debug', `Terminando conexión muerta: ${ws.connectionId}`);
            return ws.terminate();
        }
        
        ws.isAlive = false;
        ws.ping();
    });
}, 30000);

// Limpiar al cerrar servidor
wss.on('close', () => {
    clearInterval(heartbeat);
});

// Iniciar servidor
server.listen(PORT, HOST, () => {
    log('info', `Servidor iniciado en ${HOST}:${PORT}`);
    log('info', `WebSocket disponible en ws://${HOST}:${PORT}`);
});

// Manejo de señales para cierre limpio
process.on('SIGTERM', () => {
    log('info', 'Recibida señal SIGTERM, cerrando servidor...');
    server.close(() => {
        log('info', 'Servidor cerrado');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    log('info', 'Recibida señal SIGINT, cerrando servidor...');
    server.close(() => {
        log('info', 'Servidor cerrado');
        process.exit(0);
    });
});
