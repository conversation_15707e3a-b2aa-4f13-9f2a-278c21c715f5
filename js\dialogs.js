/**
 * Gestor de diálogos y popups
 * Proporciona funcionalidad para mostrar diferentes tipos de diálogos y popups
 */

// Pila global para gestionar popups y modales abiertos
var uiStack = [];

/**
 * Añade un elemento a la pila UI
 * @param {HTMLElement} element - El elemento DOM a añadir a la pila
 * @param {string} type - Tipo de elemento ('popup', 'modal', 'container' o 'dialog'). Si es undefined, se detecta automáticamente.
 * @param {Object} instancia - Instancia del objeto que gestiona el elemento
 * @param {Object} params - Parámetros adicionales (como callbacks)
 */
async function pushToUIStack(element, type, instancia, params = {}) {
    // Si el tipo no está definido, intentar detectarlo automáticamente
    if (type === undefined) {
        if (element.classList.contains('popup')) {
            type = 'popup';
        } else if (element.classList.contains('modal')) {
            type = 'modal';
        } else if (element.classList.contains('container')) {
            type = 'container';
        } else if (element.classList.contains('dialog')) {
            type = 'dialog';
        } else {
            // Si no se puede determinar, usar 'unknown'
            type = 'unknown';
        }
    }

    if (instancia)
        app.pant_act = instancia;
    else
        instancia = app.pant_act;

    app.view_act = element

    let cargado = false;
    uiStack.push({
        element,
        type,
        instancia,
        params,
        cargado
    });
    await showElement(element);
    console.log(`Añadido a pila UI: ${type} - ${element.id || 'sin ID'}`);
}



/**
 * Elimina el último elemento de la pila UI y lo quita del DOM si existe.
 * @returns {Promise<Object|null>} Promesa que resuelve al elemento eliminado o null si la pila estaba vacía.
 */
async function popFromUIStack(volver_anterior = false) {
    if (uiStack.length === 0) return null;
    const removed = uiStack.pop();
    console.log(`Eliminado de pila UI: ${removed.type} - ${removed.element.id || 'sin ID'}`);

    if (document.body.contains(removed.element)) {
        console.log(`Ocultando y eliminando nodo: ${removed.element.id || 'sin ID'}`);

        // Asegurar que no capture eventos durante el proceso de eliminación
        //NUNCA NUNCA NUNCA PONER ÉSTA LINEA. Luego no se quita y la pantalla queda sin responder al ratón
       // removed.element.style.pointerEvents = 'none';

        // Esperar a que la animación de ocultar termine
        await hideElement(removed.element);

        if (removed.element._clickOutsideHandler) {
            document.removeEventListener('click', removed.element._clickOutsideHandler);
            delete removed.element._clickOutsideHandler;
        }

        // Eliminar del DOM si es necesario
        if (removed.element.dataset.remove !== 'N') {
            console.log('popFromUIStack removes ' + removed.element.id);
            removed.element.remove();
        } else {
            console.log('popFromUIStack dont removes ' + removed.element.id);
            // Asegurar que los elementos que no se eliminan no capturen eventos
            removed.element.classList.add('hidden');
        }
    }

    // Mostrar el elemento anterior si es necesario
    if (volver_anterior && uiStack.length > 0) {
        const current = uiStack[uiStack.length - 1];
        if (current) {
            if (current.instancia && typeof current.instancia.refresh === 'function') {
                app.pant_act = current.instancia;
                console.log(`Refrescando datos del contenedor: ${current.element.id || 'sin ID'}`);
                current.instancia.refresh();
            }
            current.cargado = true;
            // Mostrar con animación y esperar a que termine
            await showElement(current.element);
            return current;
        }
    }

    removed.cargado = false;
    return removed;
}

function popFromStackNoFX() {
    if (uiStack.length === 0) return null;
    const removed = uiStack.pop();
    console.log(`Eliminado de pila UI: ${removed.type} - ${removed.element.id || 'sin ID'}`);

    if (document.body.contains(removed.element)) {
        console.log(`Ocultando y eliminando nodo: ${removed.element.id || 'sin ID'}`);

        // Ocultar inmediatamente sin animación
        removed.element.classList.add('hidden');

        if (removed.element._clickOutsideHandler) {
            document.removeEventListener('click', removed.element._clickOutsideHandler);
            delete removed.element._clickOutsideHandler;
        }

        // Eliminar del DOM si es necesario
        if (removed.element.dataset.remove !== 'N') {
            console.log('Eliminado ' + removed.element);
            removed.element.remove();
            delete removed.element;
        } else {
            console.log('NO eliminado nodo ' + removed.element.id);
            // Asegurar que los elementos que no se eliminan no capturen eventos
            removed.element.classList.add('hidden');
        }
    }

    return removed;
}

/**
 * Obtiene el último elemento de la pila sin eliminarlo
 * @returns {Object|null} El último elemento o null si la pila está vacía
 */
function peekUIStack() {
    if (uiStack.length === 0) return null;
    return uiStack[uiStack.length - 1];
}


//Vaciar la pila de pantallas mostradas
function emptyUIStack() {
    let currentElement = peekUIStack();
    while (currentElement) {
        popFromStackNoFX();
        currentElement = peekUIStack();
    }
    uiStack = [];
    app.pant_act = null;
    isPushingHash = false;
}

/** Muestra un elemento. Le quita hidden y le añade show-animation para que se muestre con una animación.
 *
 * @param {HTMLElement} element - El elemento a mostrar.
 * @returns {Promise} Promesa que se resuelve al finalizar la animación.
 */
function showElement(element) {
    return new Promise((resolve) => {
        // Si el elemento ya está visible, resolver inmediatamente
        if (!element.classList.contains('hidden')) {
            resolve();
            return;
        }
        // element.classList.remove('hide-animation');
        element.classList.remove('hidden');
        void element.offsetHeight; // Trigger reflow para activar la animación
        element.classList.add('show-animation');

        const handler = (e) => {
            console.log('fin animación show ' + element.id);
            if (e.animationName === 'show') {
                element.classList.remove('show-animation');
                element.removeEventListener('animationend', handler);
                resolve();
            }
        };
        element.addEventListener('animationend', handler);
    });
}

/**
 * Oculta un elemento con una animación.
 * @param {HTMLElement} element - El elemento a ocultar.
 * @returns {Promise} Promesa que se resuelve al finalizar la animación.
 */
function hideElement(element) {
    return new Promise((resolve) => {
        if (!element || !element.checkVisibility() || element.style.display == 'none') {
            resolve();
            return;
        }
        // Si ya está oculto, resolver inmediatamente
        if (element.classList.contains('hidden')) {
            resolve();
            return;
        }
        const handler = (e) => {
            console.log('fin animación fin ' + element.id);
            if (e.animationName === 'hide') {
                element.classList.remove('hide-animation');
                element.classList.add('hidden');
                element.removeEventListener('animationend', handler);
                resolve();
            }
        };
        element.addEventListener('animationend', handler);

        // Asegurar que no capture eventos durante la animación
        //NUNCA NUNCA NUNCA PONER ÉSTA LINEA. Luego no se quita y la pantalla queda sin responder al ratón
        //element.style.pointerEvents = 'none';

        // element.classList.remove('show-animation');
        element.classList.add('hide-animation');
        void element.offsetHeight;
    });
}


/**
 * Configura el cierre de un elemento UI al hacer clic fuera de él
 * @param {HTMLElement} element - El elemento a cerrar al hacer clic fuera
 * @param {Function} closeFunction - Función para cerrar el elemento
 */
function setupClickOutsideToClose(element, closeFunction) {
    // Usar setTimeout para evitar que el evento actual cierre el menú inmediatamente
    setTimeout(() => {
        const handleClickOutside = (e) => {
            // Si el clic no es dentro del elemento y no es en un elemento que lo abrió
            if (!element.contains(e.target) &&
                // Verificar si el elemento que abrió el menú tiene un dataset.toggleElement
                !(e.target.dataset && e.target.dataset.toggleElement === element.id)) {

                // Verificar si el elemento tiene un manejador especial
                if (element._specialCloseHandler && typeof element._specialCloseHandler === 'function') {
                    // Usar el manejador especial
                    element._specialCloseHandler();
                } else {
                    // Cerrar el elemento con la función estándar
                    closeFunction();
                }

                // Eliminar el listener para evitar duplicados
                document.removeEventListener('click', handleClickOutside);
            }
        };

        // Añadir el listener para detectar clics fuera
        document.addEventListener('click', handleClickOutside);

        // Guardar una referencia al listener en el elemento para poder eliminarlo después
        element._clickOutsideHandler = handleClickOutside;
    }, 0);
}

/**
 * Abre un popup genérico
 * @param {HTMLElement|string} popup - El elemento popup o su ID
 */
function openPopup(popup) {
    const popupElement = typeof popup === 'string' ? document.getElementById(popup) : popup;
    if (!popupElement) return;

    popupElement.classList.remove('hidden');
    pushToUIStack(popupElement, 'popup');

    // Configurar cierre al hacer clic fuera
    setupClickOutsideToClose(popupElement, () => closePopup(popupElement));
}

/**
 * Cierra un popup genérico
 * @param {HTMLElement|string} popup - El elemento popup o su ID
 * @param {boolean} removeFromStack - Si es true, también lo elimina de la pila
 */
function closePopup(popup, removeFromStack = true) {
    const popupElement = typeof popup === 'string' ? document.getElementById(popup) : popup;
    if (!popupElement) return;

    popupElement.classList.add('hidden');
    if (popupElement.id == 'widgets-context-menu') then
    app.dashboardManager.widgetHasContextMenu = undefined;

    // Eliminar el listener de clic fuera si existe
    if (popupElement._clickOutsideHandler) {
        document.removeEventListener('click', popupElement._clickOutsideHandler);
        delete popupElement._clickOutsideHandler;
    }

    // No necesitamos restaurar ningún icono ya que ahora usamos un icono de engranaje estático

    if (removeFromStack) {
        // Encontrar y eliminar este elemento específico de la pila
        const index = uiStack.findIndex(item => item.element === popupElement);
        if (index !== -1) {
            uiStack.splice(index, 1);
            console.log(`Eliminado específicamente de pila UI: popup - ${popupElement.id || 'sin ID'}`);
        }
    }
}

/**
 * Abre un modal genérico con animación
 * @param {HTMLElement|string} modal - El elemento modal o su ID
 */
function openModal(modal, instancia = null, params = {}) {
    const modalElement = typeof modal === 'string' ? document.getElementById(modal) : modal;
    if (!modalElement) return;

    // Quitar la clase hidden para que el modal sea visible
    modalElement.classList.remove('hidden');
    void modalElement.offsetHeight; // Trigger reflow
    // Añadir la clase de animación de entrada
    modalElement.classList.add('modal-entering');

    // Escuchar el evento de fin de animación
    const handleAnimationEnd = (e) => {
        // Solo procesar si es el evento del modal
        if (e.target !== modalElement) return;

        // Quitar la clase de animación
        modalElement.classList.remove('modal-entering');

        // Eliminar el listener para evitar duplicados
        modalElement.removeEventListener('animationend', handleAnimationEnd);

        // Establecer el foco en el primer campo del formulario
        const isMobile = window.innerWidth <= 768;
        if (!isMobile) {
            // Buscar el primer campo de entrada en el formulario
            const firstInput = modalElement.querySelector('input:not([readonly]), textarea:not([readonly]), select:not([readonly])');
            if (firstInput) {
                firstInput.focus();
            }
        }
    }

    // Añadir el listener para la animación
    modalElement.addEventListener('animationend', handleAnimationEnd);

    // Hacer que el modal sea arrastrable por su encabezado
    const modalContent = modalElement.querySelector('.modal-content');
    if (modalContent) {
        // Buscar el encabezado (puede ser un h2 o el primer elemento con clase close-btn)
        let handle = modalContent.querySelector('h2');
        if (!handle) {
            // Si no hay h2, usar el contenedor del botón de cierre
            const closeBtn = modalContent.querySelector('.close-btn');
            if (closeBtn && closeBtn.parentElement) {
                handle = closeBtn.parentElement;
            } else {
                // Si no hay un manejador específico, usar todo el contenido del modal
                handle = modalContent;
            }
        }

        // Asegurarse de que el modal esté en el body, no dentro del dashboard
        if (modalElement.parentElement !== document.body) {
            // Si el modal está dentro de otro elemento, moverlo al body
            document.body.appendChild(modalElement);
        }

        // Si el modal no tiene una posición específica, centrarlo
        if (!modalContent.style.left || !modalContent.style.top) {
            // Centrar el modal
            modalContent.style.top = '50%';
            modalContent.style.left = '50%';
            modalContent.style.transform = 'translate(-50%, -50%)';
        }

        // Hacer el modal arrastrable
        makeDraggable(modalContent, handle);
    }

    // Añadir a la pila UI
    pushToUIStack(modalElement, 'modal', instancia, params);

    // Los modales no se cierran al hacer clic fuera
}

/**
 * Cierra un modal genérico con animación
 * @param {HTMLElement|string} modal - El elemento modal o su ID (opcional, actualmente no se utiliza)
 * @param {boolean} volver_anterior - Si es true, vuelve a la pantalla anterior
 * @param {Function} callback - Función a ejecutar cuando termine la animación
 */
function closeModal(_, volver_anterior = false) {
    // Usamos _ para indicar que el parámetro no se utiliza
    popFromUIStack(volver_anterior);
}

/**
 * Cierra un contenedor genérico con animación
 * @param {HTMLElement|string} container - El elemento contenedor o su ID
 * @param {boolean} removeFromStack - Si es true, también lo elimina de la pila
 * @param {Function} callback - Función a ejecutar cuando termine la animación
 */
function closeContainer(container, removeFromStack = true, callback) {
    const containerElement = typeof container === 'string' ? document.getElementById(container) : container;
    if (!containerElement) return;

    // Eliminar el listener de clic fuera si existe
    if (containerElement._clickOutsideHandler) {
        document.removeEventListener('click', containerElement._clickOutsideHandler);
        delete containerElement._clickOutsideHandler;
    }

    // Añadir clase de animación de salida
    containerElement.classList.add('container-exiting');

    // Escuchar el evento de fin de animación
    const handleAnimationEnd = (e) => {
        // Solo procesar si es el evento del contenedor
        if (e.target !== containerElement) return;

        // Ocultar el contenedor
        containerElement.classList.add('hidden');
        containerElement.classList.remove('container-exiting');

        // Eliminar el listener para evitar duplicados
        containerElement.removeEventListener('animationend', handleAnimationEnd);

        // Ejecutar callback si existe
        if (typeof callback === 'function') {
            callback();
        }

        if (removeFromStack) {
            popFromUIStack();
            if (peekUIStack()) {
                window.history.back();
            } else navigateTo('main-menu');
        }
    };

    // Añadir el listener para la animación
    containerElement.addEventListener('animationend', handleAnimationEnd);
}

/**
 * Cierra el último elemento de la pila UI con animación si es un contenedor o modal
 * @param {Function} callback - Función a ejecutar cuando termine la animación
 */
function closeLastUIElement(callback) {
    const lastElement = peekUIStack(); // Solo miramos, no eliminamos aún
    if (!lastElement) return;

    // Usar animación según el tipo de elemento
    if (lastElement.type === 'container') {
        closeContainer(lastElement.element, true, callback);
    } else if (lastElement.type === 'modal') {
        closeModal(lastElement.element, true, callback);
    } else if (lastElement.type === 'popup') {
        closePopup(lastElement.element, true);
        // Ejecutar callback si existe
        if (typeof callback === 'function') {
            callback();
        }
    } else {
        // Para otros tipos, comportamiento normal
        popFromUIStack(); // Ahora sí eliminamos de la pila
        lastElement.element.classList.add('hidden');

        // Ejecutar callback si existe
        if (typeof callback === 'function') {
            callback();
        }
    }
}

/**
 * Hace que un elemento sea arrastrable
 * @param {HTMLElement} element - Elemento a hacer arrastrable
 * @param {HTMLElement} handle - Elemento que sirve como "manija" para arrastrar
 */
function makeDraggable(element, handle) {
    // Limpiar eventos previos si existen
    if (element._dragInitialized && element._dragHandle) {
        if (element._dragHandle) {
            element._dragHandle.onmousedown = null;
            element._dragHandle.ontouchstart = null;
        } else {
            element.onmousedown = null;
            element.ontouchstart = null;
        }
    }

    let startX = 0,
        startY = 0,
        startLeft = 0,
        startTop = 0;
    let isDragging = false;
    let dragThreshold = 5; // Umbral en píxeles para iniciar el arrastre

    // Asegurar que el z-index sea alto para que esté por encima de otros elementos
    if (element.parentElement && element.parentElement.classList.contains('modal')) {
        element.style.zIndex = '1001'; // Asegurar que esté por encima del fondo del modal
    }

    // Guardar referencia al handle para limpieza posterior
    element._dragHandle = handle;
    element._dragInitialized = true;

    if (handle) {
        // Si se proporciona un handle, el evento se inicia en él
        handle.style.cursor = 'move';
        handle.onmousedown = dragMouseDown;
        handle.ontouchstart = dragTouchStart;
    } else {
        // De lo contrario, el evento se inicia en el elemento completo
        element.onmousedown = dragMouseDown;
        element.ontouchstart = dragTouchStart;
    }



    function dragMouseDown(e) {
        e.preventDefault();
        e.stopPropagation();

        // Obtener la posición inicial del cursor
        startX = e.clientX;
        startY = e.clientY;

        // Guardar la posición inicial del elemento sin ajustar el transform todavía
        /*const computedStyle = window.getComputedStyle(element);
        startLeft = parseInt(computedStyle.left) || 0;
        startTop = parseInt(computedStyle.top) || 0;*/

        // Convertir valores en NaN a 0
        if (isNaN(startLeft)) startLeft = 0;
        if (isNaN(startTop)) startTop = 0;

        // Inicializar el estado de arrastre
        isDragging = false;

        // Añadir listeners a nivel de documento
        document.addEventListener('mousemove', elementDrag);
        document.addEventListener('mouseup', closeDragElement);

        // Añadir clase para indicar que se está arrastrando
        element.classList.add('dragging');
    }

    function dragTouchStart(e) {
        if (e.touches && e.touches.length) {
            e.preventDefault();
            e.stopPropagation();

            // Obtener la posición inicial del toque
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;

            // Guardar la posición inicial del elemento sin ajustar el transform todavía
            const computedStyle = window.getComputedStyle(element);
            startLeft = parseInt(computedStyle.left) || 0;
            startTop = parseInt(computedStyle.top) || 0;

            // Convertir valores en NaN a 0
            if (isNaN(startLeft)) startLeft = 0;
            if (isNaN(startTop)) startTop = 0;

            // Inicializar el estado de arrastre
            isDragging = false;

            // Añadir listeners a nivel de documento
            document.addEventListener('touchmove', elementTouchDrag, {
                passive: false
            });
            document.addEventListener('touchend', closeDragElement);

            // Añadir clase para indicar que se está arrastrando
            element.classList.add('dragging');
        }
    }

    function elementDrag(e) {
        e.preventDefault();
        e.stopPropagation();

        // Calcular la distancia movida
        const dx = e.clientX - startX;
        const dy = e.clientY - startY;

        // Si no estamos arrastrando, verificar si superamos el umbral
        if (!isDragging) {
            const distance = Math.sqrt(dx * dx + dy * dy);
            if (distance < dragThreshold) {
                return; // No iniciar arrastre hasta superar el umbral
            }
            isDragging = true;

            // Obtener la posición actual del elemento en la pantalla
            const rect = element.getBoundingClientRect();

            // Eliminar la transformación y establecer la posición exacta
            element.style.transform = 'none';
            element.style.translate = 'none';
            element.style.left = rect.left + 'px';
            element.style.top = rect.top + 'px';

            // Actualizar las variables de inicio
            startLeft = rect.left;
            startTop = rect.top;
        }

        // Calcular las nuevas coordenadas
        let newLeft = startLeft + dx;
        let newTop = startTop + dy;

        // Limitar el movimiento para que no salga completamente de la pantalla
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        const elementWidth = element.offsetWidth;
        const elementHeight = element.offsetHeight;

        // Asegurar que al menos 100px del elemento permanezcan visibles
        const minVisiblePx = 100;

        newLeft = Math.max(-elementWidth + minVisiblePx, Math.min(newLeft, windowWidth - minVisiblePx));
        newTop = Math.max(-elementHeight + minVisiblePx, Math.min(newTop, windowHeight - minVisiblePx));

        // Establecer la nueva posición del elemento
        element.style.left = newLeft + "px";
        element.style.top = newTop + "px";
    }

    function elementTouchDrag(e) {
        if (e.touches && e.touches.length) {
            e.preventDefault();
            e.stopPropagation();

            // Calcular la distancia movida
            const dx = e.touches[0].clientX - startX;
            const dy = e.touches[0].clientY - startY;

            // Si no estamos arrastrando, verificar si superamos el umbral
            if (!isDragging) {
                const distance = Math.sqrt(dx * dx + dy * dy);
                if (distance < dragThreshold) {
                    return; // No iniciar arrastre hasta superar el umbral
                }
                isDragging = true;

                // Obtener la posición actual del elemento en la pantalla
                const rect = element.getBoundingClientRect();

                // Eliminar la transformación y establecer la posición exacta
                element.style.transform = 'none';
                element.style.translate = 'none';
                element.style.left = rect.left + 'px';
                element.style.top = rect.top + 'px';

                // Actualizar las variables de inicio
                startLeft = rect.left;
                startTop = rect.top;
            }

            // Calcular las nuevas coordenadas
            let newLeft = startLeft + dx;
            let newTop = startTop + dy;

            // Limitar el movimiento para que no salga completamente de la pantalla
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;
            const elementWidth = element.offsetWidth;
            const elementHeight = element.offsetHeight;

            // Asegurar que al menos 100px del elemento permanezcan visibles
            const minVisiblePx = 100;

            newLeft = Math.max(-elementWidth + minVisiblePx, Math.min(newLeft, windowWidth - minVisiblePx));
            newTop = Math.max(-elementHeight + minVisiblePx, Math.min(newTop, windowHeight - minVisiblePx));

            // Establecer la nueva posición del elemento
            element.style.left = newLeft + "px";
            element.style.top = newTop + "px";
        }
    }

    function closeDragElement() {
        // Quitar listeners
        document.removeEventListener('mousemove', elementDrag);
        document.removeEventListener('mouseup', closeDragElement);
        document.removeEventListener('touchmove', elementTouchDrag);
        document.removeEventListener('touchend', closeDragElement);

        // Quitar clase de arrastre
        element.classList.remove('dragging');

        isDragging = false;
    }
}

class DialogManager {
    constructor() {
        this.init();
        this.activePromises = new Map(); // Para manejar diálogos que bloquean la ejecución
    }

    /**
     * Inicializa el gestor de diálogos
     */
    init() {
        // Crear plantilla de diálogo si no existe
        let dialogTemplate = document.getElementById('dialog-template');
        if (!dialogTemplate) {
            dialogTemplate = document.createElement('div');
            dialogTemplate.id = 'dialog-template';
            dialogTemplate.className = 'dialog';
            dialogTemplate.classList.add('hidden'); // Asegurarse de que esté oculto inicialmente
            document.body.appendChild(dialogTemplate);
        } else {
            // Si ya existe, asegurarse de que esté oculto
            dialogTemplate.classList.add('hidden');
        }
    }

    /**
     * Muestra un diálogo de confirmación
     * @param {string} message - Mensaje a mostrar
     * @param {Function} onConfirm - Función a ejecutar si se confirma
     * @param {Function} onCancel - Función a ejecutar si se cancela
     * @returns {Promise} - Promesa que se resuelve con true si se confirma, false si se cancela
     */
    showConfirmDialog(message, onConfirm, onCancel) {
        // Crear un ID único para este diálogo
        const dialogId = `confirm-dialog-${Date.now()}`;

        // Crear el diálogo clonando la plantilla
        const dialogTemplate = document.getElementById('dialog-template');
        const dialog = dialogTemplate.cloneNode(false);
        dialog.className = 'dialog confirm-dialog';
        dialog.id = dialogId;

        // Contenido del diálogo
        dialog.innerHTML = `
            <div class="dialog-content">
                <div class="dialog-header">
                    <h3>Confirmar</h3>
                    <span class="close-btn">&times;</span>
                </div>
                <div class="dialog-body">
                    <p>${message}</p>
                </div>
                <div class="button-group">
                    <button id="${dialogId}-confirm-btn" class="primary-btn">Aceptar</button>
                    <button id="${dialogId}-cancel-btn" class="secondary-btn">Cancelar</button>
                </div>
            </div>
        `;

        // Añadir directamente al body
        document.body.appendChild(dialog);
        dialog.classList.remove('hidden');

        // Añadir el diálogo a la pila UI
        pushToUIStack(dialog, 'dialog', this, {
            onCancel
        });

        // Hacer el diálogo arrastrable por el encabezado
        const dialogContent = dialog.querySelector('.dialog-content');
        const dialogHeader = dialog.querySelector('.dialog-header');
        makeDraggable(dialogContent, dialogHeader);

        // Crear una promesa para manejar el bloqueo
        let resolvePromise;
        const promise = new Promise(resolve => {
            resolvePromise = resolve;
        });

        // Guardar la promesa en el mapa
        this.activePromises.set(dialogId, resolvePromise);

        // Configurar eventos
        const confirmBtn = document.getElementById(`${dialogId}-confirm-btn`);
        const cancelBtn = document.getElementById(`${dialogId}-cancel-btn`);
        const closeBtn = dialog.querySelector('.close-btn');

        // Dar foco al botón de confirmar
        requestAnimationFrame(() => {
            if (confirmBtn) confirmBtn.focus();
        });

        const closeDialog = (result) => {           
            // Añadir clase para la animación de salida
            dialog.classList.add('dialog-exiting');

            // Escuchar el evento de fin de animación
            dialog.addEventListener('animationend', function handleAnimEnd() {
                // Eliminar el listener para evitar duplicados
                dialog.removeEventListener('animationend', handleAnimEnd);

                // Eliminar el diálogo de la pila UI (popFromUIStack ahora elimina el nodo del DOM)
                popFromUIStack();

                // Resolver la promesa con el resultado
                const resolve = this.activePromises.get(dialogId);
                if (resolve) {
                    resolve(result);
                    this.activePromises.delete(dialogId);
                }

                // Asegurar que el diálogo se elimina del DOM
                if (document.body.contains(dialog)) {
                    dialog.remove();
                }
            }.bind(this), {
                once: true
            });
        };

        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => {
                closeDialog(true);
                if (onConfirm) onConfirm();
            });
        }

        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                closeDialog(false);
                if (onCancel) onCancel();
            });
        }

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                closeDialog(false);
                if (onCancel) onCancel();
            });
        }

        // Los diálogos no se cierran al hacer clic fuera

        // La tecla Escape ahora se maneja globalmente en app.js

        // Devolver la promesa para permitir el uso con async/await
        return promise;
    }

    /**
     * Reemplazo para window.confirm que usa nuestro diálogo personalizado
     * @param {string} message - Mensaje a mostrar
     * @returns {Promise<boolean>} - Promesa que se resuelve con true si se confirma, false si se cancela
     */
    confirm(message) {
        return this.showConfirmDialog(message);
    }

    /**
     * Muestra un diálogo de alerta
     * @param {string} message - Mensaje a mostrar
     * @param {Function} onClose - Función a ejecutar al cerrar
     * @returns {Promise} - Promesa que se resuelve cuando se cierra el diálogo
     */
    showAlertDialog(message, onClose) {
        // Crear un ID único para este diálogo
        const dialogId = `alert-dialog-${Date.now()}`;

        // Crear el diálogo clonando la plantilla
        const dialogTemplate = document.getElementById('dialog-template');
        const dialog = dialogTemplate.cloneNode(false);
        dialog.className = 'dialog alert-dialog';
        dialog.id = dialogId;

        // Contenido del diálogo
        dialog.innerHTML = `
            <div class="dialog-content">
                <div class="dialog-header">
                    <h3>Aviso</h3>
                    <span class="close-btn">&times;</span>
                </div>
                <div class="dialog-body">
                    <p>${message}</p>
                </div>
                <div class="button-group">
                    <button id="${dialogId}-close-btn" class="primary-btn">Aceptar</button>
                </div>
            </div>
        `;

        // Añadir directamente al body
        document.body.appendChild(dialog);
        dialog.classList.remove('hidden');

        // Añadir el diálogo a la pila UI
        pushToUIStack(dialog, 'dialog', this, {
            onClose
        });

        // Hacer el diálogo arrastrable por el encabezado
        const dialogContent = dialog.querySelector('.dialog-content');
        const dialogHeader = dialog.querySelector('.dialog-header');
        makeDraggable(dialogContent, dialogHeader);

        // Crear una promesa para manejar el bloqueo
        let resolvePromise;
        const promise = new Promise(resolve => {
            resolvePromise = resolve;
        });

        // Guardar la promesa en el mapa
        this.activePromises.set(dialogId, resolvePromise);

        // Configurar eventos
        const acceptBtn = document.getElementById(`${dialogId}-close-btn`);
        const closeBtn = dialog.querySelector('.close-btn');

        // Dar foco al botón de aceptar
        requestAnimationFrame(() => {
            if (acceptBtn) acceptBtn.focus();
        });

        const closeDialog = () => {
            // Añadir clase para la animación de salida
            dialog.classList.add('dialog-exiting');

            // Escuchar el evento de fin de animación
            dialog.addEventListener('animationend', function handleAnimEnd() {
                // Eliminar el listener para evitar duplicados
                dialog.removeEventListener('animationend', handleAnimEnd);

                // Eliminar el diálogo de la pila UI (popFromUIStack ahora elimina el nodo del DOM)
                popFromUIStack();

                // Resolver la promesa
                const resolve = this.activePromises.get(dialogId);
                if (resolve) {
                    resolve();
                    this.activePromises.delete(dialogId);
                }

                if (onClose) onClose();

                // Asegurar que el diálogo se elimina del DOM
                if (document.body.contains(dialog)) {
                    dialog.remove();
                }
            }.bind(this), {
                once: true
            });
        };

        if (acceptBtn) {
            acceptBtn.addEventListener('click', closeDialog);
        }

        if (closeBtn) {
            closeBtn.addEventListener('click', closeDialog);
        }

        // La tecla Escape ahora se maneja globalmente en app.js

        // Devolver la promesa para permitir el uso con async/await
        return promise;
    }

    /**
     * Reemplazo para window.alert que usa nuestro diálogo personalizado
     * @param {string} message - Mensaje a mostrar
     * @returns {Promise} - Promesa que se resuelve cuando se cierra el diálogo
     */
    alert(message) {
        return this.showAlertDialog(message);
    }

    /**
     * Muestra un diálogo de entrada de texto
     * @param {string} message - Mensaje a mostrar
     * @param {string} defaultValue - Valor por defecto
     * @param {Function} onConfirm - Función a ejecutar si se confirma (recibe el valor)
     * @param {Function} onCancel - Función a ejecutar si se cancela
     * @returns {Promise<string|null>} - Promesa que se resuelve con el valor ingresado o null si se cancela
     */
    showPromptDialog(message, defaultValue = '', onConfirm, onCancel) {
        // Crear un ID único para este diálogo
        const dialogId = `prompt-dialog-${Date.now()}`;

        // Crear el diálogo clonando la plantilla
        const dialogTemplate = document.getElementById('dialog-template');
        const dialog = dialogTemplate.cloneNode(false);
        dialog.className = 'dialog prompt-dialog';
        dialog.id = dialogId;

        // Contenido del diálogo
        dialog.innerHTML = `
            <div class="dialog-content">
                <div class="dialog-header">
                    <h3>Entrada</h3>
                    <span class="close-btn">&times;</span>
                </div>
                <div class="dialog-body">
                    <p>${message}</p>
                    <div class="form-group">
                        <input type="text" id="${dialogId}-input" value="${defaultValue}">
                    </div>
                </div>
                <div class="button-group">
                    <button id="${dialogId}-confirm-btn" class="primary-btn">Aceptar</button>
                    <button id="${dialogId}-cancel-btn" class="secondary-btn">Cancelar</button>
                </div>
            </div>
        `;

        // Añadir directamente al body
        document.body.appendChild(dialog);
        dialog.classList.remove('hidden');

        // Añadir el diálogo a la pila UI
        pushToUIStack(dialog, 'dialog', this, {
            onCancel
        });

        // Hacer el diálogo arrastrable por el encabezado
        const dialogContent = dialog.querySelector('.dialog-content');
        const dialogHeader = dialog.querySelector('.dialog-header');
        makeDraggable(dialogContent, dialogHeader);

        // Crear una promesa para manejar el bloqueo
        let resolvePromise;
        const promise = new Promise(resolve => {
            resolvePromise = resolve;
        });

        // Guardar la promesa en el mapa
        this.activePromises.set(dialogId, resolvePromise);

        // Dar foco al input
        const input = document.getElementById(`${dialogId}-input`);
        if (input) {
            // Usar requestAnimationFrame para asegurar que el DOM está listo
            requestAnimationFrame(() => {
                input.focus();
                input.select();
            });
        }

        // Configurar eventos
        const confirmBtn = document.getElementById(`${dialogId}-confirm-btn`);
        const cancelBtn = document.getElementById(`${dialogId}-cancel-btn`);
        const closeBtn = dialog.querySelector('.close-btn');

        const closeDialog = (value) => {
            // Añadir clase para la animación de salida
            dialog.classList.add('dialog-exiting');

            // Escuchar el evento de fin de animación
            dialog.addEventListener('animationend', function handleAnimEnd() {
                // Eliminar el listener para evitar duplicados
                dialog.removeEventListener('animationend', handleAnimEnd);

                // Eliminar el diálogo de la pila UI (popFromUIStack ahora elimina el nodo del DOM)
                popFromUIStack();

                // Resolver la promesa con el valor
                const resolve = this.activePromises.get(dialogId);
                if (resolve) {
                    resolve(value);
                    this.activePromises.delete(dialogId);
                }

                // Asegurar que el diálogo se elimina del DOM
                if (document.body.contains(dialog)) {
                    dialog.remove();
                }
            }.bind(this), {
                once: true
            });
        };

        const handleConfirm = () => {
            const value = input ? input.value : '';
            closeDialog(value);
            if (onConfirm) onConfirm(value);
        };

        const handleCancel = () => {
            closeDialog(null);
            if (onCancel) onCancel();
        };

        if (confirmBtn) {
            confirmBtn.addEventListener('click', handleConfirm);
        }

        if (cancelBtn) {
            cancelBtn.addEventListener('click', handleCancel);
        }

        if (closeBtn) {
            closeBtn.addEventListener('click', handleCancel);
        }

        // Confirmar con Enter
        if (input) {
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    handleConfirm();
                }
            });
        }

        // Los diálogos no se cierran al hacer clic fuera

        // La tecla Escape ahora se maneja globalmente en app.js

        // Devolver la promesa para permitir el uso con async/await
        return promise;
    }

    /**
     * Reemplazo para window.prompt que usa nuestro diálogo personalizado
     * @param {string} message - Mensaje a mostrar
     * @param {string} defaultValue - Valor por defecto
     * @returns {Promise<string|null>} - Promesa que se resuelve con el valor ingresado o null si se cancela
     */
    prompt(message, defaultValue = '') {
        return this.showPromptDialog(message, defaultValue);
    }

    /**
     * Muestra un diálogo de confirmación para eliminar con estilo rojizo
     * @param {string} message - Mensaje a mostrar
     * @param {Function} onConfirm - Función a ejecutar si se confirma
     * @param {Function} onCancel - Función a ejecutar si se cancela
     * @returns {Promise} - Promesa que se resuelve con true si se confirma, false si se cancela
     */
    showDeleteConfirmDialog(message, onConfirm, onCancel) {
        // Crear un ID único para este diálogo
        const dialogId = `delete-confirm-dialog-${Date.now()}`;

        // Crear el diálogo clonando la plantilla
        const dialogTemplate = document.getElementById('dialog-template');
        const dialog = dialogTemplate.cloneNode(false);
        dialog.className = 'dialog delete-confirm-dialog';
        dialog.id = dialogId;

        // Contenido del diálogo
        dialog.innerHTML = `
            <div class="dialog-content">
                <div class="dialog-header">
                    <h3>Confirmar eliminación</h3>
                    <span class="close-btn">&times;</span>
                </div>
                <div class="dialog-body">
                    <p>${message}</p>
                </div>
                <div class="button-group">
                    <button id="${dialogId}-confirm-btn" class="danger-btn">Eliminar</button>
                    <button id="${dialogId}-cancel-btn" class="secondary-btn">Cancelar</button>
                </div>
            </div>
        `;

        // Añadir directamente al body
        document.body.appendChild(dialog);
        dialog.classList.remove('hidden');

        // Añadir el diálogo a la pila UI
        pushToUIStack(dialog, 'dialog', app.pant_act, {
            onCancel
        });

        // Hacer el diálogo arrastrable por el encabezado
        const dialogContent = dialog.querySelector('.dialog-content');
        const dialogHeader = dialog.querySelector('.dialog-header');
        makeDraggable(dialogContent, dialogHeader);

        // Crear una promesa para manejar el bloqueo
        let resolvePromise;
        const promise = new Promise(resolve => {
            resolvePromise = resolve;
        });

        // Guardar la promesa en el mapa
        this.activePromises.set(dialogId, resolvePromise);

        // Configurar eventos
        const confirmBtn = document.getElementById(`${dialogId}-confirm-btn`);
        const cancelBtn = document.getElementById(`${dialogId}-cancel-btn`);
        const closeBtn = dialog.querySelector('.close-btn');

        // Dar foco al botón de confirmar
        requestAnimationFrame(() => {
            if (confirmBtn) confirmBtn.focus();
        });

        const closeDialog = (result) => {
            // Añadir clase para la animación de salida
            dialog.classList.add('dialog-exiting');

            // Escuchar el evento de fin de animación
            dialog.addEventListener('animationend', function handleAnimEnd() {
                // Eliminar el listener para evitar duplicados
                dialog.removeEventListener('animationend', handleAnimEnd);

                // Eliminar el diálogo de la pila UI (popFromUIStack ahora elimina el nodo del DOM)
                popFromUIStack();

                // Resolver la promesa con el resultado
                const resolve = this.activePromises.get(dialogId);
                if (resolve) {
                    resolve(result);
                    this.activePromises.delete(dialogId);
                }

                // Asegurar que el diálogo se elimina del DOM
                if (document.body.contains(dialog)) {
                    dialog.remove();
                }
            }.bind(this), {
                once: true
            });
        };

        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => {
                closeDialog(true);
                if (onConfirm) onConfirm();
            });
        }

        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                closeDialog(false);
                if (onCancel) onCancel();
            });
        }

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                closeDialog(false);
                if (onCancel) onCancel();
            });
        }

        // Los diálogos no se cierran al hacer clic fuera

        // La tecla Escape ahora se maneja globalmente en app.js

        // Devolver la promesa para permitir el uso con async/await
        return promise;
    }

    /**
     * Reemplazo para window.confirm que usa nuestro diálogo personalizado para eliminación
     * @param {string} message - Mensaje a mostrar
     * @returns {Promise<boolean>} - Promesa que se resuelve con true si se confirma, false si se cancela
     */
    confirmDelete(message) {
        return this.showDeleteConfirmDialog(message);
    }

    /**
     * Muestra un diálogo personalizado con contenido HTML y botones configurables
     * @param {Object} options - Opciones del diálogo
     * @param {string} options.title - Título del diálogo
     * @param {string} options.content - Contenido HTML del diálogo
     * @param {Array} options.buttons - Array de botones con texto, tipo y acción
     * @returns {Object} - Objeto con método close para cerrar el diálogo
     */
    custom(options) {
        // Crear un ID único para este diálogo
        const dialogId = `custom-dialog-${Date.now()}`;

        // Crear el diálogo clonando la plantilla
        const dialogTemplate = document.getElementById('dialog-template');
        const dialog = dialogTemplate.cloneNode(false);
        dialog.className = 'dialog custom-dialog';
        dialog.id = dialogId;

        // Contenido del diálogo
        dialog.innerHTML = `
            <div class="dialog-content">
                <div class="dialog-header">
                    <h3>${options.title || 'Diálogo'}</h3>
                    <span class="close-btn">&times;</span>
                </div>
                <div class="dialog-body">
                    ${options.content || ''}
                </div>
                <div class="button-group">
                    ${options.buttons ? options.buttons.map((btn, index) =>
                        `<button id="${dialogId}-btn-${index}" class="${btn.type || 'secondary'}-btn">${btn.text}</button>`
                    ).join('') : '<button id="${dialogId}-close-btn" class="primary-btn">Aceptar</button>'}
                </div>
            </div>
        `;

        // Añadir directamente al body
        document.body.appendChild(dialog);
        dialog.classList.remove('hidden');

        // Añadir el diálogo a la pila UI
        pushToUIStack(dialog, 'dialog', this);

        // Hacer el diálogo arrastrable por el encabezado
        const dialogContent = dialog.querySelector('.dialog-content');
        const dialogHeader = dialog.querySelector('.dialog-header');
        makeDraggable(dialogContent, dialogHeader);

        // Objeto para controlar el diálogo
        const dialogControl = {
            close: () => {
                // Añadir clase para la animación de salida
                dialog.classList.add('dialog-exiting');

                // Escuchar el evento de fin de animación
                dialog.addEventListener('animationend', function handleAnimEnd() {
                    // Eliminar el listener para evitar duplicados
                    dialog.removeEventListener('animationend', handleAnimEnd);

                    // Eliminar el diálogo de la pila UI (popFromUIStack ahora elimina el nodo del DOM)
                    popFromUIStack();

                    // Asegurar que el diálogo se elimina del DOM
                    if (document.body.contains(dialog)) {
                        dialog.remove();
                    }
                }, {
                    once: true
                });
            }
        };

        // Configurar botones
        if (options.buttons) {
            options.buttons.forEach((btn, index) => {
                const btnElement = document.getElementById(`${dialogId}-btn-${index}`);
                if (btnElement && btn.action) {
                    btnElement.addEventListener('click', () => {
                        // Si la acción devuelve true, cerrar el diálogo
                        if (btn.action(dialogControl) !== false) {
                            dialogControl.close();
                        }
                    });
                }
            });
        }

        // Configurar botón de cierre
        const closeBtn = dialog.querySelector('.close-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                dialogControl.close();
            });
        }

        // Devolver el objeto de control
        return dialogControl;
    }
}

// Crear instancia del gestor de diálogos
const dialogManager = new DialogManager();