/**
 * Estilos responsive para iPRA Dashboard
 */

/* Estilos generales responsive */
@media (max-width: 768px) {
    body {
        font-size: 14px;
        overflow-x: hidden;
        /* Evitar scroll horizontal en el body */
    }

    .container {
        padding: 10px;
        max-width: 100%;
        max-height: 100vh;
        overflow-x: hidden;
        /* Evitar scroll horizontal en el contenedor principal */
        position: relative;
        top: 0;
        left: 0;
        transform: none;
        margin: 0;
    }

    .entity-management-container {
        height: 100vh;
    }

    .entity-table-container {
        height: calc(100vh - 150px);
        max-height: calc(100vh - 150px);
    }

    .clear-filter-btn {
        top: 22px;
        /* Ajustar posición en móviles */
    }

    #login-container {
        height: 100vh;
    }

    main {
        width: 100%;
        max-width: 100%;
        overflow-x: auto;
        /* Permitir scroll horizontal en el main */
        -webkit-overflow-scrolling: touch;
        /* Scroll suave en iOS */
    }

    /* Ajustar el ancho del contenedor de entidades en móvil */
    .entity-container {
        width: 100%;
    }

    header {
        padding: 10px 0;
        flex-direction: row !important;
        /* Forzar dirección horizontal */
        align-items: center;
        justify-content: space-between;
    }

    header h1 {
        font-size: 18px;
        margin-bottom: 0;
        white-space: wrap;
    }

    /* Ocultar el botón de opciones normal y mostrar el icono de menú móvil */
    #options-btn {
        display: none;
    }

    #mobile-menu-btn {
        display: flex !important;
    }

    /* Hacer que el menú desplegable se abra hacia abajo en móviles */
    .dropdown-menu {
        top: 100%;
        bottom: auto;
        width: 160px;
        /* Ancho reducido para que quepa en pantalla */
        max-width: 70%;
        /* Asegurar que no ocupe más del 70% del ancho */
        right: 0;
        left: auto;
        transform: none;
        max-height: 80vh;
        /* Limitar altura máxima */
        overflow-y: auto;
        /* Permitir scroll si hay muchas opciones */
        z-index: 1000;
        /* Asegurar que esté por encima de otros elementos */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        border-radius: 4px;
        font-size: 14px;
        /* Texto más pequeño para móviles */
    }

    /* Asegurar que el menú no se salga de la pantalla */
    .menu-container {
        position: relative;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        z-index: 1201;
        top: -2em;
    }

    #main-menu-container {
        width: 300px;
        margin-right: auto;
        margin-left: auto;
        height: fit-content;
    }

    /* Ajustar posición del menú para que no se salga de la pantalla */
    @media (max-width: 320px) {
        .dropdown-menu {
            right: -10px;
            /* Ajustar posición en pantallas muy pequeñas */
            width: 140px;
        }
    }

    /* Ajustar tamaño de botones para mejor toque */
    button {
        padding: 12px 15px;
        min-height: 45px;
        /* Mínimo recomendado para elementos táctiles */
    }

    /* Botones más compactos en el menú desplegable */
    .dropdown-menu button {
        padding: 8px 10px;
        min-height: 36px;
        font-size: 13px;
        white-space: normal;
        /* Permitir que el texto se envuelva */
        text-align: left;
        line-height: 1.2;
    }

    /* Estilos para botones de iconos en móvil */
    .icon-btn {
        min-width: 40px !important;
        width: auto !important;
        padding: 0 10px !important;
        font-size: 20px !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        text-align: center !important;
    }

    /* Distribuir botones en una sola fila */
    .button-row {
        display: flex !important;
        flex-direction: row !important;
        justify-content: space-between !important;
        width: 100% !important;
        gap: 5px !important;
    }

    /* Ajuste específico para el botón de menú móvil */
    .mobile-menu-btn {
        min-height: 30px;
        padding: 0;
        display: flex !important;
        justify-content: center;
        align-items: center;
        text-align: center;
        line-height: 30px;
        /* Igual a la altura para centrar verticalmente */
    }

    /* Ajustar modales para pantallas pequeñas */
    .modal-content {
        width: 90%;
        max-width: 400px;
        padding: 15px;
    }

    .modal-content h2 {
        margin-bottom: 5px;
        /* Reducido de 7px a 5px */
        margin-top: 0;
        /* Cambiado de -5px a 0 */
    }

    .modal-content .close-btn {
        margin-bottom: 5px;
        /* Reducido de 7px a 5px */
        margin-top: -5px;
        /* Cambiado de -7px a -5px */
    }

    /* Estilos para la fila de cabecera del widget con acciones en móvil */
    .widget-header-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        flex-wrap: nowrap;
    }

    .widget-type-label {
        margin-bottom: 0 !important;
        white-space: nowrap;
        font-size: 14px;
        flex: 1;
    }

    .widget-actions-container {
        display: flex;
        gap: 5px;
        margin-left: 5px;
    }

    /* Estilos para la fila del selector de tipo de widget en móvil */
    .widget-type-row {
        margin-bottom: 12px;
        padding-left: 0;
    }

    .widget-type-select {
        width: 100%;
        font-size: 14px;
    }

    .icon-action-btn {
        width: 32px;
        height: 32px;
        min-height: 32px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
    }

    .action-icon {
        font-size: 18px;
    }

    /* Estilos específicos para el modal de configuración de columnas */
    #column-config-modal .modal-content {
        width: 430px !important;
        /* Mantener ancho fijo */
        max-width: 95vw !important;
        /* Pero asegurar que no se salga de la pantalla */
    }

    /* En modo horizontal, ajustar altura */
    @media (orientation: landscape) {
        #column-config-modal .modal-content {
            height: 100vh;
            max-height: 100vh;
            border-radius: 0;
        }
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
    }

    #dashboard-config-modal .form-group label {
        display: inline-block;
        margin-bottom: 5px;
        width: 45%;
    }

    #dashboard-config-modal .form-group input,
    #dashboard-config-modal .form-group select {
        width: 49%;
    }

    .form-group input,
    .form-group select {
        width: 100%;
    }

    /* Ajustar el dashboard para scroll */
    .dashboard {
        width: 800px !important;
        /* Mantener ancho fijo para posicionamiento correcto */
        height: 600px !important;
        /* Mantener altura fija para posicionamiento correcto */
        overflow: visible;
        /* Permitir que el contenido sobresalga */
        position: relative;
        margin: 0 auto;
        border-radius: 0 !important;
        /* Sin bordes redondeados en móviles */
        transform: none !important;
        /* Evitar transformaciones que afecten al posicionamiento */
        min-width: 800px;
        /* Asegurar que el dashboard mantenga su ancho mínimo */
    }

    /* Contenedor para el dashboard con scroll horizontal */
    .dashboard-container {
        width: 100%;
        max-width: 100%;
        overflow-x: auto !important;
        /* Forzar scroll horizontal */
        overflow-y: visible;
        -webkit-overflow-scrolling: touch;
        /* Scroll suave en iOS */
        padding-bottom: 20px;
        /* Espacio para los botones de navegación */
        position: relative;
        /* Asegurar que el contenedor tenga posición relativa */
        display: block;
        /* Asegurar que el contenedor se muestre como bloque */
        white-space: nowrap;
        /* Evitar que el contenido se envuelva */
        scrollbar-width: thin;
        /* Barra de desplazamiento delgada en Firefox */
        scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
        /* Color de la barra de desplazamiento en Firefox */
    }

    /* Indicador de scroll horizontal */
    .dashboard-container::after {
        content: '← Desliza →';
        position: absolute;
        bottom: 5px;
        right: 10px;
        background-color: rgba(0, 0, 0, 0.5);
        color: white;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        opacity: 0.8;
        /*    pointer-events: none;*/
        /* No interferir con los eventos del mouse */
        animation: fadeOut 3s forwards 2s;
        /* Desaparecer después de 5 segundos */
        z-index: 100;
    }

    @keyframes fadeOut {
        from {
            opacity: 0.8;
        }

        to {
            opacity: 0;
        }
    }

    /* Estilos para barras de desplazamiento en WebKit */
    .dashboard-container::-webkit-scrollbar {
        height: 6px;
    }

    .dashboard-container::-webkit-scrollbar-track {
        background: transparent;
    }

    .dashboard-container::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 3px;
    }

    /* Botones de navegación en móvil */
    .navigation-buttons {
        position: absolute;
        bottom: 20px;
        right: 20px;
        top: auto;
        /* Anular la posición top de escritorio */
        opacity: 0.95;
        /* Más visible en móvil */
        transform: none !important;
        /* Evitar transformaciones que puedan afectar la posición */
    }

    .nav-button {
        width: 50px;
        height: 50px;
        /* Botones más grandes para facilitar el toque */
        font-size: 28px;
        /* Texto más grande */
    }

    /* Estilos específicos para botones de navegación en formularios móviles */
    .mobile-nav-button {
        width: auto !important;
        height: auto !important;
        min-height: 45px !important;
        border-radius: 4px !important;
        font-size: 20px !important;
        flex: 1 !important;
    }

    /* Ajustes para el formulario de login en móviles */
    .login-form {
        width: 280px !important;
        /* Ancho más estrecho en móviles */
        padding: 20px;
    }

    .login-form h2 {
        font-size: 1.2rem;
        margin-bottom: 15px;
    }

    .form-group {
        margin-bottom: 12px;
    }

    .form-group label {
        font-size: 0.9rem;
    }

    .form-group input,
    .form-group select {
        padding: 8px;
        font-size: 0.9rem;
    }

    /* Estilos para el selector de tema */
    .theme-selector {
        margin-top: 15px;
        width: 100%;
    }

    .theme-selector select {
        width: 100%;
        padding: 8px;
        border-radius: 4px;
        background-color: var(--surface-color);
        border: 1px solid var(--border-color);
        color: var(--text-color);
        font-size: 0.9rem;
    }

    /* Ajustar el botón de login */
    #login-btn {
        width: 100%;
        margin-top: 15px;
    }
}

/* Configuración para pantallas de entidades en modo vertical */
@media (max-width: 768px) and (orientation: portrait) {

    /* Mostrar el botón de volver atrás y ocultar el menú móvil */
    #entity-back-btn,
    [id^="entity-back-btn-"] {
        display: flex !important;
        /* Hacemos visible el botón de volver atrás */
    }

    /* Ocultamos el botón de menú móvil en las entidades */
    #entity-mobile-menu-btn,
    [id^="entity-mobile-menu-btn-"] {
        display: none !important;
    }
}

/* Ajustes para pantallas muy pequeñas */
@media (max-width: 480px) {
    header h1 {
        font-size: 1.2rem;
    }

    /* Estilo general para grupos de botones en móvil */
    .button-group {
        flex-direction: column;
        gap: 10px;
    }

    .button-group button {
        width: 100%;
    }


    /* Estilo para el botón de cancelar */
    .cancel-btn {
        background-color: #f5f5f5;
        color: #333;
        border: 1px solid #ddd;
        padding: 10px 15px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        flex: 1;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .cancel-btn:hover {
        background-color: #e0e0e0;
    }
}

/* Posición de los botones de navegación en escritorio */
.navigation-buttons {
    position: absolute;
    top: 80px;
    /* Ajustado para que no quede oculto por el header */
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 1;
    /* Aumentado para asegurar que esté por encima de todo */
    opacity: 0.9;
    /* Más visible por defecto */
    transition: all 0.3s ease;
}

.navigation-buttons:hover {
    opacity: 1;
    transform: scale(1.1);
    /* Efecto de escala al pasar el cursor */
}

.nav-button {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
    font-size: 24px;
    border: 2px solid white;
    transition: all 0.3s ease;
    font-weight: bold;
}

.nav-button:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.4);
}

/* Ajustes específicos para temas en escritorio */
.theme-tron .nav-button {
    background-color: rgba(0, 136, 204, 0.9);
    box-shadow: 0 0 15px rgba(0, 136, 204, 0.7);
    border: 2px solid var(--accent-color);
    color: white;
    text-shadow: 0 0 5px white;
}

.theme-tron .nav-button:hover {
    background-color: rgba(0, 162, 255, 1);
    box-shadow: 0 0 20px rgba(0, 162, 255, 0.9);
    transform: translateY(-2px) scale(1.05);
}

.theme-neumorphic .nav-button {
    background-color: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-medium);
    border: 2px solid var(--surface-color);
}

.theme-neumorphic .nav-button:hover {
    box-shadow: var(--shadow-large);
    transform: translateY(-3px);
}

/* Ajustes para eventos táctiles */
@media (hover: none) and (pointer: coarse) {

    /* Aumentar áreas táctiles */
    button,
    .dropdown-menu button,
    .close-btn,
    .resize-handle {
        min-height: 44px;
        min-width: 44px;
    }

    /* Tamaño normal para checkboxes en pantallas táctiles */
    .checkbox-group input[type="checkbox"] {
        min-height: 20px;
        min-width: 20px;
    }

    /* Mejorar visibilidad del manejador de redimensionamiento */
    .widget .resize-handle {
        width: 30px;
        height: 30px;
    }

    .widget .resize-handle::before {
        width: 15px;
        height: 15px;
    }

    /* Estilo para widgets cuando están siendo tocados */
    .widget.touch-active {
        opacity: 0.8;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
        z-index: 1000;
    }

    /* Estilo para el widget durante el arrastre táctil */
    .widget.dragging {
        opacity: 0.7;
        z-index: 1001;
        transition: none !important;
        /* Desactivar transiciones durante el arrastre */
        touch-action: none !important;
        /* Prevenir gestos del navegador */
        -webkit-touch-callout: none !important;
        /* Prevenir menú contextual en iOS */
        -webkit-user-select: none !important;
        /* Prevenir selección de texto */
        user-select: none !important;
        /* Prevenir selección de texto */
    }

    /* Prevenir gestos del navegador en el dashboard en modo edición */
    .edit-mode {
        touch-action: none !important;
        -webkit-overflow-scrolling: auto !important;
    }

    /* Eliminar efectos hover que no funcionan en táctil */
    .dropdown-menu button:hover,
    button:hover {
        transform: none;
    }
}