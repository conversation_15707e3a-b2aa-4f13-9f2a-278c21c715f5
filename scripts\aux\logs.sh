#!/bin/bash

# Script para ver logs del servidor IPRA_I (Linux) - MOVIDO A AUX

# Obtener directorio del script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

# Cargar configuración
CONFIG_FILE="$SCRIPT_DIR/../config.conf"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "ERROR: Archivo config.conf no encontrado."
    exit 1
fi

source "$CONFIG_FILE"

SERVER_LOG="$PROJECT_ROOT/$LOG_DIR/server.log"

if [ ! -f "$SERVER_LOG" ]; then
    echo "Log del servidor no encontrado: $SERVER_LOG"
    exit 1
fi

echo "=== LOGS DEL SERVIDOR ==="
echo "Archivo: $SERVER_LOG"
echo

# Mostrar últimas 50 líneas
tail -50 "$SERVER_LOG"

echo
