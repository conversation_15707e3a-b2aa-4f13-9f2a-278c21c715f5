#!/bin/bash

# Script para detener el servidor IPRA_I (Linux)

set -e

# Obtener directorio del script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "=== DETENIENDO SERVIDOR IPRA_I ==="

# Cargar configuración
if [ ! -f "$SCRIPT_DIR/config.conf" ]; then
    echo "ERROR: Archivo scripts/config.conf no encontrado."
    exit 1
fi

source "$SCRIPT_DIR/config.conf"

echo "Puerto: $SERVER_PORT"
echo

# Buscar procesos en el puerto
echo "Buscando procesos en puerto $SERVER_PORT..."

FOUND=false

# Buscar por puerto usando netstat
if command -v netstat &> /dev/null; then
    PIDS=$(netstat -tlnp 2>/dev/null | grep ":$SERVER_PORT " | awk '{print $7}' | cut -d'/' -f1 | grep -v '-')
elif command -v ss &> /dev/null; then
    PIDS=$(ss -tlnp 2>/dev/null | grep ":$SERVER_PORT " | sed 's/.*pid=\([0-9]*\).*/\1/')
fi

if [ -n "$PIDS" ]; then
    for PID in $PIDS; do
        if [ -n "$PID" ] && [ "$PID" != "-" ]; then
            # Verificar si es un proceso Node.js
            if ps -p "$PID" -o comm= 2>/dev/null | grep -q "node"; then
                echo "Deteniendo proceso Node.js PID: $PID"
                kill -TERM "$PID" 2>/dev/null || true
                
                # Esperar un poco y forzar si es necesario
                sleep 2
                if kill -0 "$PID" 2>/dev/null; then
                    echo "Forzando detención del proceso $PID"
                    kill -KILL "$PID" 2>/dev/null || true
                fi
                FOUND=true
            fi
        fi
    done
fi

if [ "$FOUND" = false ]; then
    echo "No se encontraron procesos Node.js en puerto $SERVER_PORT"
else
    echo "Servidor detenido"
fi

# Limpiar archivos temporales
cd "$PROJECT_ROOT"
rm -f "$LOG_DIR/ipra-i.pid" 2>/dev/null || true
rm -f "$LOG_DIR"/*.lock 2>/dev/null || true

echo
