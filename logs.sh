#!/bin/bash

# Script para ver logs del servidor IPRA_I

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para logging
log() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARN] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

# Cargar configuración
if [ ! -f "config.conf" ]; then
    error "Archivo config.conf no encontrado. Ejecuta configure.sh primero."
fi

source config.conf

# Función para mostrar logs del servidor
show_server_logs() {
    local lines="${1:-50}"
    local follow="${2:-false}"
    
    local server_log="$LOG_DIR/server.log"
    
    if [ ! -f "$server_log" ]; then
        warn "Log del servidor no encontrado: $server_log"
        return 1
    fi
    
    log "Mostrando logs del servidor: $server_log"
    echo ""
    
    if [ "$follow" = true ]; then
        tail -f -n "$lines" "$server_log"
    else
        tail -n "$lines" "$server_log"
    fi
}

# Función para mostrar logs de systemd
show_systemd_logs() {
    local lines="${1:-50}"
    local follow="${2:-false}"
    
    if ! systemctl is-enabled ipra-i &>/dev/null; then
        warn "Servicio systemd no configurado"
        return 1
    fi
    
    log "Mostrando logs de systemd para ipra-i"
    echo ""
    
    if [ "$follow" = true ]; then
        journalctl -u ipra-i -f -n "$lines"
    else
        journalctl -u ipra-i --no-pager -n "$lines"
    fi
}

# Función para mostrar logs de error
show_error_logs() {
    local hours="${1:-24}"
    
    log "Mostrando errores de las últimas $hours horas"
    echo ""
    
    # Errores del servidor
    local server_log="$LOG_DIR/server.log"
    if [ -f "$server_log" ]; then
        echo -e "${BLUE}=== ERRORES DEL SERVIDOR ===${NC}"
        
        # Buscar errores recientes (aproximado por líneas)
        local total_lines=$(wc -l < "$server_log")
        local lines_per_hour=100  # Estimación
        local lines_to_check=$((hours * lines_per_hour))
        
        if [ "$lines_to_check" -gt "$total_lines" ]; then
            lines_to_check=$total_lines
        fi
        
        tail -n "$lines_to_check" "$server_log" | grep -i "error\|exception\|fail" || echo "No se encontraron errores"
        echo ""
    fi
    
    # Errores de systemd
    if systemctl is-enabled ipra-i &>/dev/null; then
        echo -e "${BLUE}=== ERRORES DE SYSTEMD ===${NC}"
        journalctl -u ipra-i --since "${hours} hours ago" --no-pager | grep -i "error\|fail\|exception" || echo "No se encontraron errores"
        echo ""
    fi
}

# Función para mostrar estadísticas de logs
show_log_stats() {
    log "Estadísticas de logs"
    echo ""
    
    # Estadísticas del servidor
    local server_log="$LOG_DIR/server.log"
    if [ -f "$server_log" ]; then
        echo -e "${BLUE}=== SERVIDOR ===${NC}"
        local size=$(du -h "$server_log" | cut -f1)
        local lines=$(wc -l < "$server_log")
        local errors=$(grep -c -i "error" "$server_log" 2>/dev/null || echo "0")
        local warnings=$(grep -c -i "warn" "$server_log" 2>/dev/null || echo "0")
        
        echo "Archivo: $server_log"
        echo "Tamaño: $size"
        echo "Líneas: $lines"
        echo "Errores: $errors"
        echo "Advertencias: $warnings"
        echo ""
    fi
    
    # Estadísticas de systemd
    if systemctl is-enabled ipra-i &>/dev/null; then
        echo -e "${BLUE}=== SYSTEMD ===${NC}"
        local systemd_lines=$(journalctl -u ipra-i --no-pager | wc -l)
        local systemd_errors=$(journalctl -u ipra-i --no-pager | grep -c -i "error" || echo "0")
        local systemd_warnings=$(journalctl -u ipra-i --no-pager | grep -c -i "warn" || echo "0")
        
        echo "Líneas totales: $systemd_lines"
        echo "Errores: $systemd_errors"
        echo "Advertencias: $systemd_warnings"
        echo ""
    fi
    
    # Otros logs
    if [ -d "$LOG_DIR" ]; then
        echo -e "${BLUE}=== DIRECTORIO DE LOGS ===${NC}"
        ls -lah "$LOG_DIR" 2>/dev/null || echo "Directorio vacío o no accesible"
        echo ""
    fi
}

# Función para limpiar logs
clean_logs() {
    local days="${1:-7}"
    
    log "Limpiando logs más antiguos de $days días"
    
    # Limpiar logs del servidor
    if [ -d "$LOG_DIR" ]; then
        find "$LOG_DIR" -name "*.log" -type f -mtime +$days -delete 2>/dev/null || true
        find "$LOG_DIR" -name "*.log.*" -type f -mtime +$days -delete 2>/dev/null || true
        log "Logs del directorio $LOG_DIR limpiados"
    fi
    
    # Limpiar logs de systemd (requiere permisos)
    if [ "$EUID" -eq 0 ] && systemctl is-enabled ipra-i &>/dev/null; then
        journalctl --vacuum-time=${days}d
        log "Logs de systemd limpiados"
    else
        warn "Se necesitan permisos de root para limpiar logs de systemd"
    fi
}

# Función para rotar logs
rotate_logs() {
    log "Rotando logs del servidor"
    
    local server_log="$LOG_DIR/server.log"
    
    if [ -f "$server_log" ]; then
        local timestamp=$(date +%Y%m%d_%H%M%S)
        local rotated_log="$LOG_DIR/server.log.$timestamp"
        
        mv "$server_log" "$rotated_log"
        touch "$server_log"
        
        # Comprimir log rotado
        if command -v gzip &> /dev/null; then
            gzip "$rotated_log"
            log "Log rotado y comprimido: $rotated_log.gz"
        else
            log "Log rotado: $rotated_log"
        fi
        
        # Reiniciar servidor si está ejecutándose para que use el nuevo archivo
        if pgrep -f "node.*server.js" > /dev/null; then
            warn "Considera reiniciar el servidor para usar el nuevo archivo de log"
        fi
    else
        warn "No se encontró log del servidor para rotar"
    fi
}

# Función para buscar en logs
search_logs() {
    local pattern="$1"
    local lines="${2:-10}"
    
    if [ -z "$pattern" ]; then
        error "Patrón de búsqueda requerido"
    fi
    
    log "Buscando '$pattern' en logs (últimas $lines coincidencias)"
    echo ""
    
    # Buscar en log del servidor
    local server_log="$LOG_DIR/server.log"
    if [ -f "$server_log" ]; then
        echo -e "${BLUE}=== SERVIDOR ===${NC}"
        grep -i "$pattern" "$server_log" | tail -n "$lines" || echo "No se encontraron coincidencias"
        echo ""
    fi
    
    # Buscar en systemd
    if systemctl is-enabled ipra-i &>/dev/null; then
        echo -e "${BLUE}=== SYSTEMD ===${NC}"
        journalctl -u ipra-i --no-pager | grep -i "$pattern" | tail -n "$lines" || echo "No se encontraron coincidencias"
        echo ""
    fi
}

# Función para mostrar ayuda
show_help() {
    echo "Uso: $0 [OPCIONES] [ARGUMENTOS]"
    echo ""
    echo "Opciones:"
    echo "  -h, --help              Mostrar esta ayuda"
    echo "  -s, --server [LÍNEAS]   Mostrar logs del servidor (por defecto: 50 líneas)"
    echo "  -y, --systemd [LÍNEAS]  Mostrar logs de systemd (por defecto: 50 líneas)"
    echo "  -f, --follow            Seguir logs en tiempo real (tail -f)"
    echo "  -e, --errors [HORAS]    Mostrar solo errores (por defecto: 24 horas)"
    echo "  -t, --stats             Mostrar estadísticas de logs"
    echo "  -c, --clean [DÍAS]      Limpiar logs antiguos (por defecto: 7 días)"
    echo "  -r, --rotate            Rotar logs del servidor"
    echo "  -g, --search PATRÓN [LÍNEAS]  Buscar patrón en logs"
    echo ""
    echo "Ejemplos:"
    echo "  $0                      # Mostrar logs del servidor (50 líneas)"
    echo "  $0 -s 100               # Mostrar 100 líneas del servidor"
    echo "  $0 -f                   # Seguir logs en tiempo real"
    echo "  $0 -e 12                # Errores de las últimas 12 horas"
    echo "  $0 -g 'websocket' 20    # Buscar 'websocket' (20 coincidencias)"
    echo "  $0 -c 3                 # Limpiar logs de más de 3 días"
    echo ""
}

# Función principal
main() {
    local follow=false
    local lines=50
    
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        -s|--server)
            lines="${2:-50}"
            show_server_logs "$lines" "$follow"
            ;;
        -y|--systemd)
            lines="${2:-50}"
            show_systemd_logs "$lines" "$follow"
            ;;
        -f|--follow)
            follow=true
            if [ -f "$LOG_DIR/server.log" ]; then
                show_server_logs "$lines" "$follow"
            else
                show_systemd_logs "$lines" "$follow"
            fi
            ;;
        -e|--errors)
            local hours="${2:-24}"
            show_error_logs "$hours"
            ;;
        -t|--stats)
            show_log_stats
            ;;
        -c|--clean)
            local days="${2:-7}"
            clean_logs "$days"
            ;;
        -r|--rotate)
            rotate_logs
            ;;
        -g|--search)
            if [ -z "$2" ]; then
                error "Patrón de búsqueda requerido para --search"
            fi
            local pattern="$2"
            local search_lines="${3:-10}"
            search_logs "$pattern" "$search_lines"
            ;;
        "")
            # Por defecto, mostrar logs del servidor
            show_server_logs "$lines" "$follow"
            ;;
        *)
            error "Opción desconocida: $1. Usa -h para ayuda."
            ;;
    esac
}

# Ejecutar función principal
main "$@"
