<!DOCTYPE html><html lang="es" class="theme-tron"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="mobile-web-app-capable" content="yes"><title>iPRA - Dashboard</title><link rel="icon" type="image/svg+xml" href="favicon.svg"><link rel="icon" type="image/x-icon" href="favicon.ico"><link rel="stylesheet" href="css/styles.css"><link rel="stylesheet" href="css/styles2.css"><link rel="stylesheet" href="css/accordion.css"><link rel="stylesheet" href="css/series_buttons.css"><link rel="stylesheet" href="css/themes/default.css"><link rel="stylesheet" href="css/themes/default2.css"><link rel="stylesheet" href="css/themes/tron.css"><link rel="stylesheet" href="css/themes/tron2.css"><link rel="stylesheet" href="css/themes/neumorphic.css"><link rel="stylesheet" href="css/themes/neumorphic2.css"><link rel="stylesheet" href="css/themes/azul_nuboso.css"><link rel="stylesheet" href="css/themes/azul_nubo2.css"><link rel="stylesheet" href="css/responsive.css"><link rel="stylesheet" href="css/widget_rules.css"></head><body><script> /** * Objeto global para almacenar todas las instancias de clases de la aplicación * Las clases se registrarán automáticamente en este objeto desde sus constructores * Ejemplo: En el constructor de WidgetManager: app['widgetManager'] = this; */ window.app = { acciones: {} }; </script><div id="login-container" class="container hidden" data-remove="N"><h1>iPRA</h1><div class="login-form"><h2>Iniciar sesión</h2><div class="form-group"><label for="username">Usuario:</label><input type="text" id="username" placeholder="Nombre de usuario"></div><div class="form-group"><label for="password">Contraseña:</label><input type="password" id="password" placeholder="Contraseña"></div><div class="form-group theme-selector"><label for="theme">Tema de la aplicación:</label><select id="theme"><option value="default">Menta Zen</option><option value="tron">Tron</option><option value="neumorphic">Neumórfico</option><option value="azul_nuboso">Azul Nuboso</option></select></div><button id="login-btn">Iniciar sesión</button><p id="login-error" class="error-message"></p></div></div><div id="main-menu-container" class="container hidden" data-remove="N"><header><div class="header-title-container"><h1>iPRA </h1></div><div class="menu-container"><button id="main-menu-logout-btn" class="logout-btn icon-button" aria-label="Cerrar sesión" title="Cerrar sesión" data-accion="authManager.logout"><span class="logout-icon">&#10005;</span></button></div></header><main><div class="main-menu-container"><div id="main-menu" class="main-menu"><!-- Los botones del menú se añadirán aquí dinámicamente --></div></div></main></div><div id="app-container" class="container hidden" data-remove="N"><header><div class="header-title-container"><h1 id="dashboard-title">iPRA Dashboard</h1><div class="dashboard-quick-actions"><button id="quick-back" class="icon-button" title="Volver atrás" data-accion="back"><span class="icon-back">&#8592;</span></button><button id="quick-switch-dashboard" class="icon-button" title="Cambiar tablero"><span class="icon-switch">&#8645;</span></button><button id="quick-new-dashboard" class="icon-button" title="Nuevo tablero"><span class="icon-new">&#43;</span></button><button id="quick-config-dashboard" class="icon-button" title="Configurar tablero"><span class="icon-config">&#9881;</span></button><button id="quick-add-widget" class="icon-button" title="Añadir widget"><span class="icon-add-widget">&#9733;</span></button></div></div><div id="dashboard-menu" class="menu-container"><button id="options-btn" aria-label="Menú de opciones">Opciones</button><button id="mobile-menu-btn" class="mobile-menu-btn" aria-label="Menú de opciones"><span class="hamburger-icon">&#9776;</span></button><div id="options-menu" class="dropdown-menu hidden"><button id="config-dashboard-btn">Configurar tablero</button><button id="add-widget-btn">Añadir widget</button><!-- Opciones comentadas según solicitud --><!-- <button id="edit-widget-btn">Editar widget</button> --><!-- <button id="delete-widget-btn">Borrar widget</button> --><!-- <button id="move-widget-btn">Mover widget</button> --><button id="selection-mode-btn" data-accion="dashboardSelectionManager.toggleWidgetSelection">Modo Selección</button><button id="copy-selecs-btn" data-accion="dashboardSelectionManager.copySelectedWidgets">Copiar Selecs.</button><button id="paste-widgets-btn" data-accion="dashboardSelectionManager.pasteWidgets">Pegar</button><button id="delete-selecs-btn" data-accion="dashboardSelectionManager.handleWidgetDeleteSelecsClick">Eliminar Selecs.</button><button id="switch-dashboard-btn">Cambio tablero</button><button id="main-menu-btn">Menú principal</button><button id="logout-btn" data-accion="authManager.logout">Cerrar sesión</button></div></div></header><main><div class="dashboard-container"><div id="dashboard" class="dashboard"><!-- Los widgets se añadirán aquí dinámicamente --></div></div></main><!-- Popup para cambio de tablero --><div id="switch-dashboard-popup" class="popup-menu hidden"><div class="popup-content"><h3>Cambio de Tablero</h3><div id="dashboard-list" class="dashboard-list"><!-- Los tableros se añadirán aquí dinámicamente --></div><div class="button-group"><button id="accept-dashboard-switch" class="primary-btn">Aceptar</button><button id="cancel-dashboard-switch" class="secondary-btn">Cancelar</button></div></div></div><!-- Modal para añadir widget --><div id="add-widget-modal" class="modal hidden"><div class="modal-content"><span class="close-btn">&times;</span><h2>Añadir Widget</h2><div class="form-group"><label for="widget-type">Tipo de widget:</label><select id="widget-type"><option value="text">Etiqueta de texto</option><option value="value">Etiqueta de valor</option><option value="gauge">Gauge</option><option value="percentage-gauge">Gauge porcentual</option><option value="chart">Gráfica</option><option value="latest">Últimos</option><option value="period-chart">Gráfica Por Períodos</option><option value="latest-by-periods">Últimos por Períodos</option></select></div><!-- El nombre se genera automáticamente --><!-- Los campos específicos para cada tipo de widget ahora se generan dinámicamente en el acordeón --><div id="widget-fields-container" class="widget-fields-container"><!-- Aquí se generarán dinámicamente los campos específicos para cada tipo de widget --></div><div class="form-group"><label for="widget-width">Ancho (px):</label><input type="number" id="widget-width" min="50" max="800" value="200"></div><div class="form-group"><label for="widget-height">Alto (px):</label><input type="number" id="widget-height" min="50" max="600" value="150"></div><button id="save-widget">Guardar</button></div></div><!-- Modal para editar widget --><div id="edit-widget-modal" class="modal hidden"><div class="modal-content"><span class="close-btn">&times;</span><h2>Editar Widget</h2><!-- Nombre del widget oculto (se mantiene el input para no modificar la lógica JS) --><div class="form-group hidden"><label for="edit-widget-name">Nombre:</label><input type="text" id="edit-widget-name"></div><!-- Cabecera con etiqueta y botones de acción --><div class="form-group widget-header-row"><label for="edit-widget-type" class="widget-type-label">Tipo Widget:</label><div class="widget-actions-container"><button id="save-edited-widget" class="icon-action-btn primary-icon-btn" title="Guardar"><span class="action-icon">&#10004;</span></button><button id="duplicate-widget" class="icon-action-btn secondary-icon-btn" title="Duplicar"><span class="action-icon">&#128459;</span></button><button id="delete-widget-from-modal-btn" class="icon-action-btn danger-icon-btn" title="Eliminar"><span class="action-icon">&#128465;</span></button></div></div><!-- Selector de tipo de widget en fila separada --><div class="form-group widget-type-row"><select id="edit-widget-type" class="widget-type-select edit-widget-type"><option value="text">Etiqueta de texto</option><option value="value">Etiqueta de valor</option><option value="gauge">Gauge</option><option value="percentage-gauge">Gauge porcentual</option><option value="chart">Gráfica</option><option value="latest">Últimos</option><option value="period-chart">Gráfica Por Períodos</option><option value="latest-by-periods">Últimos por Períodos</option></select></div><!-- Los campos específicos para cada tipo de widget ahora se generan dinámicamente en el acordeón --><div id="widget-fields-container" class="widget-fields-container"><!-- Aquí se generarán dinámicamente los campos específicos para cada tipo de widget --></div><!-- Personalización de colores <div class="accordion-section"><input type="checkbox" class="accordion-toggle" id="accordion-color" checked="false"><label class="accordion-header" for="accordion-color" data-zone-id="color"><i class="material-icons zone-icon">⚈</i><span class="title">Color</span><i class="material-icons toggle-icon"></i></label><div class="accordion-content" data-zone-id="color"><div class="form-group"><label for="edit-widget-bg-color">Color de fondo:</label><div class="color-picker-container"><input type="color" id="edit-widget-bg-color"><button id="reset-widget-bg-color" class="reset-color-btn">Defecto</button></div></div><div class="form-group"><label for="edit-widget-text-color">Color de texto:</label><div class="color-picker-container"><input type="color" id="edit-widget-text-color"><button id="reset-widget-text-color" class="reset-color-btn">Defecto</button></div></div><div class="form-group"><label for="edit-widget-border-color">Color de borde:</label><div class="color-picker-container"><input type="color" id="edit-widget-border-color"><button id="reset-widget-border-color" class="reset-color-btn">Defecto</button></div></div></div></div> --><!-- Los botones se han movido a la parte superior junto al selector de tipo de widget --></div></div></div><!-- Contenedor para la gestión de entidades --><div id="entity-management-container" class="container hidden entity-management-container"><header><div class="header-title-container"><h1 id="entity-title"><span class="tit_ico_atras" data-accion="back">←</span><span id="titulo">Gestión de Entidades</span></h1><span id="entity-count" class="entity-count"></span></div><div class="menu-container"><button id="entity-back-btn" data-accion="inicio" class="logout-btn icon-button" aria-label="Volver a pantalla anterior" title="Volver a pantalla principal"><span class="logout-icon">&#10005;</span></button><button id="entity-mobile-menu-btn" class="mobile-menu-btn" aria-label="Menú de opciones"><span class="hamburger-icon">&#9776;</span></button><div id="entity-options-menu" class="dropdown-menu hidden"><button id="entity-add-btn" data-accion="showEntityForm">Añadir</button><button id="entity-back-menu-btn">Volver al menú</button></div></div></header><main><div class="entity-container"><div class="entity-filter-container"><input type="text" id="entity-filter" class="entity-filter" placeholder="Filtrar..."><button id="entity-clear-filter" class="clear-filter-btn hidden">&times;</button></div><div id="entity-table-container" class="entity-table-container"><!-- La tabla se generará dinámicamente aquí --></div><!-- Botones de navegación para la tabla --><div class="navigation-buttons hidden"><button class="nav-button scroll-top-btn" title="Ir al inicio">↑</button><button class="nav-button scroll-bottom-btn" title="Ir al final">↓</button></div></div></main></div><!-- Modal para editar/añadir entidad --><div id="entity-edit-modal" class="modal hidden"><div class="modal-content"><span class="close-btn" data-accion="closeEntityForm">&times;</span><h2 id="entity-edit-title">Editar Entidad</h2><div id="entity-form-container"><!-- El formulario se generará dinámicamente aquí --></div><div class="button-group"><button id="entity-save-btn" class="primary-btn" data-accion="saveEntity">Guardar</button><button id="entity-cancel-btn" class="secondary-btn" data-accion="closeEntityForm">Cancelar</button></div></div></div><!-- Los botones de navegación se crearán dinámicamente en cada contenedor de entidades --><!-- Modal para pedir nombre de tablero --><div id="dashboard-ask-name" class="modal hidden" data-remove="N"><div class="modal-content"><span class="close-btn">&times;</span><h2>Nombre del Tablero</h2><div class="form-group"><label for="new-dashboard-name">Nombre del tablero:</label><input type="text" id="new-dashboard-name" placeholder="Introduce un nombre para el tablero"><p id="new-dashboard-error" class="error-message"></p></div><div class="button-group"><button id="create-dashboard-btn" class="primary-btn">Aceptar</button></div></div></div><!-- Modal para configurar tablero --><div id="dashboard-config-modal" class="modal hidden" data-remove="N"><div class="modal-content"><div class="modal-header"><h2>Configurar Tablero</h2><span class="close-btn">&times;</span></div><div class="form-group"><label for="dashboard-name">Nombre:</label><input type="text" id="dashboard-name"></div><div class="form-group"><label for="dashboard-width">Ancho (px):</label><input type="number" id="dashboard-width" min="400" max="1920"></div><div class="form-group"><label for="dashboard-height">Alto (px):</label><input type="number" id="dashboard-height" min="300" max="1080"></div><div class="form-group"><label for="dashboard-bg-color">Color fondo:</label><input type="color" id="dashboard-bg-color"></div><div class="form-group"><label for="dashboard-grid-color">Cuadrícula:</label><input type="color" id="dashboard-grid-color"></div><div class="form-group"><label for="dashboard-widget-text-color">Texto Widgets:</label><input type="color" id="dashboard-widget-text-color"></div><div class="form-group"><label for="dashboard-widget-bg-color">Fondo Widgets:</label><input type="color" id="dashboard-widget-bg-color"></div><div class="checkbox-group"><input type="checkbox" id="dashboard-show-borders"><label for="dashboard-show-borders">Sí, poner borde a los widgets</label></div><div class="checkbox-group"><input type="checkbox" id="dashboard-transparent-widgets"><label for="dashboard-transparent-widgets">Sí, hacer widgets con fondo transparente</label></div><div class="checkbox-group"><input type="checkbox" id="dashboard-show-grid"><label for="dashboard-show-grid">Sí, mostrar cuadrícula en el tablero</label></div><div class="button-group dashboard-config-buttons"><button id="save-dashboard-config" class="primary-btn" title="Guardar"><span class="btn-text">Guardar</span><span class="btn-icon">&#10004;</span></button><button id="dashboard-options-btn" class="secondary-btn" title="Opciones"><span class="btn-text">Opciones</span><span class="btn-icon">&#9881;</span></button><button id="cancel-dashboard-config" class="cancel-btn" title="Cancelar"><span class="btn-text">Cancelar</span><span class="btn-icon">&#10005;</span></button></div></div></div><!-- Popup para opciones de tablero --><div id="dashboard-options-popup" class="popup-menu hidden"><div class="popup-content"><div class="popup-header"><h3>Opciones</h3><span id="close-dashboard-options" class="close-btn">&times;</span></div><button id="new-dashboard-btn" class="popup-option">Nuevo tablero</button><button id="duplicate-dashboard-btn" class="popup-option">Duplicar tablero</button><button id="clear-dashboard-btn" class="popup-option danger-option">Vaciar tablero</button><button id="delete-dashboard-btn" class="popup-option danger-option">Eliminar tablero</button><div class="button-group"><button id="cancel-dashboard-options" class="secondary-btn">Cancelar</button></div></div></div><script src="chart.min.js" defer></script><script src="js/tableros/dashboard_loader.js"></script><script src="js/data.js"></script><script src="js/database_innerdb.js"></script><script src="js/temas/themes.js"></script><script src="js/auth.js"></script><script src="js/app.js"></script><script src="js/dialogs.js"></script><script src="js/entity_manager.js"></script><script src="js/entidades/usuarios.js"></script><script src="js/user_profile.js"></script><script src="js/entidades/companies.js"></script><script src="js/entidades/tableros.js"></script><script src="js/main_menu.js"></script><!-- Scripts de datos de prueba eliminados - ahora se usan los de database_innerdb.js --><script src="js/hash_navigation.js"></script><script src="js/app2.js"></script><script src="js/temas/menu_border.js"></script><script src="js/mobile.js"></script><script src="js/navigation.js"></script><script src="js/tableros/color-picker-handlers.js"></script><script> // Iniciar la carga de scripts cuando el DOM esté listo document.addEventListener('DOMContentLoaded', function () { initDashboardLoader(function () { console.log('Dashboard inicializado y listo para usar'); initApp(); }); }); </script></body></html>