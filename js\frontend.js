/**
 * Cliente WebSocket para colaboración en tiempo real
 */
class CollaborationClient {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10;
        this.reconnectDelay = 1000; // Inicial: 1 segundo
        this.maxReconnectDelay = 30000; // Máximo: 30 segundos
        this.currentDashboardId = null;
        this.currentUserId = null;
        this.currentUserName = null;
        this.messageQueue = []; // Cola para mensajes durante desconexión
        this.connectedUsers = new Map(); // connectionId -> userData
        
        // Configuración desde config.conf (se cargará dinámicamente)
        this.config = {
            serverHost: 'localhost',
            serverPort: 3000,
            reconnectInitial: 1000,
            reconnectMax: 30000
        };
        
        // Cargar configuración
        this.loadConfig();
        
        // Bind methods
        this.connect = this.connect.bind(this);
        this.disconnect = this.disconnect.bind(this);
        this.handleMessage = this.handleMessage.bind(this);
        this.handleClose = this.handleClose.bind(this);
        this.handleError = this.handleError.bind(this);
        
        // Registrar en app global
        if (typeof app !== 'undefined') {
            app.collaborationClient = this;
        }
    }
    
    async loadConfig() {
        try {
            // En un entorno real, esto se cargaría del servidor o de variables de configuración
            // Por ahora usamos valores por defecto que pueden ser sobrescritos
            const response = await fetch('/config.json').catch(() => null);
            if (response && response.ok) {
                const config = await response.json();
                Object.assign(this.config, config);
            }
        } catch (error) {
            console.warn('No se pudo cargar configuración, usando valores por defecto');
        }
    }
    
    connect(dashboardId, userId, userName) {
        if (this.isConnected && this.currentDashboardId === dashboardId) {
            console.log('Ya conectado al dashboard', dashboardId);
            return Promise.resolve();
        }
        
        this.currentDashboardId = dashboardId;
        this.currentUserId = userId;
        this.currentUserName = userName;
        
        return new Promise((resolve, reject) => {
            try {
                const wsUrl = `ws://${this.config.serverHost}:${this.config.serverPort}`;
                console.log('Conectando a WebSocket:', wsUrl);
                
                this.ws = new WebSocket(wsUrl);
                
                this.ws.onopen = () => {
                    console.log('WebSocket conectado');
                    this.isConnected = true;
                    this.reconnectAttempts = 0;
                    this.reconnectDelay = this.config.reconnectInitial;
                    
                    // Unirse al dashboard
                    this.sendMessage({
                        type: 'join_dashboard',
                        payload: {
                            dashboardId: this.currentDashboardId,
                            userId: this.currentUserId,
                            userName: this.currentUserName
                        }
                    });
                    
                    // Procesar cola de mensajes pendientes
                    this.processMessageQueue();
                    
                    // Mostrar indicador de conexión
                    this.showConnectionStatus('connected');
                    
                    resolve();
                };
                
                this.ws.onmessage = this.handleMessage;
                this.ws.onclose = this.handleClose;
                this.ws.onerror = this.handleError;
                
                // Timeout para conexión
                setTimeout(() => {
                    if (!this.isConnected) {
                        reject(new Error('Timeout de conexión WebSocket'));
                    }
                }, 10000);
                
            } catch (error) {
                console.error('Error al conectar WebSocket:', error);
                reject(error);
            }
        });
    }
    
    disconnect() {
        if (this.ws) {
            // Enviar mensaje de salida del dashboard
            if (this.isConnected && this.currentDashboardId) {
                this.sendMessage({
                    type: 'leave_dashboard',
                    payload: { dashboardId: this.currentDashboardId }
                });
            }
            
            this.ws.close();
            this.ws = null;
        }
        
        this.isConnected = false;
        this.currentDashboardId = null;
        this.currentUserId = null;
        this.currentUserName = null;
        this.connectedUsers.clear();
        
        this.showConnectionStatus('disconnected');
    }
    
    handleMessage(event) {
        try {
            const message = JSON.parse(event.data);
            const { type, payload } = message;
            
            console.log('Mensaje recibido:', type, payload);
            
            switch (type) {
                case 'dashboard_joined':
                    this.handleDashboardJoined(payload);
                    break;
                    
                case 'user_joined':
                    this.handleUserJoined(payload);
                    break;
                    
                case 'user_left':
                    this.handleUserLeft(payload);
                    break;
                    
                case 'widget_updated':
                    this.handleWidgetUpdated(payload);
                    break;
                    
                case 'widget_created':
                    this.handleWidgetCreated(payload);
                    break;
                    
                case 'widget_deleted':
                    this.handleWidgetDeleted(payload);
                    break;
                    
                case 'error':
                    console.error('Error del servidor:', payload.message);
                    this.showNotification('Error: ' + payload.message, 'error');
                    break;
                    
                case 'pong':
                    // Respuesta a ping, no hacer nada
                    break;
                    
                default:
                    console.warn('Tipo de mensaje desconocido:', type);
            }
        } catch (error) {
            console.error('Error al procesar mensaje WebSocket:', error);
        }
    }
    
    handleClose(event) {
        console.log('WebSocket cerrado:', event.code, event.reason);
        this.isConnected = false;
        this.showConnectionStatus('disconnected');
        
        // Intentar reconexión automática si no fue cierre intencional
        if (event.code !== 1000 && this.currentDashboardId) {
            this.scheduleReconnect();
        }
    }
    
    handleError(error) {
        console.error('Error WebSocket:', error);
        this.showConnectionStatus('error');
    }
    
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Máximo número de intentos de reconexión alcanzado');
            this.showNotification('No se pudo reconectar al servidor', 'error');
            return;
        }
        
        this.reconnectAttempts++;
        console.log(`Reintentando conexión en ${this.reconnectDelay}ms (intento ${this.reconnectAttempts})`);
        
        this.showConnectionStatus('reconnecting');
        
        setTimeout(() => {
            if (!this.isConnected && this.currentDashboardId) {
                this.connect(this.currentDashboardId, this.currentUserId, this.currentUserName)
                    .catch(error => {
                        console.error('Error en reconexión:', error);
                        // Incrementar delay con backoff exponencial
                        this.reconnectDelay = Math.min(this.reconnectDelay * 2, this.maxReconnectDelay);
                        this.scheduleReconnect();
                    });
            }
        }, this.reconnectDelay);
    }
    
    sendMessage(message) {
        if (this.isConnected && this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
            return true;
        } else {
            // Agregar a cola para enviar cuando se reconecte
            this.messageQueue.push(message);
            console.warn('Mensaje agregado a cola (desconectado):', message.type);
            return false;
        }
    }
    
    processMessageQueue() {
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            this.sendMessage(message);
        }
    }
    
    // === MÉTODOS PARA ENVIAR EVENTOS ===
    
    sendWidgetUpdate(widgetId, changes) {
        return this.sendMessage({
            type: 'widget_update',
            payload: {
                widgetId,
                changes,
                timestamp: Date.now()
            }
        });
    }
    
    sendWidgetCreate(widget) {
        return this.sendMessage({
            type: 'widget_create',
            payload: {
                widget,
                timestamp: Date.now()
            }
        });
    }
    
    sendWidgetDelete(widgetId) {
        return this.sendMessage({
            type: 'widget_delete',
            payload: {
                widgetId,
                timestamp: Date.now()
            }
        });
    }
    
    // === MANEJADORES DE EVENTOS RECIBIDOS ===
    
    handleDashboardJoined(payload) {
        const { dashboardId, users } = payload;
        console.log(`Unido al dashboard ${dashboardId} con ${users.length} usuarios`);
        
        // Actualizar lista de usuarios conectados
        this.connectedUsers.clear();
        users.forEach(user => {
            this.connectedUsers.set(user.connectionId, user);
        });
        
        this.updateUserIndicators();
        this.showNotification(`Conectado al dashboard (${users.length} usuarios)`, 'success');
    }
    
    handleUserJoined(payload) {
        const { userId, userName, connectionId } = payload;
        console.log(`Usuario ${userName} se unió al dashboard`);
        
        this.connectedUsers.set(connectionId, { userId, userName, connectionId });
        this.updateUserIndicators();
        this.showNotification(`${userName} se unió al dashboard`, 'info');
    }
    
    handleUserLeft(payload) {
        const { userName, connectionId } = payload;
        console.log(`Usuario ${userName} dejó el dashboard`);
        
        this.connectedUsers.delete(connectionId);
        this.updateUserIndicators();
        this.showNotification(`${userName} dejó el dashboard`, 'info');
    }
    
    handleWidgetUpdated(payload) {
        const { widgetId, changes, userId, userName } = payload;
        
        // No procesar nuestros propios cambios
        if (userId === this.currentUserId) return;
        
        console.log(`Widget ${widgetId} actualizado por ${userName}:`, changes);
        
        // Aplicar cambios al widget
        if (typeof widgetManager !== 'undefined') {
            widgetManager.applyRemoteWidgetUpdate(widgetId, changes);
        }
        
        this.showNotification(`${userName} modificó un widget`, 'info');
    }
    
    handleWidgetCreated(payload) {
        const { widget, userId, userName } = payload;
        
        // No procesar nuestros propios cambios
        if (userId === this.currentUserId) return;
        
        console.log(`Widget creado por ${userName}:`, widget);
        
        // Agregar widget al dashboard
        if (typeof widgetManager !== 'undefined') {
            widgetManager.applyRemoteWidgetCreate(widget);
        }
        
        this.showNotification(`${userName} creó un widget`, 'info');
    }
    
    handleWidgetDeleted(payload) {
        const { widgetId, userId, userName } = payload;
        
        // No procesar nuestros propios cambios
        if (userId === this.currentUserId) return;
        
        console.log(`Widget ${widgetId} eliminado por ${userName}`);
        
        // Eliminar widget del dashboard
        if (typeof widgetManager !== 'undefined') {
            widgetManager.applyRemoteWidgetDelete(widgetId);
        }
        
        this.showNotification(`${userName} eliminó un widget`, 'info');
    }
    
    // === MÉTODOS DE UI ===
    
    showConnectionStatus(status) {
        // Crear o actualizar indicador de estado de conexión
        let indicator = document.getElementById('collaboration-status');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'collaboration-status';
            indicator.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                z-index: 10000;
                transition: all 0.3s ease;
            `;
            document.body.appendChild(indicator);
        }
        
        const statusConfig = {
            connected: { text: '🟢 Conectado', color: '#4CAF50', bg: '#E8F5E8' },
            disconnected: { text: '🔴 Desconectado', color: '#F44336', bg: '#FFEBEE' },
            reconnecting: { text: '🟡 Reconectando...', color: '#FF9800', bg: '#FFF3E0' },
            error: { text: '❌ Error', color: '#F44336', bg: '#FFEBEE' }
        };
        
        const config = statusConfig[status] || statusConfig.disconnected;
        indicator.textContent = config.text;
        indicator.style.color = config.color;
        indicator.style.backgroundColor = config.bg;
        indicator.style.border = `1px solid ${config.color}`;
    }
    
    updateUserIndicators() {
        // Mostrar lista de usuarios conectados
        let userList = document.getElementById('collaboration-users');
        if (!userList) {
            userList = document.createElement('div');
            userList.id = 'collaboration-users';
            userList.style.cssText = `
                position: fixed;
                top: 50px;
                right: 10px;
                background: white;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px;
                font-size: 12px;
                max-width: 200px;
                z-index: 9999;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            `;
            document.body.appendChild(userList);
        }
        
        const users = Array.from(this.connectedUsers.values());
        if (users.length === 0) {
            userList.style.display = 'none';
        } else {
            userList.style.display = 'block';
            userList.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 4px;">
                    Usuarios conectados (${users.length}):
                </div>
                ${users.map(user => `
                    <div style="margin: 2px 0;">
                        👤 ${user.userName}
                    </div>
                `).join('')}
            `;
        }
    }
    
    showNotification(message, type = 'info') {
        // Crear notificación temporal
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            right: 10px;
            padding: 12px 16px;
            border-radius: 4px;
            font-size: 14px;
            max-width: 300px;
            z-index: 10001;
            animation: slideIn 0.3s ease;
        `;
        
        const typeConfig = {
            success: { bg: '#E8F5E8', color: '#2E7D32', border: '#4CAF50' },
            error: { bg: '#FFEBEE', color: '#C62828', border: '#F44336' },
            info: { bg: '#E3F2FD', color: '#1565C0', border: '#2196F3' },
            warning: { bg: '#FFF3E0', color: '#EF6C00', border: '#FF9800' }
        };
        
        const config = typeConfig[type] || typeConfig.info;
        notification.style.backgroundColor = config.bg;
        notification.style.color = config.color;
        notification.style.border = `1px solid ${config.border}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Remover después de 3 segundos
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }, 3000);
    }
    
    // === MÉTODOS PÚBLICOS PARA INTEGRACIÓN ===
    
    isConnectedToDashboard(dashboardId) {
        return this.isConnected && this.currentDashboardId === dashboardId;
    }
    
    getConnectedUsers() {
        return Array.from(this.connectedUsers.values());
    }
    
    getCurrentDashboardId() {
        return this.currentDashboardId;
    }
}

// Agregar estilos CSS para animaciones
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Crear instancia global
if (typeof window !== 'undefined') {
    window.collaborationClient = new CollaborationClient();
}
