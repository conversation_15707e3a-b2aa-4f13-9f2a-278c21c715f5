#!/bin/bash

# Script de instalación para IPRA_I
# Instala Node.js, dependencias, configura firewall y servicios

set -e  # Salir en caso de error

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Cargar configuración
if [ ! -f "config.conf" ]; then
    error "Archivo config.conf no encontrado. Ejecuta configure.sh primero."
fi

source config.conf

log "=== INSTALACIÓN IPRA_I ==="
log "Puerto configurado: $SERVER_PORT"
log "Configuración regional: $LC_NUMERIC"

# Verificar si se ejecuta como root para configuración de firewall
if [ "$EUID" -ne 0 ]; then
    warn "No se ejecuta como root. Algunas configuraciones de sistema pueden fallar."
    warn "Para configurar firewall y servicios del sistema, ejecuta: sudo ./install.sh"
fi

# Detectar sistema operativo
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VER=$VERSION_ID
else
    error "No se pudo detectar el sistema operativo"
fi

log "Sistema detectado: $OS $VER"

# Función para instalar Node.js
install_nodejs() {
    log "Verificando instalación de Node.js..."
    
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        log "Node.js ya instalado: $NODE_VERSION"
        
        # Verificar versión mínima (v16+)
        NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$NODE_MAJOR" -lt 16 ]; then
            warn "Node.js versión $NODE_VERSION es muy antigua. Se recomienda v16+"
        fi
    else
        log "Instalando Node.js..."
        
        if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
            curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
            sudo apt-get install -y nodejs
        elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]] || [[ "$OS" == *"Fedora"* ]]; then
            curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash -
            sudo dnf install -y nodejs npm
        else
            error "Sistema operativo no soportado para instalación automática de Node.js"
        fi
        
        log "Node.js instalado: $(node --version)"
    fi
    
    # Verificar npm
    if command -v npm &> /dev/null; then
        log "npm disponible: $(npm --version)"
    else
        error "npm no encontrado después de instalar Node.js"
    fi
}

# Función para instalar dependencias Python (para WeasyPrint)
install_python_deps() {
    log "Instalando dependencias para WeasyPrint..."
    
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        sudo apt-get update
        sudo apt-get install -y python3 python3-pip python3-venv
        sudo apt-get install -y libpango-1.0-0 libharfbuzz0b libpangoft2-1.0-0
        sudo apt-get install -y libffi-dev libcairo2 libcairo2-dev
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]] || [[ "$OS" == *"Fedora"* ]]; then
        sudo dnf install -y python3 python3-pip
        sudo dnf install -y pango harfbuzz cairo cairo-devel
        sudo dnf install -y libffi-devel
    else
        warn "Sistema no reconocido para instalación automática de dependencias Python"
    fi
    
    # Crear entorno virtual para WeasyPrint
    if [ ! -d "venv" ]; then
        log "Creando entorno virtual Python..."
        python3 -m venv venv
    fi
    
    log "Instalando WeasyPrint en entorno virtual..."
    source venv/bin/activate
    pip install --upgrade pip
    pip install weasyprint
    deactivate
    
    log "WeasyPrint instalado correctamente"
}

# Función para instalar dependencias del proyecto
install_project_deps() {
    log "Instalando dependencias del frontend..."
    npm install
    
    log "Instalando dependencias del backend..."
    cd backend
    npm install
    cd ..
    
    log "Dependencias instaladas correctamente"
}

# Función para configurar firewall
configure_firewall() {
    if [ "$EUID" -ne 0 ]; then
        warn "Se necesitan permisos de root para configurar firewall"
        return
    fi
    
    log "Configurando firewall para puerto $SERVER_PORT..."
    
    # Detectar tipo de firewall
    if command -v ufw &> /dev/null; then
        log "Usando UFW..."
        ufw allow $SERVER_PORT/tcp
        log "Puerto $SERVER_PORT abierto en UFW"
    elif command -v firewall-cmd &> /dev/null; then
        log "Usando firewalld..."
        firewall-cmd --permanent --add-port=$SERVER_PORT/tcp
        firewall-cmd --reload
        log "Puerto $SERVER_PORT abierto en firewalld"
    elif command -v iptables &> /dev/null; then
        log "Usando iptables..."
        iptables -A INPUT -p tcp --dport $SERVER_PORT -j ACCEPT
        # Intentar guardar reglas (varía según distribución)
        if command -v iptables-save &> /dev/null; then
            iptables-save > /etc/iptables/rules.v4 2>/dev/null || true
        fi
        log "Puerto $SERVER_PORT abierto en iptables"
    else
        warn "No se detectó sistema de firewall conocido"
        warn "Configura manualmente el puerto $SERVER_PORT en tu firewall"
    fi
}

# Función para crear servicio systemd
create_systemd_service() {
    if [ "$EUID" -ne 0 ]; then
        warn "Se necesitan permisos de root para crear servicio systemd"
        return
    fi
    
    log "Creando servicio systemd..."
    
    CURRENT_DIR=$(pwd)
    CURRENT_USER=$(logname 2>/dev/null || echo $SUDO_USER)
    
    cat > /etc/systemd/system/ipra-i.service << EOF
[Unit]
Description=IPRA_I Backend Server
After=network.target

[Service]
Type=simple
User=$CURRENT_USER
WorkingDirectory=$CURRENT_DIR/backend
Environment=NODE_ENV=production
ExecStart=/usr/bin/node server.js
Restart=always
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=ipra-i

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    systemctl enable ipra-i
    
    log "Servicio systemd creado y habilitado"
    log "Usa 'sudo systemctl start ipra-i' para iniciar el servicio"
}

# Función para crear directorios necesarios
create_directories() {
    log "Creando directorios necesarios..."
    
    mkdir -p "$BUILD_DIR"
    mkdir -p "$DOCS_DIR"
    mkdir -p "$LOG_DIR"
    mkdir -p "backend/logs"
    
    log "Directorios creados: $BUILD_DIR, $DOCS_DIR, $LOG_DIR"
}

# Función para configurar permisos
setup_permissions() {
    log "Configurando permisos..."
    
    # Hacer scripts ejecutables
    chmod +x *.sh
    
    # Configurar permisos de directorios
    chmod 755 "$BUILD_DIR" "$DOCS_DIR" "$LOG_DIR" 2>/dev/null || true
    
    log "Permisos configurados"
}

# Función para build inicial
initial_build() {
    log "Realizando build inicial..."
    
    npm run build
    
    log "Build inicial completado"
}

# Función para verificar instalación
verify_installation() {
    log "Verificando instalación..."
    
    # Verificar Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js no está instalado correctamente"
    fi
    
    # Verificar dependencias
    if [ ! -d "node_modules" ]; then
        error "Dependencias del frontend no instaladas"
    fi
    
    if [ ! -d "backend/node_modules" ]; then
        error "Dependencias del backend no instaladas"
    fi
    
    # Verificar build
    if [ ! -f "$BUILD_DIR/$COMBINED_JS_FILE" ]; then
        error "Build de JavaScript no encontrado"
    fi
    
    if [ ! -f "$BUILD_DIR/$COMBINED_CSS_FILE" ]; then
        error "Build de CSS no encontrado"
    fi
    
    # Verificar WeasyPrint
    if [ -d "venv" ]; then
        source venv/bin/activate
        if ! python -c "import weasyprint" 2>/dev/null; then
            warn "WeasyPrint no está disponible"
        else
            log "WeasyPrint verificado correctamente"
        fi
        deactivate
    fi
    
    log "Verificación completada exitosamente"
}

# Función principal
main() {
    log "Iniciando instalación..."
    
    # Verificar configuración
    if [ -z "$SERVER_PORT" ]; then
        error "SERVER_PORT no configurado en config.conf"
    fi
    
    # Instalar componentes
    install_nodejs
    install_python_deps
    create_directories
    install_project_deps
    setup_permissions
    configure_firewall
    create_systemd_service
    initial_build
    verify_installation
    
    log "=== INSTALACIÓN COMPLETADA ==="
    log ""
    log "Próximos pasos:"
    log "1. Iniciar servidor: ./start.sh"
    log "2. Verificar estado: ./status.sh"
    log "3. Ver logs: ./logs.sh"
    log "4. Parar servidor: ./stop.sh"
    log ""
    log "El servidor estará disponible en: http://localhost:$SERVER_PORT"
    log ""
    log "Para documentación: ./generate_docs.sh"
    log "Para rebuild: npm run build"
}

# Ejecutar función principal
main "$@"
