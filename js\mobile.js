/**
 * Funcionalidades específicas para dispositivos móviles
 */
document.addEventListener('DOMContentLoaded', () => {
    initMobileFeatures();
});

/**
 * Inicializa las características específicas para móviles
 */
function initMobileFeatures() {
    // Inicializar eventos táctiles para widgets
    initTouchEvents();
}

/**
 * Inicializa los botones de navegación para un contenedor específico
 * @param {HTMLElement} containerElement - El contenedor de entidades
 */
function initNavigationButtons(containerElement) {
    // Buscar los botones de navegación dentro del contenedor
    const navButtons = containerElement.querySelector('.navigation-buttons');
    if (!navButtons) return;

    // Mostrar los botones
    navButtons.classList.remove('hidden');

    // Buscar los botones específicos por clase en lugar de por ID
    const scrollTopBtn = navButtons.querySelector('.scroll-top-btn');
    const scrollBottomBtn = navButtons.querySelector('.scroll-bottom-btn');

    // Buscar el contenedor de la tabla dentro del contenedor de entidades
    const tableContainer = containerElement.querySelector('.entity-table-container');
    if (!tableContainer) return;

    // Añadir listeners a los botones directamente
    if (scrollTopBtn) {
        // Añadir listener para ir al inicio
        scrollTopBtn.addEventListener('click', () => {
            tableContainer.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    if (scrollBottomBtn) {
        // Añadir listener para ir al final
        scrollBottomBtn.addEventListener('click', () => {
            tableContainer.scrollTo({
                top: tableContainer.scrollHeight,
                behavior: 'smooth'
            });
        });
    }

    // Actualizar la visibilidad y opacidad de los botones según el scroll
    function updateButtonsOpacity() {
        if (!tableContainer || !scrollTopBtn || !scrollBottomBtn) return;

        const scrollTop = tableContainer.scrollTop;
        const maxScroll = tableContainer.scrollHeight - tableContainer.clientHeight;

        // Calcular porcentaje de scroll
        const scrollPercentage = maxScroll > 0 ? (scrollTop / maxScroll) * 100 : 0;

        // Ajustar opacidad según la posición de scroll
        // Botón de ir arriba: más visible cuando estamos abajo
        scrollTopBtn.style.opacity = scrollPercentage > 20 ? '1' : '0.7';

        // Botón de ir abajo: más visible cuando estamos arriba
        scrollBottomBtn.style.opacity = scrollPercentage < 80 ? '1' : '0.7';
    }

    // Posicionar los botones siempre en la parte inferior derecha
    function positionButtons() {
        if (!navButtons) return;

        // Siempre posicionar los botones abajo a la derecha
        navButtons.style.top = 'auto';
        navButtons.style.bottom = '20px';
        navButtons.style.right = '20px';
    }

    // Configurar los listeners para actualizar la opacidad
    tableContainer.addEventListener('scroll', updateButtonsOpacity);
    window.addEventListener('resize', positionButtons);

    // Inicializar posición y opacidad
    positionButtons();
    updateButtonsOpacity();

    // No necesitamos más código aquí, ya que los botones se manejan por contenedor
}

/**
 * Inicializa los eventos táctiles para widgets
 */
function initTouchEvents() {
    // Detectar si estamos en un dispositivo táctil
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    if (isTouchDevice) {
        // Añadir clase para identificar dispositivos táctiles
        document.documentElement.classList.add('touch-device');

        // Mejorar el manejo de eventos táctiles para widgets
        enhanceTouchForWidgets();
    }
}

/**
 * Mejora el manejo de eventos táctiles para widgets
 */
function enhanceTouchForWidgets() {
    const dashboard = document.getElementById('dashboard');
    if (!dashboard) return;


    // Evento de inicio de toque
    // Transformar eventos táctiles en eventos de ratón para unificar la lógica
    function mapTouchEventToMouseEvent(touchEvent, mouseEventType) {
        const touch = touchEvent.touches[0] || touchEvent.changedTouches[0];
        const mouseEvent = new MouseEvent(mouseEventType, {
            clientX: touch.clientX,
            clientY: touch.clientY,
            bubbles: true,
            cancelable: true
        });

        // Añadir propiedades adicionales para mantener compatibilidad
        mouseEvent.isTouchEvent = true;
        mouseEvent.originalEvent = touchEvent;

        return mouseEvent;
    }

    // Reemplazar todos los eventos táctiles con eventos de ratón
    ['touchstart', 'touchmove', 'touchend', 'touchcancel'].forEach(touchEvent => {
        dashboard.addEventListener(touchEvent, (e) => {
            // No procesar si se toca un checkbox de selección
            if (e.target.type == 'checkbox' || e.target.closest('.widget-checkbox-container')) return;

            // Para touchstart en widgets, no activar inmediatamente el modo edición
            // Se activará en touchmove cuando se supere el umbral de arrastre

            // Para touchmove, permitir el procesamiento incluso si no estamos en modo edición
            // El modo edición se activará automáticamente en el evento mousedown simulado
            // Solo bloquear touchend y touchcancel si no estamos en modo edición
            if ((touchEvent === 'touchend' || touchEvent === 'touchcancel') &&
                !dashboard.classList.contains('edit-mode')) {
                return;
            }

            const mouseEventType = {
                'touchstart': 'mousedown',
                'touchmove': 'mousemove',
                'touchend': 'mouseup',
                'touchcancel': 'mouseup'
            } [touchEvent];

            const mouseEvent = mapTouchEventToMouseEvent(e, mouseEventType);
            e.target.dispatchEvent(mouseEvent);

            // Prevenir el comportamiento predeterminado para evitar conflictos
            e.preventDefault();
        }, {
            passive: false
        });
    });

    // Eliminar manejadores específicos de ratón ya que ahora usamos los mismos para toques
    // Los eventos de ratón existentes se mantienen para compatibilidad con PC
}

/**
 * Encuentra el widget en las coordenadas dadas
 * @param {number} x - Coordenada X
 * @param {number} y - Coordenada Y
 * @returns {HTMLElement|null} - El widget encontrado o null
 */
function findTouchedWidget(x, y) {
    // Obtener todos los elementos en esa posición
    const elements = document.elementsFromPoint(x, y);

    // Buscar el primer elemento que sea un widget
    for (const element of elements) {
        if (element.classList.contains('widget')) {
            return element;
        }
    }

    return null;
}

/**
 * Verifica si un toque está sobre un elemento específico
 * @param {Touch} touch - El objeto Touch
 * @param {HTMLElement} element - El elemento a verificar
 * @returns {boolean} - true si el toque está sobre el elemento
 */
function isTouchOnElement(touch, element) {
    const rect = element.getBoundingClientRect();
    return (
        touch.clientX >= rect.left &&
        touch.clientX <= rect.right &&
        touch.clientY >= rect.top &&
        touch.clientY <= rect.bottom
    );
}

/**
 * Maneja el arrastre de un widget mediante toque
 * @param {HTMLElement} widget - El widget a arrastrar
 * @param {number} deltaX - Desplazamiento en X
 * @param {number} deltaY - Desplazamiento en Y
 * @param {number} startX - Posición inicial X del widget
 * @param {number} startY - Posición inicial Y del widget
 */
function handleTouchDrag(widget, deltaX, deltaY, startX, startY) {
    const widgetId = parseInt(widget.dataset.widgetId);
    const widgetObj = widgetManager.getWidget(widgetId);

    if (!widgetObj) {
        console.error("Widget no encontrado en la estructura de datos:", widgetId);
        return;
    }

    // Obtener el desplazamiento del scroll
    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
    const scrollY = window.pageYOffset || document.documentElement.scrollTop;

    // Calcular nueva posición, teniendo en cuenta el scroll
    let newX = startX + deltaX;
    let newY = startY + deltaY;

    // Limitar al dashboard
    const dashboard = document.getElementById('dashboard');
    if (!dashboard) {
        console.error("Dashboard no encontrado");
        return;
    }

    // Obtener el offset del dashboard respecto a la ventana
    const dashboardRect = dashboard.getBoundingClientRect();
    const dashboardOffset = {
        x: dashboardRect.left + scrollX,
        y: dashboardRect.top + scrollY
    };

    // Evitar que se salga por la izquierda o arriba
    if (newX < 0) newX = 0;
    if (newY < 0) newY = 0;

    // Evitar que se salga por la derecha o abajo
    const maxX = dashboard.offsetWidth - widget.offsetWidth;
    const maxY = dashboard.offsetHeight - widget.offsetHeight;

    if (newX > maxX) newX = maxX;
    if (newY > maxY) newY = maxY;

    // Asegurarse de que las coordenadas sean números válidos
    if (isNaN(newX)) newX = 0;
    if (isNaN(newY)) newY = 0;

    // Actualizar posición en el DOM con animación desactivada
    widget.style.transition = 'none';
    widget.style.left = `${newX}px`;
    widget.style.top = `${newY}px`;

    // Actualizar posición en el objeto widget
    widgetObj.x = newX;
    widgetObj.y = newY;

    console.log("Touch drag - New position:", newX, newY, "Scroll:", scrollX, scrollY, "Dashboard offset:", dashboardOffset);

    // Forzar reflow para aplicar cambios inmediatamente
    widget.offsetHeight;
}

/**
 * Maneja el redimensionamiento de un widget mediante toque
 * @param {HTMLElement} widget - El widget a redimensionar
 * @param {number} deltaX - Desplazamiento en X
 * @param {number} deltaY - Desplazamiento en Y
 * @param {number} startWidth - Ancho inicial del widget
 * @param {number} startHeight - Alto inicial del widget
 */
function handleTouchResize(widget, deltaX, deltaY, startWidth, startHeight) {
    // Calcular nuevas dimensiones
    const newWidth = Math.max(50, startWidth + deltaX);
    const newHeight = Math.max(50, startHeight + deltaY);

    // Actualizar dimensiones en el DOM
    widget.style.width = `${newWidth}px`;
    widget.style.height = `${newHeight}px`;

    // Actualizar dimensiones en el objeto
    const widgetId = parseInt(widget.dataset.widgetId);
    const widgetObj = widgetManager.getWidget(widgetId);

    if (widgetObj) {
        widgetObj.width = newWidth;
        widgetObj.height = newHeight;
        console.log("Touch resize - New dimensions:", newWidth, newHeight, "Widget ID:", widgetId);
    } else {
        console.error("Widget no encontrado en la estructura de datos para redimensionar:", widgetId);
    }
}

/**
 * Maneja el tap (toque rápido) en un widget
 * @param {HTMLElement} widget - El widget tocado
 */
function handleWidgetTap(widget) {
    // Verificar el modo actual
    const dashboard = document.getElementById('dashboard');

    if (dashboard.classList.contains('delete-mode')) {
        // Modo eliminar
        const widgetId = parseInt(widget.dataset.widgetId);

        if (confirm('¿Estás seguro de que deseas eliminar este widget?')) {
            widgetManager.deleteWidget(widgetId);
            widget.remove();
        }
    } else if (dashboard.classList.contains('edit-widget-mode')) {
        // Modo editar
        const widgetId = parseInt(widget.dataset.widgetId);
        dashboardManager.openWidgetEditDialog(widgetId);
    }
    // En modo mover, no hacemos nada en el tap, solo en el arrastre
}