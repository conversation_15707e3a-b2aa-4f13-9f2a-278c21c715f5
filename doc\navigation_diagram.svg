<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="700" height="550"
  xmlns="http://www.w3.org/2000/svg">
  <!-- Estilos -->
  <defs>
    <style>
      .box {
        fill: #f5f5f5;
        stroke: #4a6fa5;
        stroke-width: 2;
        rx: 10;
        ry: 10;
      }
      .box-text {
        font-family: Arial, sans-serif;
        font-size: 16px;
        text-anchor: middle;
        fill: #333333;
      }
      .arrow {
        stroke: #4a6fa5;
        stroke-width: 2;
        fill: none;
        marker-end: url(#arrowhead);
      }
      .arrow-text {
        font-family: Arial, sans-serif;
        font-size: 12px;
        text-anchor: middle;
        fill: #666666;
      }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4a6fa5" />
    </marker>
  </defs>

  <!-- Cajas (pantallas) -->
  <rect x="50" y="50" width="150" height="80" class="box" />
  <text x="125" y="90" class="box-text">Inicio de sesión</text>

  <rect x="275" y="50" width="150" height="80" class="box" />
  <text x="350" y="90" class="box-text">Menú principal</text>

  <rect x="500" y="50" width="150" height="80" class="box" />
  <text x="575" y="90" class="box-text">Tableros</text>

  <rect x="275" y="200" width="150" height="80" class="box" />
  <text x="350" y="240" class="box-text">Empresas</text>

  <rect x="275" y="350" width="150" height="80" class="box" />
  <text x="350" y="390" class="box-text">Usuarios</text>

  <rect x="50" y="200" width="150" height="80" class="box" />
  <text x="125" y="240" class="box-text">Config. Usuario</text>

  <rect x="500" y="200" width="150" height="80" class="box" />
  <text x="575" y="240" class="box-text">Tableros Empresa</text>

  <rect x="500" y="350" width="150" height="80" class="box" />
  <text x="575" y="390" class="box-text">Config. Tablero</text>

  <rect x="500" y="450" width="150" height="80" class="box" />
  <text x="575" y="490" class="box-text">Config. Widget</text>

  <rect x="50" y="350" width="150" height="80" class="box" />
  <text x="125" y="390" class="box-text">Ficha Empresa</text>

  <rect x="50" y="450" width="150" height="80" class="box" />
  <text x="125" y="490" class="box-text">Ficha Usuario</text>

  <!-- Flechas (navegación) -->
  <!-- Inicio de sesión -> Menú principal -->
  <path d="M 200 90 L 275 90" class="arrow" />

  <!-- Menú principal -> Tableros -->
  <path d="M 425 90 L 500 90" class="arrow" />

  <!-- Menú principal -> Empresas -->
  <path d="M 350 130 L 350 200" class="arrow" />

  <!-- Menú principal -> Config. Usuario -->
  <path d="M 275 90 C 200 90 200 150 125 200" class="arrow" />

  <!-- Empresas -> Usuarios -->
  <path d="M 350 280 L 350 350" class="arrow" />

  <!-- Empresas -> Tableros Empresa -->
  <path d="M 425 240 L 500 240" class="arrow" />

  <!-- Empresas -> Ficha Empresa -->
  <path d="M 275 240 L 200 350" class="arrow" />

  <!-- Usuarios -> Ficha Usuario -->
  <path d="M 275 390 L 200 450" class="arrow" />

  <!-- Tableros -> Empresas (flecha curva) -->
  <path d="M 575 130 C 575 160 450 160 350 200" class="arrow" />

  <!-- Tableros -> Config. Tablero -->
  <path d="M 575 130 L 575 350" class="arrow" />

  <!-- Tableros -> Config. Widget -->
  <path d="M 575 130 C 650 200 650 400 575 450" class="arrow" />

  <!-- Tableros Empresa -> Tableros -->
  <path d="M 575 200 C 650 150 650 100 575 130" class="arrow" />

  <!-- Flecha de retorno (todas las pantallas pueden volver al inicio) -->
  <path d="M 125 50 C 125 20 350 20 350 50" class="arrow" stroke-dasharray="5,5" />
  <text x="350" y="30" class="arrow-text">Cerrar sesión (desde cualquier pantalla)</text>
</svg>
