# Guía Técnica: Sistema de Formulario de Edición de Widgets

## Archivos Involucrados
1. **js/tableros/dashboard.js**: Contiene `openWidgetEditDialog()` que inicia el proceso
2. **js/tableros/widget_fields.js**: Define `getWidgetFields()` que proporciona la estructura de campos
3. **js/tableros/form_fields_accordion.js**: Implementa `FormFieldsAccordion.createAccordion()` para generar el acordeón
4. **js/utiles/html.js**: Proporciona utilidades para crear elementos HTML
5. **index.html**: Contiene el template del diálogo que se clona para mostrar el formulario

## Flujo de Ejecución

1. **Inicio del proceso**:
   ```javascript
   // En dashboard.js
   handleWidgetEditClick(e) {
       this.openWidgetEditDialog(e);
   }
   
   openWidgetEditDialog(e) {
       // Obtiene el widget a editar
       // Crea el diálogo clonando el template
       // Obtiene los campos y zonas
       // Genera el formulario
   }
   ```

2. **Obtención del template del diálogo**:
   - El diálogo se obtiene clonando un template en index.html con ID `widget-edit-dialog-template`
   - Si se quiere modificar la estructura del diálogo, hay que editar este template

3. **Obtención de campos y zonas**:
   ```javascript
   // Ejemplo de como implementar getWidgetFields() en widget_fields.js
   function getWidgetFields() {
       return {
           zones: {
               base: { name: "general", icon: "settings", title:'General' },
               posicion: { name: "posicion", icon: "visibility",title:'Posición' },
               color: { name: "color", icon: "database", title:"Color" }
               // Más zonas...
           },
           fields: [
               { id: "title", label: "Título", type: "text", zone: "general" },
               { id: "chartType", label: "Tipo de gráfico", type: "select", zone: "posicion", 
                 options: [{value: "bar", label: "Barras"}, {value: "line", label: "Líneas"}] },
               { id: "dataSource", label: "Fuente de datos", type: "select", zone: "general" }
               // Más campos...
           ]
       };
   }
   ```

4. **Generación del acordeón**:

   ```javascript
   // En dashboard.js, después de obtener los campos
   const formContainer = dialogElement.querySelector('.widget-form-container');
   FormFieldsAccordion.createAccordion(formContainer, zones,fieldsForType, true);
   ```

## Consideraciones Importantes

1. **Template del diálogo**: 
   - El diálogo está definido en `index.html` como un template
   - Tiene una estructura específica con contenedor para el formulario
   - Si se modifica, hay que asegurarse de mantener los selectores que usa `openWidgetEditDialog()`

2. **Definición de campos**:
   - Todos los campos posibles están en `widget_fields.js`
   - Cada tipo de widget usa un subconjunto de estos campos
   - Para añadir un nuevo campo, hay que modificar este archivo

3. **Generación del acordeón**:
   - El acordeón se genera dinámicamente, no existe en el HTML inicial
   - Las clases CSS para el acordeón están definidas en el CSS accordion.css
   - La interactividad se añade mediante event un check oculto de la cabecera del acordeon. 
     Al pulsar el label for="checkbox_oculto" el check se marca/desmarca y por clases css se pliega/despliega la sección del acordeón.

## Detalles de Implementación del Acordeón

### Creación de Secciones del Acordeón

Para cada zona definida en `widget_fields.js`, se crea una sección del acordeón mediante `createAccordionSection()`:

```javascript
static createAccordionSection(zone, zoneFields, isEdit) {
    // Crear la sección del acordeón
    const section = uhtml.crearElemento('div', {
        class: 'accordion-section'
    });

    // Crear el encabezado de la sección
    const header = uhtml.crearElemento('div', {
        class: 'accordion-header',
        attrs: {
            'data-zone-id': zone.name
        }
    });

    // Añadir icono si existe
    if (zone.icon) {
        uhtml.crearIcono(zone.icon, {
            class: 'zone-icon'
        }, header);
    }

    // Añadir título
    uhtml.crearElemento('span', {
        class: 'title',
        text: zone.name
    }, header);

    // Añadir icono de despliegue
    uhtml.crearIcono('expand_more', {
        class: 'toggle-icon'
    }, header);

    // Crear el contenido de la sección
    const content = uhtml.crearElemento('div', {
        class: 'accordion-content',
        attrs: {
            'data-zone-id': zone.name
        }
    });

    // Añadir los campos a la sección
    zoneFields.forEach(field => {
        this.createFieldElement(field, content, isEdit);
    });

    // Añadir encabezado y contenido a la sección
    section.appendChild(header);
    section.appendChild(content);

    return section;
}
```

### Estructura HTML Generada para el Acordeón

El acordeón generado tiene la siguiente estructura HTML:

```html
<div class="form-fields-accordion">
    <!-- Sección de acordeón para cada zona -->
    <div class="accordion-section">
        <!-- Encabezado de la sección -->
        <div class="accordion-header" data-zone-id="General">
            <i class="material-icons zone-icon">settings</i>
            <span class="title">Configuración General</span>
            <i class="material-icons toggle-icon">expand_more</i>
        </div>
        
        <!-- Contenido de la sección -->
        <div class="accordion-content" data-zone-id="General">
            <!-- Campos de esta zona -->
            <div class="field-group">
                <label for="title">Título</label>
                <input type="text" id="title" name="title" data-param-name="title">
            </div>
            <!-- Más campos... -->
        </div>
    </div>
    
    <!-- Más secciones de acordeón... -->
</div>
```

## Funciones de html.js y HTML Generado

El módulo `uhtml` en `js/utiles/html.js` proporciona funciones para crear elementos HTML de manera consistente:

### Principales Funciones y HTML Generado

1. **crearElemento**: Función base para crear cualquier elemento HTML
   - Genera elementos con atributos, clases y contenido configurables
   - HTML generado: `<div class="mi-clase" data-id="123">Contenido</div>`

2. **crearIcono**: Crea iconos de Material Design
   - HTML generado: `<i class="material-icons zone-icon">settings</i>`

3. **crearInputText**: Crea campos de texto
   - HTML generado: `<input type="text" id="title" name="title" value="Valor predeterminado">`

4. **crearInputNumber**: Crea campos numéricos
   - HTML generado: `<input type="number" id="maxItems" min="1" max="100" value="10">`

5. **crearCheckbox**: Crea casillas de verificación
   - HTML generado: `<input type="checkbox" id="showLegend" checked>`

6. **crearSelect**: Crea listas desplegables con opciones
   - HTML generado:
     ```html
     <select id="chartType" name="chartType">
         <option value="bar">Barras</option>
         <option value="line">Líneas</option>
     </select>
     ```

7. **crearGrupoCampo**: Crea grupos de campo con etiqueta e input
   - HTML generado:
     ```html
     <div class="field-group">
         <label for="title">Título</label>
         <input type="text" id="title" name="title">
         <div id="error-title" class="error-message"></div>
     </div>
     ```

### HTML Generado para Diferentes Tipos de Campos

1. **Campo de texto**:
   ```html
   <div class="field-group">
       <label for="title">Título</label>
       <input type="text" id="title" name="title" data-param-name="title" value="Título predeterminado">
   </div>
   ```

2. **Campo select**:
   ```html
   <div class="field-group">
       <label for="chartType">Tipo de gráfico</label>
       <select id="chartType" name="chartType" data-param-name="chartType">
           <option value="bar">Barras</option>
           <option value="line">Líneas</option>
           <option value="pie">Circular</option>
       </select>
   </div>
   ```

3. **Campo checkbox**:
   ```html
   <div class="field-group">
       <label for="showLegend">Mostrar leyenda</label>
       <input type="checkbox" id="showLegend" name="showLegend" data-param-name="showLegend" checked>
   </div>
   ```

4. **Campo numérico**:
   ```html
   <div class="field-group">
       <label for="maxItems">Máximo de elementos</label>
       <input type="number" id="maxItems" name="maxItems" data-param-name="maxItems" min="1" max="100" value="10">
   </div>
   ```

## Ejemplo: Transformar el Acordeón en Pestañas

Para ilustrar cómo se podría modificar el sistema de formularios, a continuación se muestra cómo transformar el acordeón en un sistema de pestañas:

### 1. Modificar form_fields_accordion.js

Renombrar la clase a algo como `FormFieldsTabs` y cambiar `createAccordion()` para generar una estructura de pestañas:

```javascript
static createTabs(container, zones, fields, isEdit = true) {
    // Crear contenedor de pestañas
    const tabsContainer = uhtml.crearElemento('div', {
        class: 'form-fields-tabs'
    });
    
    // Crear navegación de pestañas
    const tabsNav = uhtml.crearElemento('div', {
        class: 'tabs-nav'
    }, tabsContainer);
    
    // Crear contenedor de contenido
    const tabsContent = uhtml.crearElemento('div', {
        class: 'tabs-content'
    }, tabsContainer);
    
    // Agrupar campos por zona (igual que antes)
    const fieldsByZone = {};
    
    // Inicializar todas las zonas disponibles
    Object.keys(zones).forEach(zoneKey => {
        fieldsByZone[zoneKey] = [];
    });
    
    // Agrupar los campos por zona
    fields.forEach(field => {
        if (field.zone && fieldsByZone[field.zone]) {
            fieldsByZone[field.zone].push(field);
        } else {
            // Si no tiene zona o la zona no existe, ponerlo en la primera zona
            const firstZone = Object.keys(zones)[0];
            fieldsByZone[firstZone].push(field);
        }
    });
    
    // Crear pestañas para cada zona
    Object.keys(zones).forEach((zoneKey, index) => {
        const zone = zones[zoneKey];
        const zoneFields = fieldsByZone[zoneKey];
        
        if (zoneFields.length > 0) {
            // Crear tab en la navegación
            const tabButton = uhtml.crearElemento('button', {
                class: 'tab-button' + (index === 0 ? ' active' : ''),
                text: zone.name,
                attrs: {
                    'data-zone-id': zoneKey
                }
            }, tabsNav);
            
            if (zone.icon) {
                uhtml.crearIcono(zone.icon, {
                    class: 'tab-icon'
                }, tabButton);
            }
            
            // Crear panel de contenido
            const tabPanel = uhtml.crearElemento('div', {
                class: 'tab-panel' + (index === 0 ? ' active' : ''),
                attrs: {
                    'data-zone-id': zoneKey
                }
            }, tabsContent);
            
            // Añadir campos al panel
            zoneFields.forEach(field => {
                this.createFieldElement(field, tabPanel, isEdit);
            });
        }
    });
    
    // Añadir a contenedor
    container.appendChild(tabsContainer);
    
    // Inicializar eventos
    this.initTabsEvents(tabsContainer);
    
    return tabsContainer;
}

static initTabsEvents(tabsContainer) {
    const tabButtons = tabsContainer.querySelectorAll('.tab-button');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Desactivar todas las pestañas
            tabsContainer.querySelectorAll('.tab-button').forEach(b => 
                b.classList.remove('active'));
            tabsContainer.querySelectorAll('.tab-panel').forEach(p => 
                p.classList.remove('active'));
            
            // Activar la pestaña seleccionada
            button.classList.add('active');
            const zoneId = button.dataset.zoneId;
            tabsContainer.querySelector(`.tab-panel[data-zone-id="${zoneId}"]`)
                .classList.add('active');
        });
    });
}
```

### 2. Estructura HTML Generada para las Pestañas

```html
<div class="form-fields-tabs">
    <div class="tabs-nav">
        <button class="tab-button active" data-zone-id="general">
            <i class="material-icons tab-icon">settings</i>
            Configuración General
        </button>
        <button class="tab-button" data-zone-id="display">
            <i class="material-icons tab-icon">visibility</i>
            Visualización
        </button>
        <button class="tab-button" data-zone-id="data">
            <i class="material-icons tab-icon">database</i>
            Datos
        </button>
    </div>
    <div class="tabs-content">
        <div class="tab-panel active" data-zone-id="general">
            <!-- Campos de la zona general -->
            <div class="field-group">
                <label for="title">Título</label>
                <input type="text" id="title" name="title" data-param-name="title">
            </div>
            <!-- Más campos... -->
        </div>
        <div class="tab-panel" data-zone-id="display">
            <!-- Campos de la zona display -->
            <div class="field-group">
                <label for="chartType">Tipo de gráfico</label>
                <select id="chartType" name="chartType" data-param-name="chartType">
                    <option value="bar">Barras</option>
                    <option value="line">Líneas</option>
                </select>
            </div>
            <!-- Más campos... -->
        </div>
        <div class="tab-panel" data-zone-id="data">
            <!-- Campos de la zona data -->
            <div class="field-group">
                <label for="dataSource">Fuente de datos</label>
                <select id="dataSource" name="dataSource" data-param-name="dataSource">
                    <option value="sales">Ventas</option>
                    <option value="users">Usuarios</option>
                </select>
            </div>
            <!-- Más campos... -->
        </div>
    </div>
</div>
```

### 3. Modificar dashboard.js

Para implementar este cambio, habría que modificar la llamada en dashboard.js:

```javascript
// Antes
FormFieldsAccordion.createAccordion(formContainer,zones,  fieldsForType, true);

// Después
FormFieldsTabs.createTabs(formContainer, zones, fieldsForType, true);


### 4. CSS ppara la Estructura de Tabs

/* Estilos para el sistema de pestañas en formularios de widgets
   Utiliza las variables CSS definidas en default.css para mantener consistencia
*/

:root {
  /* Estas variables ya deberían estar definidas en default.css, 
     se incluyen aquí como referencia */
  --color-primary: var(--color-primary, #2196F3);
  --color-primary-light: var(--color-primary-light, rgba(33, 150, 243, 0.2));
  --color-text: var(--color-text, #333);
  --color-text-secondary: var(--color-text-secondary, #666);
  --color-border: var(--color-border, #ddd);
  --color-background: var(--color-background, #fff);
  --color-background-light: var(--color-background-light, #f5f5f5);
  --color-background-hover: var(--color-background-hover, #eaeaea);
  --border-radius: var(--border-radius, 4px);
  --shadow-small: var(--shadow-small, 0 1px 3px rgba(0, 0, 0, 0.1));
  --font-family: var(--font-family, 'Roboto', sans-serif);
  --transition-default: var(--transition-default, all 0.2s ease);
}

/* Contenedor principal de pestañas */
.form-fields-tabs {
  width: 100%;
  font-family: var(--font-family);
  margin-bottom: 20px;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-small);
}

/* Navegación de pestañas */
.tabs-nav {
  display: flex;
  background-color: var(--color-background-light);
  border-bottom: 1px solid var(--color-border);
}

/* Botones de pestañas */
.tab-button {
  padding: 12px 16px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-secondary);
  display: flex;
  align-items: center;
  position: relative;
  transition: var(--transition-default);
  outline: none;
}

.tab-button:hover {
  background-color: var(--color-background-hover);
  color: var(--color-text);
}

.tab-button.active {
  color: var(--color-primary);
  background-color: var(--color-background);
  border-bottom: 2px solid var(--color-primary);
}

/* Iconos en las pestañas */
.tab-icon {
  font-size: 18px;
  margin-right: 8px;
}

/* Contenedor de contenido de pestañas */
.tabs-content {
  background-color: var(--color-background);
  padding: 0;
}

/* Paneles individuales de pestañas */
.tab-panel {
  display: none;
  padding: 16px;
}

.tab-panel.active {
  display: block;
  animation: fadeIn 0.3s ease;
}

/* Grupos de campos dentro de las pestañas */
.tab-panel .field-group {
  margin-bottom: 16px;
}

.tab-panel label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  color: var(--color-text);
  font-weight: 500;
}

.tab-panel input[type="text"],
.tab-panel input[type="number"],
.tab-panel select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: border 0.2s ease;
}

.tab-panel input[type="text"]:focus,
.tab-panel input[type="number"]:focus,
.tab-panel select:focus {
  border-color: var(--color-primary);
  outline: none;
  box-shadow: 0 0 0 2px var(--color-primary-light);
}

.tab-panel input[type="checkbox"] {
  margin-right: 8px;
}

/* Animación de aparición */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Estilos responsivos */
@media (max-width: 600px) {
  .tabs-nav {
    flex-direction: column;
  }
  
  .tab-button {
    width: 100%;
    justify-content: flex-start;
  }
  
  .tab-button.active {
    border-bottom: none;
    border-left: 2px solid var(--color-primary);
  }
}

