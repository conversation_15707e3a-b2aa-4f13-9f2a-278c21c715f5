/**
 * Extensión para WidgetFieldsManager que implementa el acordeón en los campos de widgets
 */

// Guardar la referencia al método original
const originalFillWidgetFields = WidgetFieldsManager.prototype.fillWidgetFields;

// Reemplazar el método con nuestra versión mejorada
WidgetFieldsManager.prototype.fillWidgetFields = function (widget, isEdit = true, container = null) {
    // Obtener el contenedor específico para este tipo de widget
    const prefix = isEdit ? 'edit-' : '';
    const widgetFieldsContainerId = `${prefix}${widget.type}-widget-fields`;

    // Función auxiliar para obtener el elemento según el contexto
    const getElement = (id) => {
        if (container) {
            return container.querySelector(`#${id}`);
        } else {
            return document.getElementById(id);
        }
    };

    // Obtener o crear el contenedor de campos
    let fieldsContainer = getElement(widgetFieldsContainerId);

    // Si no existe el contenedor específico para este tipo de widget, crearlo
    if (!fieldsContainer) {
        console.log(`Creando contenedor de campos con ID ${widgetFieldsContainerId}`);

        // Buscar el contenedor principal de campos (debe existir en el HTML)
        const widgetFieldsContainer = container ?
            container.querySelector('.widget-fields-container') :
            document.querySelector('.widget-fields-container');

        // Si no existe el contenedor principal, es un error grave en la estructura HTML
        if (!widgetFieldsContainer) {
            console.error('ERROR: No se encontró el contenedor principal .widget-fields-container en el HTML');
            throw new Error('Falta el elemento .widget-fields-container en el HTML. Este elemento es obligatorio.');
        }

        // Crear el contenedor específico para este tipo de widget
        fieldsContainer = document.createElement('div');
        fieldsContainer.id = widgetFieldsContainerId;
        fieldsContainer.className = 'widget-fields';

        // Añadir al contenedor principal
        widgetFieldsContainer.appendChild(fieldsContainer);
    }

    // Verificar si ya se ha convertido a acordeón
    if (fieldsContainer.querySelector('.form-fields-accordion')) {
        console.log('El contenedor ya tiene un acordeón, solo actualizando valores');
        this._updateAccordionValues(widget, isEdit, container);
        return;
    }

    console.log(`Creando acordeón para widget de tipo ${widget.type} (isEdit: ${isEdit})`);

    // Obtener la información de los campos para este tipo de widget
    const widgetFields = this.getWidgetFields(widget.type, isEdit);

    // Limpiar el contenedor
    fieldsContainer.innerHTML = '';

    // Crear el acordeón
    FormFieldsAccordion.createAccordion(fieldsContainer, widgetFields.zones, widgetFields.fields, isEdit);

    // Ahora actualizar los valores en el acordeón
    this._updateAccordionValues(widget, isEdit, container);
};

/**
 * Método auxiliar para actualizar los valores en el acordeón
 * @param {Object} widget - Widget cuyos datos se usarán para llenar el formulario
 * @param {boolean} isEdit - Indica si es para edición (true) o creación (false)
 * @param {HTMLElement} container - Contenedor donde buscar los elementos (opcional)
 */
WidgetFieldsManager.prototype._updateAccordionValues = function (widget, isEdit = true, container = null) {
    const fields = this.getWidgetFields(widget.type, isEdit).fields;

    console.log(`Actualizando valores para widget de tipo ${widget.type} (isEdit: ${isEdit})`);

    // Función auxiliar para obtener el elemento según el contexto
    const getElement = (id) => {
        if (container) {
            return container.querySelector(`#${id}`);
        } else {
            return document.getElementById(id);
        }
    };

    fields.forEach(field => {
        const element = getElement(field.id);
        if (!element) {
            console.warn(`No se encontró el elemento con ID ${field.id}`);
            return;
        }

        if (field.zone === 'posicion') {
            if (field.paramName === 'x' && widget.x !== undefined) {
                element.value = widget.x;
            } else if (field.paramName === 'y' && widget.y !== undefined) {
                element.value = widget.y;
            } else if (field.paramName === 'width' && widget.width !== undefined) {
                element.value = widget.width;
            } else if (field.paramName === 'height' && widget.height !== undefined) {
                element.value = widget.height;
            }

            return;
        }
        if (field.type === 'select' && field.dataSource) {
            // Llenar selector con datos dinámicos
            element.innerHTML = '';
            dataService[field.dataSource]().forEach(item => {
                const option = document.createElement('option');
                option.value = item.id;
                option.textContent = item.name;
                element.appendChild(option);
            });

            // Establecer el valor seleccionado
            if (widget.params && widget.params[field.paramName]) {
                element.value = widget.params[field.paramName];
            } else if (element.options.length > 0) {
                element.selectedIndex = 0;
            }
        } else if (field.type === 'select' && field.options) {
            // Para selects con opciones estáticas, llenar el selector con las opciones
            element.innerHTML = '';
            field.options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.label;
                element.appendChild(optionElement);
            });

            // Establecer el valor seleccionado
            if (widget.params && widget.params[field.paramName]) {
                element.value = widget.params[field.paramName];
            } else if (element.options.length > 0) {
                element.selectedIndex = 0;
            }
        } else if (field.type === 'checkbox') {
            // Establecer el estado del checkbox
            if (widget.params && widget.params[field.paramName] !== undefined) {
                element.checked = widget.params[field.paramName];
            } else if (field.defaultValue !== undefined) {
                element.checked = field.defaultValue;
            }
        } else if (field.type === 'input' || field.type === 'number') {
            // Establecer el valor del input
            if (widget.params && widget.params[field.paramName] !== undefined) {
                element.value = widget.params[field.paramName];
            } else if (field.defaultValue !== undefined) {
                element.value = field.defaultValue;
            } else if (widget.type === 'text' && field.paramName === 'text') {
                element.value = '(contenido del widget)';
            }
        }
    });
};


// Guardar la referencia al método original
const originalGetWidgetParams = WidgetFieldsManager.prototype.getWidgetParams;

// Reemplazar el método con nuestra versión mejorada
WidgetFieldsManager.prototype.getWidgetParams = function (widgetType, isEdit = true, container = null) {
    const fields = this.getWidgetFields(widgetType, isEdit).fields;
    const params = {};

    console.log(`Obteniendo parámetros para widget de tipo ${widgetType} (isEdit: ${isEdit})`);

    // Obtener el widget actual para preservar valores existentes
    let existingWidget = null;
    if (isEdit && container) {
        const widgetId = parseInt(container.dataset.widgetId);
        if (widgetId) {
            existingWidget = app.widgetManager.getWidget(widgetId);
            console.log('Widget existente encontrado:', existingWidget);
        }
    }

    // Preservar series existentes para widgets de tipo chart
    if (existingWidget && widgetType === 'chart' && existingWidget.params && existingWidget.params.series) {
        params.series = [...existingWidget.params.series]; // Clonar el array de series
        console.log('Series preservadas:', params.series);
    }

    // Función auxiliar para obtener el elemento según el contexto
    const getElement = (id) => {
        if (container) {
            return container.querySelector(`#${id}`);
        } else {
            return document.getElementById(id);
        }
    };

    fields.forEach(field => {
        const element = getElement(field.id);

        if (!element) {
            console.warn(`No se encontró el elemento con ID ${field.id}`);

            // Para widgets de texto, asegurar que siempre haya un valor por defecto
            if (widgetType === 'text' && field.paramName === 'text' && !params[field.paramName]) {
                params[field.paramName] = '(contenido del widget)';
            }

            return;
        }

        if (field.type === 'number') {
            params[field.paramName] = parseInt(element.value);
        } else if (field.type === 'checkbox') {
            params[field.paramName] = element.checked;
        } else {
            params[field.paramName] = element.value;
        }
    });

    // Para widgets de texto, asegurar que siempre haya un valor por defecto
    if (widgetType === 'text' && !params['text']) {
        params['text'] = '(contenido del widget)';
    }

    return params;
};