<!DOCTYPE html>
<html lang="es">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Usuario - iPRA Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            color: #4a6fa5;
            text-align: center;
            border-bottom: 2px solid #4a6fa5;
            padding-bottom: 10px;
        }

        h2 {
            color: #4a6fa5;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }

        h3 {
            color: #3a5a8c;
        }

        a {
            color: #4a6fa5;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        code {
            background-color: #f5f5f5;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }

        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-family: monospace;
        }

        .diagram {
            text-align: center;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre;
        }

        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        @media print {
            body {
                font-size: 12pt;
            }

            h1 {
                font-size: 18pt;
            }

            h2 {
                font-size: 16pt;
            }

            h3 {
                font-size: 14pt;
            }

            pre,
            code {
                font-size: 10pt;
            }

            a {
                color: #000;
                text-decoration: none;
            }

            @page {
                margin: 2cm;
            }
        }
    </style>
</head>

<body>
    <h1>iPRA Dashboard</h1>

    <h2>Índice</h2>
    <ol>
        <li><a href="#introduccion">Introducción</a></li>
        <li><a href="#tipos-de-usuarios">Tipos de usuarios y permisos</a>
            <ul>
                <li><a href="#administrador">Administrador</a></li>
                <li><a href="#editor-tableros">Editor de Tableros</a></li>
                <li><a href="#visor-tableros">Visor de Tableros</a></li>
                <li><a href="#permisos-especiales">Permisos especiales</a></li>
            </ul>
        </li>
        <li><a href="#pantallas">Pantallas y funcionalidades</a>
            <ul>
                <li><a href="#pantalla-login">Pantalla de inicio de sesión</a></li>
                <li><a href="#menu-principal">Menú principal</a></li>
                <li><a href="#pantalla-tableros">Pantalla de tableros</a></li>
                <li><a href="#gestion-empresas">Gestión de empresas</a></li>
                <li><a href="#gestion-usuarios">Gestión de usuarios</a></li>
                <li><a href="#configuracion-usuario">Configuración de usuario</a></li>
            </ul>
        </li>
        <li><a href="#navegacion">Navegación entre pantallas</a></li>
        <li><a href="#temas">Temas de la aplicación</a></li>
        <li><a href="#tableros">Tableros</a>
            <ul>
                <li><a href="#configuracion-tableros">Configuración de tableros</a></li>
                <li><a href="#operaciones-tableros">Operaciones con tableros</a></li>
                <li><a href="#widgets">Widgets</a>
                    <ul>
                        <li><a href="#manipulacion-widgets">Manipulación de widgets</a></li>
                        <li><a href="#tipos-widgets">Tipos de widgets</a></li>
                        <li><a href="#configuracion-widgets">Configuración de widgets</a></li>
                    </ul>
                </li>
            </ul>
        </li>
        <li><a href="#funcionalidades-genericas">Funcionalidades genéricas</a>
            <ul>
                <li><a href="#atajos-teclado">Atajos de teclado y navegación</a></li>
                <li><a href="#persistencia-sesion">Persistencia de sesión</a></li>
                <li><a href="#interaccion-dialogos">Interacción con diálogos</a></li>
                <li><a href="#gestion-listados">Gestión de listados</a></li>
                <li><a href="#interaccion-gestual">Interacción gestual</a></li>
            </ul>
        </li>
        <li><a href="#datos">Datos de la aplicación</a></li>
        <li><a href="#compatibilidad">Compatibilidad con dispositivos</a></li>
        <li><a href="#conclusiones">Conclusiones</a></li>
    </ol>

    <h2 id="introduccion">Introducción</h2>
    <p>iPRA Dashboard es una aplicación que permite a los usuarios configurar y visualizar tableros personalizados (dashboards) para monitorizar datos en tiempo real. El objetivo
        principal de la aplicación es proporcionar una herramienta flexible y fácil de usar para la visualización de datos, permitiendo a cada usuario o empresa crear sus propios
        tableros adaptados a sus necesidades específicas.</p>
    <p>La aplicación está diseñada para ser intuitiva y accesible, ofreciendo una experiencia de usuario fluida tanto en dispositivos de escritorio como en dispositivos móviles.
    </p>

    <h2 id="tipos-de-usuarios">Tipos de usuarios y permisos</h2>
    <p>La aplicación iPRA Dashboard cuenta con tres tipos de usuarios, cada uno con diferentes niveles de permisos:</p>

    <h3>Administrador</h3>
    <ul>
        <li>Puede crear, editar y eliminar usuarios</li>
        <li>Puede crear, editar y eliminar tableros</li>
        <li>Tiene acceso completo a todas las funcionalidades de la aplicación</li>
    </ul>

    <h3>Editor de Tableros</h3>
    <ul>
        <li>Puede crear, editar y eliminar tableros de su empresa</li>
        <li>No puede gestionar usuarios</li>
    </ul>

    <h3>Visor de Tableros</h3>
    <ul>
        <li>Puede ver los tableros de su empresa</li>
        <li>No puede crear, editar ni eliminar tableros</li>
        <li>Acceso de solo lectura</li>
    </ul>

    <h3>Permisos especiales</h3>
    <p>La empresa con ID 1 (iPRA) tiene un estatus especial en la aplicación:</p>
    <ul>
        <li>Los administradores de iPRA son los únicos que pueden crear empresas</li>
        <li>Los administradores de iPRA pueden crear usuarios para cualquier empresa</li>
        <li>Los administradores de iPRA pueden crear tableros para cualquier empresa</li>
    </ul>
    <p>Los administradores y editores de otras empresas solo pueden crear tableros para su propia empresa, no para otras empresas.</p>

    <h2 id="pantallas">Pantallas y funcionalidades</h2>

    <h3>Pantalla de inicio de sesión</h3>
    <ul>
        <li>Permite a los usuarios acceder a la aplicación mediante sus credenciales</li>
        <li>Incluye un selector de tema para personalizar la apariencia de la aplicación</li>
        <li>El tema seleccionado se guarda en el navegador del usuario</li>
    </ul>

    <h3>Menú principal</h3>
    <ul>
        <li>Muestra las opciones disponibles según el tipo de usuario</li>
        <li>Todos los usuarios tienen acceso a la opción "Tablero"</li>
        <li>Los administradores tienen acceso adicional a "Empresas" (o "Empresa" si no son de iPRA) y "Personalizar"</li>
    </ul>

    <h3>Pantalla de tableros</h3>
    <ul>
        <li>Muestra el tablero activo con sus widgets</li>
        <li>Permite cambiar entre diferentes tableros</li>
        <li>Incluye opciones para crear, editar y eliminar tableros (según permisos)</li>
        <li>Permite añadir, editar y eliminar widgets (según permisos)</li>
    </ul>

    <h3>Gestión de empresas</h3>
    <ul>
        <li>Disponible solo para administradores</li>
        <li>Permite ver, crear, editar y eliminar empresas (solo administradores de iPRA pueden ver todas las empresas)</li>
        <li>Incluye acceso a la gestión de usuarios de cada empresa</li>
    </ul>

    <h3>Gestión de usuarios</h3>
    <ul>
        <li>Disponible solo para administradores</li>
        <li>Permite ver, crear, editar y eliminar usuarios</li>
        <li>Los administradores de iPRA pueden gestionar usuarios de cualquier empresa</li>
        <li>Los administradores de otras empresas solo pueden gestionar usuarios de su propia empresa</li>
        <li>Las contraseñas de los usuarios no son visibles ni siquiera para los administradores</li>
    </ul>

    <h3 id="configuracion-usuario">Configuración de usuario</h3>
    <ul>
        <li>Disponible para todos los usuarios desde el menú principal</li>
        <li>Permite modificar datos personales:
            <ul>
                <li>Nombre</li>
                <li>Correo electrónico</li>
                <li>Contraseña</li>
            </ul>
        </li>
        <li>Los cambios afectan únicamente al usuario que realiza la modificación</li>
        <li>La contraseña actual es requerida para confirmar cualquier cambio de seguridad</li>
    </ul>

    <h2 id="navegacion">Navegación entre pantallas</h2>
    <div style="text-align: center; margin: 20px 0;">
        <img src="navigation_diagram.svg" alt="Diagrama de navegación entre pantallas" style="max-width: 100%; height: auto;">
    </div>
    <ul>
        <li>Desde la pantalla de <strong>inicio de sesión</strong> se accede al <strong>menú principal</strong></li>
        <li>Desde el <strong>menú principal</strong> se puede acceder a:
            <ul>
                <li><strong>Tableros</strong>: Visualización y gestión de tableros</li>
                <li><strong>Empresas</strong>: Gestión de empresas</li>
                <li><strong>Configuración de usuario</strong>: Modificación de datos personales</li>
            </ul>
        </li>
        <li>Desde la pantalla de <strong>tableros</strong> se puede:
            <ul>
                <li>Volver al <strong>menú principal</strong></li>
                <li>Acceder a <strong>empresas</strong></li>
                <li>Abrir la <strong>configuración de tablero</strong> para modificar sus propiedades</li>
                <li>Abrir la <strong>configuración de widgets</strong> para personalizar elementos visuales</li>
            </ul>
        </li>
        <li>Desde la pantalla de <strong>empresas</strong> se puede:
            <ul>
                <li>Acceder a la <strong>gestión de usuarios</strong> de una empresa específica</li>
                <li>Ver los <strong>tableros de una empresa</strong> específica</li>
                <li>Abrir la <strong>ficha de empresa</strong> para ver o editar sus detalles</li>
            </ul>
        </li>
        <li>Desde la pantalla de <strong>usuarios</strong> se puede abrir la <strong>ficha de usuario</strong> para ver o editar sus detalles</li>
        <li>Desde cualquier pantalla se puede <strong>cerrar sesión</strong> y volver a la pantalla de inicio</li>
    </ul>

    <h2 id="temas">Temas de la aplicación</h2>
    <p>La aplicación iPRA Dashboard ofrece varios temas visuales que el usuario puede seleccionar según sus preferencias:</p>
    <ul>
        <li><strong>Menta Zen</strong>: Tema por defecto con tonos verdes suaves</li>
        <li><strong>Tron</strong>: Tema oscuro con acentos azules brillantes</li>
        <li><strong>Neumórfico</strong>: Tema con efecto de relieve y sombras suaves</li>
        <li><strong>Azul Nuboso</strong>: Tema con tonos azules suaves</li>
    </ul>
    <p>Los temas son una preferencia personal de cada usuario y se almacenan en el navegador local. Esto significa que:</p>
    <ul>
        <li>Cada usuario puede tener su propio tema preferido</li>
        <li>El tema seleccionado se recordará la próxima vez que el usuario inicie sesión en el mismo navegador</li>
        <li>Si el usuario accede desde otro dispositivo o navegador, deberá seleccionar su tema preferido nuevamente</li>
    </ul>

    <h2 id="tableros">Tableros</h2>
    <p>Los tableros son la parte central de la aplicación iPRA Dashboard. Cada tablero pertenece a una empresa específica y solo los usuarios de esa empresa pueden verlo.</p>

    <h3 id="configuracion-tableros">Configuración de tableros</h3>
    <p>Cada tablero puede personalizarse con las siguientes opciones:</p>
    <ul>
        <li><strong>Nombre</strong>: Identificador descriptivo del tablero</li>
        <li><strong>Dimensiones</strong>: Ancho y alto del tablero en píxeles</li>
        <li><strong>Color de fondo</strong>: Color del fondo del tablero</li>
        <li><strong>Cuadrícula</strong>: Activar/desactivar la cuadrícula y cambiar su color</li>
        <li><strong>Texto de widgets</strong>: Color por defecto para el texto de los widgets</li>
        <li><strong>Fondo de widgets</strong>: Color por defecto para el fondo de los widgets</li>
        <li><strong>Bordes de widgets</strong>: Mostrar/ocultar los bordes de los widgets</li>
        <li><strong>Widgets transparentes</strong>: Hacer transparentes los fondos de los widgets</li>
    </ul>
    <p>Si al cambiar el tamaño de un tablero los widgets no caben en la nueva configuración, estos se reajustarán automáticamente para adaptarse al nuevo tamaño.</p>

    <h3 id="operaciones-tableros">Operaciones con tableros</h3>
    <p>Los tableros admiten diversas operaciones que permiten una gestión eficiente:</p>
    <ul>
        <li><strong>Cambio rápido</strong>: Desde la pantalla de tableros, existe un botón de cambio rápido que permite alternar entre los diferentes tableros disponibles sin
            necesidad de volver al menú principal.</li>
        <li><strong>Crear nuevo tablero</strong>: Permite crear un tablero vacío con la configuración predeterminada.</li>
        <li><strong>Clonar tablero</strong>: Crea una copia exacta del tablero actual, incluyendo todos sus widgets y configuraciones.</li>
        <li><strong>Vaciar tablero</strong>: Elimina todos los widgets del tablero actual manteniendo su configuración.</li>
        <li><strong>Eliminar tablero</strong>: Elimina completamente el tablero seleccionado (requiere confirmación).</li>
    </ul>
    <p>Estas operaciones están disponibles en el menú de opciones de la pantalla de tableros, y su disponibilidad depende de los permisos del usuario.</p>

    <h3 id="widgets">Widgets</h3>
    <p>Los widgets son los elementos visuales que se colocan en los tableros para mostrar información. Cada widget tiene un propósito específico y puede configurarse de manera
        independiente.</p>

    <h4 id="manipulacion-widgets">Manipulación de widgets</h4>
    <p>Los widgets ofrecen una gran flexibilidad en su uso:</p>
    <ul>
        <li><strong>Mover</strong>: Los widgets pueden arrastrarse libremente a cualquier posición dentro del tablero.</li>
        <li><strong>Redimensionar</strong>: Cada widget puede redimensionarse desde sus esquinas o bordes para adaptarse a las necesidades de visualización.</li>
        <li><strong>Editar</strong>: Al hacer doble clic sobre un widget, se abre un diálogo de configuración específico para ese tipo de widget.</li>
        <li><strong>Duplicar</strong>: Es posible duplicar widgets existentes, lo que resulta útil para crear múltiples visualizaciones del mismo tipo de datos con diferentes
            configuraciones.</li>
    </ul>
    <p>Desde la pantalla de configuración de un widget también es posible ajustar su tamaño mediante controles numéricos precisos, lo que permite un posicionamiento exacto.</p>

    <h4 id="tipos-widgets">Tipos de widgets</h4>
    <p>Los tableros pueden contener diferentes tipos de widgets para visualizar información:</p>
    <ul>
        <li><strong>Etiqueta de texto</strong>: Muestra texto estático personalizable.</li>
        <li><strong>Etiqueta de valor</strong>: Muestra un valor numérico con su unidad de medida.</li>
        <li><strong>Gauge</strong>: Muestra un medidor tipo velocímetro con rangos configurables.</li>
        <li><strong>Gauge porcentual</strong>: Muestra un medidor de porcentaje con indicador visual.</li>
        <li><strong>Gráfica</strong>: Muestra datos históricos en diferentes formatos visuales.</li>
        <li><strong>Últimos</strong>: Muestra los últimos N valores registrados en formato de lista.</li>
        <li><strong>Gráfica por Períodos</strong>: Muestra datos agrupados en períodos de tiempo, visualizando valores mínimos, máximos y medios para cada período.</li>
        <li><strong>Últimos por Períodos</strong>: Muestra datos agrupados en períodos de tiempo en formato de tabla, permitiendo ver los valores de cada período.</li>
    </ul>
    <p>Es posible tener múltiples widgets del mismo tipo en un tablero, por ejemplo, varias gráficas que muestren diferentes métricas o la misma métrica con diferentes
        configuraciones visuales.</p>

    <h4 id="configuracion-widgets">Configuración de widgets</h4>
    <p>Cada tipo de widget tiene su propio conjunto de parámetros configurables:</p>

    <h5>Configuración visual común a todos los widgets</h5>
    <ul>
        <li><strong>Color de texto</strong>: Permite establecer un color de texto específico para el widget, diferente al color por defecto del tablero.</li>
        <li><strong>Color de fondo</strong>: Permite establecer un color de fondo específico para el widget, diferente al color por defecto del tablero.</li>
        <li><strong>Color de borde</strong>: Permite establecer un color de borde específico para el widget, diferente al configurado en el tablero.</li>
        <li><strong>Botón "Usar valores por defecto"</strong>: Permite revertir cualquier configuración de color personalizada y volver a utilizar los valores por defecto definidos
            en el tablero.</li>
    </ul>
    <p>Cuando se realizan cambios en la configuración de colores del tablero, estos se aplican instantáneamente a todos los widgets que estén utilizando la configuración por
        defecto.</p>

    <h5>Etiqueta de texto</h5>
    <ul>
        <li><strong>Texto</strong>: El contenido textual que se mostrará en el widget.</li>
    </ul>

    <h5>Etiqueta de valor</h5>
    <ul>
        <li><strong>Valor a mostrar</strong>: Selección del tipo de dato a mostrar (temperatura, presión, humedad, etc.).</li>
    </ul>

    <h5>Gauge</h5>
    <ul>
        <li><strong>Tipo de gauge</strong>: Selección del tipo de medición (temperatura, presión, velocidad del viento, humedad).</li>
        <li><strong>Valores mínimo y máximo</strong>: Rango de valores para el medidor.</li>
        <li><strong>Unidad</strong>: Unidad de medida que se mostrará junto al valor.</li>
    </ul>

    <h5>Gauge porcentual</h5>
    <ul>
        <li><strong>Tipo de porcentaje</strong>: Selección del tipo de medición porcentual (batería, memoria, CPU, disco).</li>
    </ul>

    <h5>Gráfica</h5>
    <ul>
        <li><strong>Valor a mostrar</strong>: Selección del tipo de dato a visualizar.</li>
        <li><strong>Tipo de gráfica</strong>: Formato visual (línea, barras, circular, anillo, radar, área polar).</li>
        <li><strong>Número de puntos</strong>: Cantidad de puntos de datos históricos a mostrar (5-50).</li>
        <li><strong>Mostrar leyenda</strong>: Opción para mostrar u ocultar la leyenda de la gráfica.</li>
        <li><strong>Mostrar cuadrícula</strong>: Opción para mostrar u ocultar la cuadrícula de fondo.</li>
    </ul>

    <h5>Últimos</h5>
    <ul>
        <li><strong>Tipo de valor</strong>: Selección del tipo de dato a mostrar en la lista.</li>
        <li><strong>Número de valores</strong>: Cantidad de valores históricos a mostrar.</li>
        <li><strong>Incluir unidades</strong>: Opción para mostrar u ocultar las unidades de medida.</li>
        <li><strong>Mostrar hora completa</strong>: Opción para mostrar la fecha y hora completa o solo la hora.</li>
        <li><strong>Incluir minutos y segundos</strong>: Opción para mostrar u ocultar los minutos y segundos en la marca de tiempo.</li>
    </ul>

    <h5>Gráfica por Períodos</h5>
    <ul>
        <li><strong>Valor a mostrar</strong>: Selección del tipo de dato a visualizar (temperatura, presión, humedad, etc.).</li>
        <li><strong>Número de períodos</strong>: Cantidad de períodos de tiempo a mostrar (entre 3 y 30, por defecto 10).</li>
        <li><strong>Ancho del período</strong>: Duración de cada período en segundos (entre 5 y 60, por defecto 15).</li>
    </ul>
    <p>Este tipo de widget agrupa los datos en períodos de tiempo y muestra tres líneas en la gráfica: valores mínimos, valores máximos y valores medios para cada período. Incluye
        un botón para acceder a la lista completa de puntos de datos de todos los períodos.</p>
    <p>Características especiales:</p>
    <ul>
        <li>Al hacer doble clic sobre un punto de la gráfica, se muestra un popup con los detalles de ese período específico.</li>
        <li>Al hacer clic en el botón de datos, se abre un diálogo con dos pestañas:
            <ul>
                <li><strong>Resumen</strong>: Muestra una tabla con los valores mínimos, medios y máximos de todos los períodos.</li>
                <li><strong>Detalles</strong>: Permite seleccionar un período específico y ver todos los puntos de datos individuales que lo componen.</li>
            </ul>
        </li>
    </ul>

    <h5>Últimos por Períodos</h5>
    <ul>
        <li><strong>Valor a mostrar</strong>: Selección del tipo de dato a visualizar.</li>
        <li><strong>Número de períodos</strong>: Cantidad de períodos de tiempo a mostrar (entre 3 y 30, por defecto 10).</li>
        <li><strong>Ancho del período</strong>: Duración de cada período en segundos (entre 5 y 60, por defecto 15).</li>
        <li><strong>Incluir unidad</strong>: Opción para mostrar u ocultar las unidades de medida.</li>
    </ul>
    <p>Este widget muestra los mismos datos que la "Gráfica por Períodos" pero en formato de tabla, con columnas para los valores mínimos, medios y máximos de cada período. La
        tabla es ordenable haciendo clic en los encabezados de columna.</p>
    <p>Al hacer clic en una fila de la tabla, se abre un diálogo que muestra todos los puntos de datos individuales que componen ese período específico, permitiendo un análisis más
        detallado de los datos.</p>

    <h2 id="datos">Datos de la aplicación</h2>
    <p>iPRA Dashboard es una aplicación de demostración que utiliza datos aleatorios generados cada 5 segundos. Estos datos simulan información que en un entorno real
        provendría de una API REST.</p>
    <p>Los datos generados incluyen:</p>
    <ul>
        <li>Temperatura</li>
        <li>Presión</li>
        <li>Velocidad del viento</li>
        <li>Dirección del viento</li>
        <li>Humedad</li>
        <li>Precipitación</li>
        <li>Niveles de batería</li>
        <li>Uso de memoria</li>
        <li>Uso de CPU</li>
        <li>Uso de disco</li>
    </ul>
    <p>Estos datos se almacenan temporalmente en el navegador del usuario y se actualizan automáticamente, permitiendo ver cambios en tiempo real en los widgets.</p>

    <h2 id="funcionalidades-genericas">Funcionalidades genéricas</h2>
    <p>iPRA Dashboard incluye una serie de funcionalidades comunes a toda la aplicación que mejoran la experiencia de usuario y facilitan la interacción con el sistema.</p>

    <h3 id="atajos-teclado">Atajos de teclado y navegación</h3>
    <ul>
        <li>La tecla <strong>Escape</strong> cierra los diálogos y pantallas modales activas</li>
        <li>El botón <strong>Atrás</strong> del navegador:
            <ul>
                <li>Si hay un diálogo abierto, lo cierra sin cambiar de pantalla</li>
                <li>Si no hay diálogos abiertos, vuelve a la pantalla anterior</li>
            </ul>
        </li>
        <li>Se admite navegación por hash en la URL:
            <ul>
                <li>Permite acceder directamente a secciones específicas mediante URL</li>
                <li>Verifica automáticamente los permisos del usuario</li>
                <li>Comprueba que el usuario esté autenticado antes de mostrar cualquier contenido</li>
            </ul>
        </li>
    </ul>

    <h3 id="persistencia-sesion">Persistencia de sesión</h3>
    <ul>
        <li>La aplicación recuerda al usuario identificado entre sesiones</li>
        <li>No es necesario iniciar sesión cada vez que se accede a la aplicación</li>
        <li>La sesión se mantiene activa hasta que el usuario cierra sesión explícitamente</li>
        <li>Por seguridad, se verifica periódicamente la validez de la sesión</li>
    </ul>

    <h3 id="interaccion-dialogos">Interacción con diálogos</h3>
    <ul>
        <li>Todos los diálogos se pueden mover libremente por la pantalla</li>
        <li>Los diálogos mantienen su estado al cerrarlos y volverlos a abrir</li>
        <li>Los formularios dentro de diálogos validan los datos en tiempo real</li>
        <li>Los diálogos se adaptan automáticamente al tamaño de pantalla del dispositivo, manteniendo todas sus funcionalidades pero reorganizando sus elementos para una mejor
            experiencia de usuario</li>
        <li>En dispositivos móviles, los diálogos utilizan controles optimizados para pantallas táctiles</li>
        <li>En dispositivos de escritorio, los diálogos aprovechan el espacio adicional para mostrar más información y opciones de manera simultánea</li>
    </ul>

    <h3 id="gestion-listados">Gestión de listados</h3>
    <p>Los listados de entidades (empresas, usuarios, tableros) ofrecen funcionalidades avanzadas:</p>
    <ul>
        <li><strong>Exportación a CSV</strong>: Permite exportar los datos mostrados a un archivo CSV</li>
        <li><strong>Configuración de columnas</strong>:
            <ul>
                <li>Selección de columnas a mostrar/ocultar</li>
                <li>Ajuste del ancho de cada columna</li>
                <li>Reordenación de columnas mediante arrastrar y soltar</li>
            </ul>
        </li>
        <li><strong>Ordenación</strong>: Todos los listados permiten ordenar por cualquier columna visible</li>
        <li><strong>Opciones específicas</strong>:
            <ul>
                <li>Opciones a nivel de cabecera que afectan a todo el listado</li>
                <li>Opciones a nivel de fila que afectan solo al registro seleccionado</li>
            </ul>
        </li>
    </ul>

    <h3 id="interaccion-gestual">Interacción gestual</h3>
    <ul>
        <li>La aplicación soporta interacción gestual en dispositivos táctiles:
            <ul>
                <li>Deslizar horizontalmente para navegar entre registros</li>
                <li>Pellizcar para hacer zoom en gráficos y tableros</li>
                <li>Arrastrar para mover widgets en los tableros</li>
            </ul>
        </li>
        <li>La interacción gestual también funciona con ratón en ordenadores:
            <ul>
                <li>Clic y arrastrar simula el deslizamiento táctil</li>
                <li>La rueda del ratón permite hacer zoom</li>
                <li>Arrastrar y soltar para mover elementos</li>
            </ul>
        </li>
    </ul>

    <h2 id="compatibilidad">Compatibilidad con dispositivos</h2>
    <p>iPRA Dashboard está diseñada para funcionar en todo tipo de dispositivos:</p>
    <ul>
        <li><strong>Ordenadores de escritorio</strong>: Experiencia completa con todas las funcionalidades</li>
        <li><strong>Tablets</strong>: Interfaz adaptada al tamaño de pantalla</li>
        <li><strong>Smartphones</strong>: Interfaz optimizada para pantallas pequeñas</li>
    </ul>
    <p>En dispositivos móviles, si se carga un tablero diseñado para pantallas más grandes, la aplicación permite hacer scroll para ver todo el contenido.</p>
    <p>La aplicación se adapta automáticamente al tamaño de la pantalla, reorganizando elementos y ajustando controles para ofrecer la mejor experiencia posible en cada
        dispositivo.</p>

    <h2 id="conclusiones">Conclusiones</h2>
    <p>iPRA Dashboard representa una solución integral para la visualización y monitorización de datos en tiempo real, diseñada con un enfoque centrado en la experiencia del
        usuario. Cada aspecto de la aplicación ha sido cuidadosamente desarrollado para garantizar una interacción fluida, intuitiva y altamente personalizable.</p>

    <p>La plataforma destaca por su agilidad y facilidad de uso, permitiendo a usuarios de todos los niveles técnicos configurar potentes visualizaciones de datos sin necesidad de
        conocimientos especializados. La flexibilidad de sus tableros personalizados, combinada con la amplia variedad de widgets disponibles, ofrece posibilidades ilimitadas para
        adaptar la visualización a las necesidades específicas de cada empresa.</p>

    <p>Hemos implementado numerosas funcionalidades que anticipan las necesidades del usuario, creando una experiencia que parece "leer la mente" de quien utiliza la aplicación.
        Desde la persistencia automática de sesiones hasta la interacción gestual intuitiva, cada detalle ha sido diseñado para que el usuario pueda concentrarse en lo que
        realmente importa: sus datos.</p>

    <p>iPRA Dashboard es, en definitiva, la herramienta perfecta para quienes buscan una solución completa, flexible y fácil de usar para la monitorización de datos en tiempo
        real, adaptándose a cualquier entorno empresarial y evolucionando con las necesidades de sus usuarios.</p>
</body>

</html>