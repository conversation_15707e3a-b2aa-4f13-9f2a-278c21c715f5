/**
 * Constantes para clases CSS y selectores utilizados en la gestión de selección de widgets
 * @type {Object.<string, string>}
 */
const CSS_CLASSES = {
    SELECTION_MODE: 'selection-mode',
    WIDGET_SELECTOR: '.widget-selector',
    WIDGET: '.widget',
    WIDGET_PASTED: 'widget-pasted',
    SELECTED: 'selected'
};

/**
 * Clase que gestiona la selección, copia y pegado de widgets en el dashboard.
 * Proporciona funcionalidad para la manipulación y gestión del estado de selección
 * de los widgets, incluyendo operaciones de selección individual y múltiple.
 */
class DashboardSelectionManager {
    /**
     * @param {HTMLElement} dashboardElement - Elemento del DOM que contiene el dashboard
     */
    constructor(dashboardElement) {
        app.dashboardSelectionManager = this;
        this.dashboardElement = dashboardElement;
        this.widgetManager = widgetManager;

        // Variables para menús contextuales
        this.dashboardContextMenuElement = undefined;
        this.widgetContextMenuElement = undefined;
        this.widgetHasContextMenu = undefined;
        this.widgetsCopiados = undefined;

        this.initWidgetContextMenu();
    }

    /**
     * Inicializa el menú contextual de widgets
     */
    initWidgetContextMenu() {
        // Asegurarnos que el menú existe en el DOM
        let menu = document.getElementById('widget-context-menu');
        if (!menu) {
            menu = this.createWidgetContextMenu();
            this.widgetContextMenuElement = menu;
            document.body.appendChild(menu);
        }

        // Usar delegación de eventos
        app.on(this.dashboardElement, 'contextmenu', '.widget', (e) => {
            this.showWidgetContextMenu(e, menu);
        });

        this.initDashboardContextMenu();
    }

    /**
     * Inicializa el menú contextual del dashboard
     */
    initDashboardContextMenu() {
        // Asegurarnos que el menú existe en el DOM
        let menu = document.getElementById('dashboard-context-menu');
        if (!menu) {
            menu = this.createDashboardContextMenu();
            this.dashboardContextMenuElement = menu;
            document.body.appendChild(menu);
        }

        //Menú contextual del dashboard
        const dashboardContainer = document.querySelector('.dashboard-container');
        dashboardContainer.addEventListener('contextmenu', (event) => {
            if (!event.target.closest('.widget')) { // Solo si el click no es en un widget
                event.stopPropagation();
                event.preventDefault();
                this.showWidgetContextMenu(event, this.dashboardContextMenuElement);
            }
        });

        //Doble clic en el dashboard finaliza modo selección
        dashboardContainer.addEventListener('dblclick', (event) => {
            event.stopPropagation();
            event.preventDefault();
            this.desactivarModoSeleccion();
        });
    }

    /**
     * Crea el menú contextual para los widgets
     * @returns {HTMLElement} Elemento del menú contextual de widgets
     */
    createWidgetContextMenu() {
        const menu = document.createElement('div');
        menu.id = 'widget-context-menu';
        menu.className = 'dropdown-menu hidden';
        menu.dataset.remove = "N";

        menu.innerHTML = `
            <button data-accion="dashboardSelectionManager.toggleWidgetSelection">Seleccionar</button>
            <button data-accion="dashboardSelectionManager.copySelectedWidgets">Copiar</button>
            <button data-accion="dashboardSelectionManager.editWidget">Editar</button>
            <hr class="dropdown-divider">
            <button data-accion="dashboardSelectionManager.handleWidgetDeleteClick">Eliminar</button>
        `;
        return menu;
    }

    /**
     * Crea el menú contextual para el dashboard
     * @returns {HTMLElement} Elemento del menú contextual del dashboard
     */
    createDashboardContextMenu() {
        const menu = document.createElement('div');
        menu.id = 'dashboard-context-menu';
        menu.className = 'dropdown-menu hidden';
        menu.dataset.remove = "N";

        menu.innerHTML = `
                   <button data-accion="dashboardSelectionManager.toggleWidgetSelection">Modo Selección</button>
                   <button data-accion="dashboardSelectionManager.widgetSelectAll">Seleccionar Todo</button>
                   <button data-accion="dashboardSelectionManager.widgetSelectNone">Seleccionar Nada</button>
                   <hr class="dropdown-divider">
                   <button data-accion="dashboardSelectionManager.copySelectedWidgets">Copiar Selecs.</button>
                   <button data-accion="dashboardSelectionManager.pasteWidgets">Pegar</button>
                   <button data-accion="dashboardSelectionManager.handleWidgetDeleteSelecsClick">Eliminar Selecs.</button>
               `;

        return menu;
    }

    /**
     * Muestra el menú contextual en la posición adecuada
     * @param {Event} event - Evento que dispara la muestra del menú
     * @param {HTMLElement} menu - Elemento del menú a mostrar
     */
    showWidgetContextMenu(event, menu) {
        if (this.widgetContextMenuElement == menu) {
            closePopup(this.dashboardContextMenuElement);
            this.widgetHasContextMenu = event.target;
            //Si es el widget-content lo que tenemos
            if (!this.widgetHasContextMenu.classList.contains('widget'))
                this.widgetHasContextMenu = this.widgetHasContextMenu.closest('.widget');
        } else {
            closePopup(this.widgetContextMenuElement);
            this.widgetHasContextMenu = undefined;
        }


        const viewport = {
            width: window.innerWidth,
            height: window.innerHeight
        };

        let x = event.clientX;
        let y = event.clientY;

        const menuRect = menu.getBoundingClientRect();

        // Ajustar posición si se sale de la pantalla
        if (x + menuRect.width > viewport.width) {
            x = viewport.width - menuRect.width;
        }
        if (y + menuRect.height > viewport.height) {
            y = viewport.height - menuRect.height;
        }

        menu.style.left = `${x}px`;
        menu.style.top = `${y}px`;
        // menu.classList.remove('hidden');
        openPopup(menu);
    }

    /**
     * Devuelve true si el dashboard está en modo selección
     * @returns {boolean} Estado del modo selección
     */
    enModoSeleccion() {
        return document.body.classList.contains(CSS_CLASSES.SELECTION_MODE);
    }

    /**
     * Devuelve true si un widget está seleccionado
     * @param {HTMLElement} widget - Elemento widget a comprobar
     * @returns {boolean} Estado de selección del widget
     */
    widgetEnModoSeleccion(widget) {
        let selector = widget.querySelector(CSS_CLASSES.WIDGET_SELECTOR);
        if (selector) return selector.checked;
        return false;
    }

    /**
     * Activa el modo selección del dashboard
     */
    activarModoSeleccion() {
        if (!this.enModoSeleccion())
            document.body.classList.add(CSS_CLASSES.SELECTION_MODE);
    }

    /**
     * Desactiva el modo selección del dashboard
     */
    desactivarModoSeleccion() {
        if (this.enModoSeleccion())
            document.body.classList.remove(CSS_CLASSES.SELECTION_MODE);
    }

    /**
     * Cambia el estado de selección de un widget
     * @param {HTMLElement} widget - Widget a alternar su selección
     * @returns {boolean} Nuevo estado de selección
     */
    widgetToggleSelected(widget) {
        let selector = widget.querySelector(CSS_CLASSES.WIDGET_SELECTOR);
        if (selector)
            selector.checked = !selector.checked;
        return selector.checked;
    }

    /**
     * Selecciona o deselecciona un widget según el parámetro 'select'
     * @param {HTMLElement} widget - Widget a seleccionar/deseleccionar
     * @param {boolean} select - True para seleccionar, false para deseleccionar
     * @returns {boolean} Estado final de la selección
     */
    widgetSelect(widget, select) {
        let selector = widget.querySelector(CSS_CLASSES.WIDGET_SELECTOR);
        if (selector)
            selector.checked = select;
        return selector.checked;
    }

    /**
     * Opción para entrar/salir modo selección de widgets
     * Gestiona la selección tanto para widgets individuales como para el dashboard completo
     * @param {Event} ev - Evento que dispara la acción
     */
    toggleWidgetSelection(ev) {
        if (this.widgetHasContextMenu) {
            this.widgetToggleSelected(this.widgetHasContextMenu);
            this.activarModoSeleccion();
            return;
        }
        document.body.classList.toggle(CSS_CLASSES.SELECTION_MODE);
        if (!this.enModoSeleccion()) {
            const checkboxes = document.querySelectorAll(CSS_CLASSES.WIDGET_SELECTOR);
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            const widgets = document.querySelectorAll(CSS_CLASSES.WIDGET);
            widgets.forEach(widget => widget.classList.remove(CSS_CLASSES.SELECTED));
        }
    }



    /** Copia los widgets seleccionados en app.dashboardManager.widgetsCopiados */
    copySelectedWidgets(ev) {
        let selectedCheckboxes;
        let widget = undefined;
        if (!this.enModoSeleccion()) {
            widget = this.widgetHasContextMenu;
            //Si no hay widget la opción era el menú contextual del dashboard
            if (!widget) {
                showNotification('No está en modo SELECCIONAR', 'error');
                return;
            }
            this.widgetSelect(widget, true);
            selectedCheckboxes = [widget];
        } else {
            // Obtener todos los widgets seleccionados
            selectedCheckboxes = document.querySelectorAll('.widget-selector:checked');
            this.widgetHasContextMenu = undefined;
        }

        // Lista para almacenar los widgets
        const copiedWidgets = [];

        // Recorrer cada widget seleccionado
        selectedCheckboxes.forEach(checkbox => {
            const widget = checkbox.closest(CSS_CLASSES.WIDGET);
            const widgetId = parseInt(widget.dataset.widgetId); // Convertir el data-widget-id a entero

            // Obtener los datos del widget usando widgetManager
            const widgetData = widgetManager.getWidget(widgetId);
            if (widgetData) {
                copiedWidgets.push(widgetData);
            }
        });

        // Guardar los widgets copiados en dashboardManager
        app.dashboardManager.widgetsCopiados = copiedWidgets;
        showNotification(`Widgets copiados: ${copiedWidgets.length}`, 'info');

        //Si hay copiados y no hay widget es que había seleccs. Salir estado seleccionar
        if (copiedWidgets.length > 0 && !widget)
            this.toggleWidgetSelection(ev);
        //Copiar en un widget lo marca como seleccionado, lo copia y activa modo selección por si se quieren seleccionar más
        else if (widget)
            this.activarModoSeleccion();
    }

    /** Pega los widgets copiados */
    pasteWidgets(ev) {
        // Verificar si hay widgets copiados
        if (!app.dashboardManager.widgetsCopiados || app.dashboardManager.widgetsCopiados.length === 0) {
            showNotification('No hay widgets copiados', 'error');
            return; // No hay nada que pegar
        }

        // Actualizar el nextWidgetId antes de empezar
        widgetManager.updateNextWidgetId();
        let currentId = widgetManager.nextWidgetId;

        // Crear copias de los widgets con nuevos IDs
        const widgetsToPaste = app.dashboardManager.widgetsCopiados.map(widget => {
            // Crear una copia profunda del widget
            const widgetCopy = JSON.parse(app.stringify(widget));
            // Asignar nuevo ID
            widgetCopy.id = currentId++;
            return widgetCopy;
        });

        // Actualizar el nextWidgetId con el último ID usado
        widgetManager.nextWidgetId = currentId;

        // Añadir los widgets copiados al dashboard actual
        app.dashboardManager.dashboard.widgets.push(...widgetsToPaste);

        //Render los widgets del dashboard
        widgetManager.renderWidgets(this.dashboardElement);

        this.activarModoSeleccion();

        widgetsToPaste.forEach(widget => {
            const widgetElement = document.querySelector(`#widget-${widget.id}`);
            if (widgetElement) {
                // Activar el checkbox de selección
                const checkbox = widgetElement.querySelector('input[type="checkbox"]');
                if (checkbox) checkbox.checked = true;

                // Añadir clase para el efecto visual
                widgetElement.classList.add(CSS_CLASSES.WIDGET_PASTED);
                // Remover la clase después de la animación
                widgetElement.addEventListener('animationend', () => {
                    widgetElement.classList.remove('widget-pasted');
                }, {
                    once: true
                });

            }
        });
    }

    /**
     * Selecciona todos los widgets del dashboard
     */
    widgetSelectAll() {
        const widgets = this.dashboardElement.querySelectorAll(CSS_CLASSES.WIDGET);
        widgets.forEach(widget => this.widgetSelect(widget, true));
        this.activarModoSeleccion();
    }

    /**
     * Deselecciona todos los widgets del dashboard
     */
    widgetSelectNone() {
        const widgets = this.dashboardElement.querySelectorAll(CSS_CLASSES.WIDGET);
        widgets.forEach(widget => this.widgetSelect(widget, false));
        this.desactivarModoSeleccion();
    }

    /** Elimina el widget sobre el que se usa la opción*/
    handleWidgetDeleteClick(e) {
        // Si no hay seleccionados, mantener la lógica original para un solo widget
        let widgetElement;
        let widgetId;
        if (this.widgetHasContextMenu) {
            widgetElement = this.widgetHasContextMenu;
        } else {
            if (!this.dashboardElement.classList.contains('delete-mode')) return;
            widgetElement = e.currentTarget;
        }

        widgetId = parseInt(widgetElement.dataset.widgetId);
        if (!widgetId) return;

        dialogManager.confirmDelete('¿Estás seguro de que deseas eliminar este widget?').then(confirmed => {
            if (confirmed) {
                widgetManager.deleteWidget(widgetId);
                widgetElement.remove();
            }
        });
    }

    /** Elimina los widgets seleccionados*/
    handleWidgetDeleteSelecsClick(e) {
        if (!this.enModoSeleccion()) {
            showNotification('No está en modo selección', 'error');
            return;
        }
        const selectedWidgets = this.dashboardElement.querySelectorAll('.widget-selector:checked');

        // Si hay widgets seleccionados, eliminarlos
        if (selectedWidgets.length > 0) {
            dialogManager.confirmDelete('¿Estás seguro de que deseas eliminar los ' + selectedWidgets.length + ' widgets seleccionados?').then(confirmed => {
                if (confirmed) {
                    selectedWidgets.forEach(selected => {
                        let widget = selected.closest('.widget');
                        const widgetId = parseInt(widget.dataset.widgetId);
                        widgetManager.deleteWidget(widgetId);
                        widget.remove();
                    });
                }
            });
        } else {
            showNotification('No hay ningún widget seleccionado', 'error');
        }
    }

    /** Abre el diálogo de edición del widget actual */
    editWidget(ev) {
        if (this.widgetHasContextMenu) {
            app.dashboardManager.openWidgetEditDialog(parseInt(this.widgetHasContextMenu.dataset.widgetId));
        } else showNotification('Ningún widget a editar', 'error');
    }
}