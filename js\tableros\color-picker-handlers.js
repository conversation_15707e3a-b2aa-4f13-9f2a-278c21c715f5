/**
 * Manejadores de eventos para los selectores de color en el diálogo de edición de widgets
 * Este archivo contiene las funciones necesarias para manejar los eventos de cambio
 * en los inputs de color y actualizar correctamente el dataset.value
 */

/**
 * Inicializa los manejadores de eventos para los selectores de color
 * Esta función debe ser llamada cuando se carga la página o se crea un nuevo diálogo
 * @param {HTMLElement} container - Contenedor donde buscar los inputs (opcional)
 */
function initColorPickerHandlers(container = null) {
    // Verificar si el contenedor es un elemento DOM válido con querySelector
    const isValidContainer = container && typeof container.querySelector === 'function';

    // Registrar advertencia si el contenedor no es válido pero no es null
    if (container && !isValidContainer) {
        console.warn('initColorPickerHandlers: El contenedor proporcionado no es un elemento DOM válido', container);
    }

    // Función auxiliar para obtener elementos según el contexto
    const getElement = (selector) => {
        if (isValidContainer) {
            return container.querySelector(selector);
        } else {
            return document.querySelector(selector);
        }
    };

    // Obtener los inputs de color usando los IDs originales
    const bgColorInput = getElement('#edit-widget-bg-color');
    const textColorInput = getElement('#edit-widget-text-color');
    const borderColorInput = getElement('#edit-widget-border-color');

    console.log('Inicializando manejadores de color para:', {
        container: container,
        bgColorInput: bgColorInput,
        textColorInput: textColorInput,
        borderColorInput: borderColorInput
    });

    // Configurar event listeners para los inputs de color
    if (bgColorInput) {
        // Eliminar manejadores existentes para evitar duplicados
        bgColorInput.removeEventListener('input', handleColorChange);
        bgColorInput.removeEventListener('change', handleColorChange);

        // Añadir nuevos manejadores
        bgColorInput.addEventListener('input', handleColorChange);
        bgColorInput.addEventListener('change', handleColorChange);

        console.log('Manejador de eventos configurado para color de fondo:', bgColorInput);
    }

    if (textColorInput) {
        // Eliminar manejadores existentes para evitar duplicados
        textColorInput.removeEventListener('input', handleColorChange);
        textColorInput.removeEventListener('change', handleColorChange);

        // Añadir nuevos manejadores
        textColorInput.addEventListener('input', handleColorChange);
        textColorInput.addEventListener('change', handleColorChange);

        console.log('Manejador de eventos configurado para color de texto:', textColorInput);
    }

    if (borderColorInput) {
        // Eliminar manejadores existentes para evitar duplicados
        borderColorInput.removeEventListener('input', handleColorChange);
        borderColorInput.removeEventListener('change', handleColorChange);

        // Añadir nuevos manejadores
        borderColorInput.addEventListener('input', handleColorChange);
        borderColorInput.addEventListener('change', handleColorChange);

        console.log('Manejador de eventos configurado para color de borde:', borderColorInput);
    }

    console.log('Manejadores de eventos para selectores de color inicializados');
}

/**
 * Maneja el evento de cambio de color
 * @param {Event} e - Evento de cambio
 */
function handleColorChange(e) {
    const input = e.target;

    console.log('Evento de cambio de color detectado:', e.type);
    console.log('Input que cambió:', input.id);
    console.log('Valor anterior:', input.dataset.value);
    console.log('Nuevo valor:', input.value);

    // Cuando el usuario cambia el color manualmente, ya no es el color por defecto
    input.dataset.defecto = 'false';
    input.dataset.reset = 'false';

    // Guardar el valor actual en el dataset para usarlo al guardar
    input.dataset.value = input.value;

    // Información detallada para depuración
    console.log(`Color cambiado: ${input.id} = ${input.value}`);
    console.log(`Dataset actualizado: defecto=${input.dataset.defecto}, reset=${input.dataset.reset}, value=${input.dataset.value}`);

    // Verificar si el input está dentro de un formulario
    const form = input.closest('form');
    if (form) {
        console.log('Formulario padre:', form.id);
    } else {
        console.log('No se encontró un formulario padre');
    }

    // Verificar si el input está dentro de un modal
    const modal = input.closest('.modal');
    if (modal) {
        console.log('Modal padre:', modal.id);

        // Verificar si es el modal de edición de widget
        if (modal.id.includes('edit-widget-modal')) {
            console.log('Es un modal de edición de widget');
        }
    } else {
        console.log('No se encontró un modal padre');
    }
}

/**
 * Resetea los valores de dataset de los inputs de color
 * Esta función debe ser llamada antes de abrir el diálogo de edición
 * @param {HTMLElement} container - Contenedor donde buscar los inputs (opcional)
 */
function resetColorInputsDataset(container = null) {
    console.log('Reseteando dataset de inputs de color. Contenedor:', container);

    // Verificar si el contenedor es un elemento DOM válido con querySelector
    const isValidContainer = container && typeof container.querySelector === 'function';

    // Registrar advertencia si el contenedor no es válido pero no es null
    if (container && !isValidContainer) {
        console.warn('resetColorInputsDataset: El contenedor proporcionado no es un elemento DOM válido', container);
    }

    // Función auxiliar para obtener elementos según el contexto
    const getElement = (selector) => {
        if (isValidContainer) {
            return container.querySelector(selector);
        } else {
            return document.querySelector(selector);
        }
    };

    // Obtener los inputs de color usando los IDs originales
    const bgColorInput = getElement('#edit-widget-bg-color');
    const textColorInput = getElement('#edit-widget-text-color');
    const borderColorInput = getElement('#edit-widget-border-color');

    console.log('Inputs de color encontrados:', {
        bgColorInput: bgColorInput,
        textColorInput: textColorInput,
        borderColorInput: borderColorInput
    });

    // Resetear los atributos data-reset y data-defecto
    if (bgColorInput) {
        // Guardar valores anteriores para depuración
        const prevReset = bgColorInput.dataset.reset;
        const prevDefecto = bgColorInput.dataset.defecto;
        const prevValue = bgColorInput.dataset.value;

        // Establecer nuevos valores
        bgColorInput.dataset.reset = 'false';
        bgColorInput.dataset.defecto = 'false';
        bgColorInput.dataset.value = '';

        console.log('Reset bgColorInput:', {
            id: bgColorInput.id,
            prevReset: prevReset,
            prevDefecto: prevDefecto,
            prevValue: prevValue,
            newReset: bgColorInput.dataset.reset,
            newDefecto: bgColorInput.dataset.defecto,
            newValue: bgColorInput.dataset.value
        });
    }

    if (textColorInput) {
        // Guardar valores anteriores para depuración
        const prevReset = textColorInput.dataset.reset;
        const prevDefecto = textColorInput.dataset.defecto;
        const prevValue = textColorInput.dataset.value;

        // Establecer nuevos valores
        textColorInput.dataset.reset = 'false';
        textColorInput.dataset.defecto = 'false';
        textColorInput.dataset.value = '';

        console.log('Reset textColorInput:', {
            id: textColorInput.id,
            prevReset: prevReset,
            prevDefecto: prevDefecto,
            prevValue: prevValue,
            newReset: textColorInput.dataset.reset,
            newDefecto: textColorInput.dataset.defecto,
            newValue: textColorInput.dataset.value
        });
    }

    if (borderColorInput) {
        // Guardar valores anteriores para depuración
        const prevReset = borderColorInput.dataset.reset;
        const prevDefecto = borderColorInput.dataset.defecto;
        const prevValue = borderColorInput.dataset.value;

        // Establecer nuevos valores
        borderColorInput.dataset.reset = 'false';
        borderColorInput.dataset.defecto = 'false';
        borderColorInput.dataset.value = '';

        console.log('Reset borderColorInput:', {
            id: borderColorInput.id,
            prevReset: prevReset,
            prevDefecto: prevDefecto,
            prevValue: prevValue,
            newReset: borderColorInput.dataset.reset,
            newDefecto: borderColorInput.dataset.defecto,
            newValue: borderColorInput.dataset.value
        });
    }
}

// Ya no inicializamos los manejadores al cargar la página
// Los manejadores se inicializan cuando se crea cada formulario de edición de widgets