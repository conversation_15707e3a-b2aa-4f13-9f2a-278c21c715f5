/* IPRA_I - Estilos combinados y minificados */
/* Generado: 2025-06-03T14:52:14.611Z */


/* === css/styles.css === */
/* Estilos base - Independientes del tema */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Clase para ocultar elementos */
.hidden {
    display: none !important;
}

[data-accion] {
    cursor: pointer;
}

/** En los encabezados, dealnte del título, la flecha para ir atrás*/
.tit_ico_atras {
    padding: 0px 6px;
    transition: all 0.4s ease;
    cursor: pointer;
    background-color: transparent;
    color: var(--accent-color);
    position: relative;
    overflow: hidden;

    border: 1px dotted var(--border-color);
    border-radius: 8px;
}

.tit_ico_atras:hover {
    background-color: rgba(0, 255, 255, 0.1);
    box-shadow: var(--glow-effect);
    border-radius: 50%;
}

/* Estructura básica */
.container {
    max-width: calc(100vw - 4em);
    max-height: calc(100vh - 2em);
    margin: auto;
    padding: 20px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    overflow: hidden;
}

/* Excepción para el contenedor de entidades */
#entity-management-container {
    z-index: 1;
    /* Asegurar que esté por debajo de los botones de navegación */
    display: flex;
    flex-direction: column;
    height: calc(100vh - 2em);
}

/* Sistema de temas */
:root {
    /* Variables base que serán sobrescritas por cada tema */
    --primary-color: #4CAF50;
    --secondary-color: #45a049;
    --accent-color: #4CAF50;
    --background-color: #f5f5f5;
    --surface-color: #ffffff;
    --text-color: #333333;
    --text-secondary-color: #555555;
    --border-color: #dddddd;
    --success-color: #4CAF50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --font-family: Arial, sans-serif;
    --widget-text-color: #333333;
    --widget-bg-color: #ffffff;
}

/* Por defecto, aplicamos el tema default */
:root {
    font-family: var(--font-family);
}

/* Evitar selección de texto excepto en áreas permitidas */
body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
img,
button,
label,
div,
span,
a,
ul,
ol,
li,
header,
footer,
nav,
aside,
section,
article,
figure,
figcaption,
blockquote {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

input[type="text"],
input[type="password"],
textarea,
td {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* Estilos para el login */
#login-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 60vh;
}

.login-form {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 350px;
}

.login-form h2 {
    margin-bottom: 20px;
    text-align: center;
}

.form-group {
    margin-bottom: 12px;
    /* Reducido de 15px a 12px */
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input:not([type="checkbox"]),
.form-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.form-group input[readonly] {
    background-color: var(--background-color) !important;
    color: gray !important;
}

/* Estilos para la fila de cabecera del widget con acciones */
.widget-header-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    /* Reducido de 10px a 6px */
    flex-wrap: nowrap;
}

.widget-type-label {
    margin-bottom: 0 !important;
    white-space: nowrap;
    flex: 1;
}

.widget-actions-container {
    display: flex;
    gap: 8px;
    margin-left: 10px;
}

/* Estilos para la fila del selector de tipo de widget */
.widget-type-row {
    margin-bottom: 10px;
    /* Reducido de 15px a 10px */
    padding-left: 0;
}

.widget-type-select {
    width: 100%;
}

.icon-action-btn {
    width: 28px;
    height: 28px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    font-size: 14px;
}

.primary-icon-btn {
    background-color: #4CAF50;
    color: white;
}

.secondary-icon-btn {
    background-color: #2196F3;
    color: white;
}

.icon-action-btn.danger-icon-btn {
    color: var(--error-color);
    border-color: var(--error-color);
    background-color: var(----background-color);
}

.icon-action-btn.danger-icon-btn:hover {
    background-color: rgba(255, 51, 102, 0.2);
    box-shadow: 0 0 10px rgba(255, 51, 102, 0.7);
}

.action-icon {
    font-size: 16px;
}

.danger-icon-btn:hover {
    background-color: #f44336;
    color: white;
}

.checkbox-group {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 10px;
}

.checkbox-group label {
    font-weight: normal;
    margin-bottom: 0;
}

button {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

button:hover {
    background-color: #45a049;
}

.error-message {
    color: red;
    margin-top: 10px;
    text-align: center;
}

/* Estilos para el header */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #ddd;
    margin-bottom: 10px;
}

/* Ajuste específico para la cabecera de entidades */
#entity-management-container header {
    margin-bottom: 5px;
    padding-top: 5px;
    /* Reducir el padding superior */
}

#entity-management-container .header-title-container {
    margin-top: -5px;
    /* Subir el título */
}

.header-title-container {
    display: flex;
    flex-direction: column;
    margin-left: 20px;
}

header h1 {
    padding-left: 10px;
    cursor: default;
    margin-bottom: 5px;
}

.dashboard-quick-actions {
    display: flex;
    gap: 10px;
    padding-left: 10px;
    margin-top: 2px;
}

.icon-button {
    background: none;
    border: none;
    font-size: 16px;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-color: #f0f0f0;
    color: #333;
    transition: all 0.2s ease;
    padding: 0;
    z-index: 5;
    /* Asegurar que esté por encima de otros elementos */
}

.icon-button:hover {
    background-color: #e0e0e0;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.icon-button span {
    display: inline-block;
    line-height: 1;
}

.icon-switch {
    font-weight: bold;
}

.icon-new {
    font-size: 18px;
    font-weight: bold;
}

.icon-add-widget {
    font-size: 14px;
}

.menu-container {
    position: relative;
}

/* Estilos para el botón de menú móvil */
.mobile-menu-btn {
    display: none;
    /* Por defecto oculto, se mostrará en móviles */
    width: 40px;
    height: 30px;
    /* Altura reducida a 30px */
    background-color: #4CAF50;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    color: white;
    /* Color del icono */
    font-size: 24px;
    /* Tamaño del icono */
    justify-content: center;
    align-items: center;
    line-height: 1;
    /* Ajustar línea base para centrar verticalmente */
    padding: 0;
    /* Eliminar padding para mejor control */
    position: relative;
    /* Para posicionar el contenido */
}

/* Animación para el icono de hamburguesa */
.hamburger-icon {
    display: inline-block;
    transform: rotate(0deg);
    /* Posición inicial */
    transition: transform 0.3s ease;
    /* Transición suave para la rotación */
}

/* Clase para la animación de apertura del menú */
.hamburger-icon.menu-opening {
    transform: rotate(90deg);
    /* Rotación cuando el menú se está abriendo */
}

/* Clase para la animación de cierre del menú */
.hamburger-icon.menu-closing {
    transform: rotate(0deg);
    /* Volver a la posición original */
}

.dropdown-menu {
    position: absolute;
    right: 0;
    top: 40px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
    width: fit-content;
    /* Ancho automático en escritorio */
    min-width: 200px;
    /* Ancho mínimo para asegurar legibilidad */
    transform-origin: top right;
    transition: transform 0.3s ease, opacity 0.3s ease;
    opacity: 1;
    transform: translateX(0);
}

/* Animación para mostrar el menú */
.dropdown-menu.menu-entering {
    animation: slideIn 0.3s ease forwards;
}

/* Animación para ocultar el menú */
.dropdown-menu.menu-exiting {
    animation: slideOut 0.3s ease forwards;
}

/* Menú oculto */
.dropdown-menu.hidden {
    display: none;
}

/* Keyframes para la animación de entrada */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Keyframes para la animación de salida */
@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }

    to {
        opacity: 0;
        transform: translateX(20px);
    }
}

.dropdown-menu button {
    display: block;
    width: 100%;
    text-align: left;
    padding: 10px 15px;
    background-color: transparent;
    color: #333;
    border: none;
    border-bottom: 1px solid #eee;
}

.dropdown-menu button:last-child {
    border-bottom: none;
}

.dropdown-menu button:hover {
    background-color: #f5f5f5;
}

.dropdown-menu .danger-option {
    color: #f44336;
    font-weight: bold;
}

.dropdown-menu .danger-option:hover {
    background-color: #ffebee;
}

/* Estilos para el menú principal */
.main-menu-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 70vh;
    padding: 20px;
}

.main-menu {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
    width: 100%;
    max-width: 600px;
}

.menu-button {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 15px 20px;
    font-size: 18px;
    border-radius: 8px;
    background-color: var(--surface-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    cursor: pointer;
    /* Estilos adicionales para los divs */
    box-sizing: border-box;
    user-select: none;
    outline: none;
}

.menu-button .icon {
    margin-right: 15px;
    font-size: 24px;
}

.logout-btn {
    background-color: transparent;
    color: var(--text-color);
    border: none;
    padding: 8px 15px;
    cursor: pointer;
    font-size: 14px;
}

.back-btn {
    background-color: transparent;
    color: var(--text-color);
    border: none;
    padding: 8px 15px;
    cursor: pointer;
    font-size: 14px;
}

/* Estilos para la gestión de entidades */
.entity-container {
    padding: 20px;
    max-width: 100%;
}

.entity-count {
    font-size: 14px;
    color: var(--text-secondary-color);
    margin-left: 10px;
}

.entity-filter-container {
    display: flex;
    margin-bottom: 20px;
    position: relative;
}

.entity-filter {
    flex: 1;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 16px;
}

.clear-filter-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    font-size: 20px;
    color: var(--text-secondary-color);
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.entity-table-container {
    overflow-x: auto;
    width: 100%;
}

.entity-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.entity-table th,
.entity-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.entity-table th {
    background-color: var(--surface-color);
    font-weight: bold;
    position: relative;
    cursor: pointer;
}

.entity-table th:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.entity-table th.sortable::after {
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 5px;
}

.entity-table th.sort-asc::after {
    content: '▲';
    font-size: 10px;
}

.entity-table th.sort-desc::after {
    content: '▼';
    font-size: 10px;
}

.entity-table tr:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.entity-table .checkbox-cell {
    width: 40px;
    text-align: center;
    padding-left: 0;
    padding-right: 0;
}

.entity-table .action-cell {
    width: 40px;
    text-align: center;
    vertical-align: middle;
    padding-left: 0;
    padding-right: 0;
}

.entity-table .action-button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 18px;
    padding: 5px;
    color: var(--text-color);
}

.entity-action-menu {
    position: absolute;
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    box-shadow: 0 2px 10px var(--shadow-color);
    z-index: 1100;
    /* Aumentado para asegurar que esté por encima de todo, incluyendo modales */
    min-width: 150px;
}

.entity-action-menu button {
    display: block;
    width: 100%;
    text-align: left;
    padding: 10px 15px;
    background: none;
    border: none;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
    cursor: pointer;
}

.entity-action-menu button:last-child {
    border-bottom: none;
}

.entity-action-menu button:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.entity-action-menu .danger-option {
    color: var(--error-color);
}

/* Estilos para el dashboard */
.dashboard {
    width: 800px;
    height: 600px;
    background-color: white;
    border: 1px solid #ddd;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

/* Contenedor del dashboard */
.dashboard-container {
    position: relative;
    margin: 0 auto;
    max-width: 100%;
    overflow-x: auto;
    /* Permitir scroll horizontal */
    overflow-y: visible;
    -webkit-overflow-scrolling: touch;
    /* Scroll suave en iOS */
}

/* Estilos para la cuadrícula */
:root {
    --grid-color: #dddddd;
}

/* Cuadrícula para el tema por defecto */
.dashboard.show-grid {
    background-image: linear-gradient(var(--grid-color) 1px, transparent 1px),
        linear-gradient(90deg, var(--grid-color) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Ocultar cuadrícula cuando no está activada */
.dashboard:not(.show-grid) {
    background-image: none !important;
}

/* Estilos para los widgets */
.widget {
    position: absolute !important;
    /* Forzar posición absoluta */
    background-color: var(--widget-bg-color, #f9f9f9);
    color: var(--widget-text-color, #333333);
    border: 0;
    border-radius: 4px;
    padding: 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: background-color 0.3s ease, border 0.3s ease, color 0.3s ease;
}

.show-widget-borders .widget {
    border: 1px solid #ddd;
}

.transparent-widgets .widget:not(.force-border) {
    background-color: transparent;
    box-shadow: none;
}

/* Asegurar que los widgets con borde forzado siempre muestren su borde */
.widget.force-border {
    border-width: 1px !important;
    border-style: solid !important;
}

/* Asegurar que los widgets con borde forzado no sean afectados por la transparencia */
.transparent-widgets .widget.force-border {
    opacity: 1 !important;
}

.widget-content {
    height: 100%;
    /* Ocupa todo el alto */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Estilos específicos para cada tipo de widget */
.text-widget .widget-content {
    font-size: 16px;
    text-align: center;
    color: var(--widget-text-color, #333333);
}

.value-widget .widget-content {
    font-size: 24px;
    font-weight: bold;
    color: var(--widget-text-color, #333333);
}

.gauge-widget .widget-content,
.percentage-gauge-widget .widget-content {
    position: relative;
}

.gauge-container {
    width: 100%;
    height: 100%;
    position: relative;
}

/* Estilos para los modales */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1203;
    /* Aumentado para asegurar que esté por encima de todo */
    overflow: hidden;
    /* Evitar scroll cuando el modal se arrastra */
}

.modal-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    width: 500px;
    max-width: 90%;
    max-height: 95vh;
    /* Aumentado de 90vh a 95vh */
    overflow-y: auto;
    position: fixed;
    /* Cambiado a fixed para mejor comportamiento al arrastrar */
    cursor: default;
    z-index: 1001;
    /* Asegurar que esté por encima del fondo del modal */
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    /* Sombra más pronunciada para destacar */
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    /* Reducido de 15px a 10px */
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
    /* Reducido de 10px a 8px */
}

/* Estilo para el encabezado de los modales para que sea reconocible como "agarradera" */
.modal-content h2,
.modal-header h2 {
    cursor: move;
    user-select: none;
    padding-right: 10px;
    margin-bottom: 0;
    margin-top: 0;
    /* Asegurar que no haya espacio extra arriba */
}

/* Estilo para elementos durante el arrastre */
.dragging {
    opacity: 0.95;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    transition: none;
    /* Desactivar transiciones durante el arrastre */
}

.close-btn {
    float: right;
    font-size: 24px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    border-radius: 50%;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.close-btn:hover {
    background-color: #f44336;
    color: white;
}

.widget-fields {
    margin-top: 15px;
}

/* Estilos para el modo de edición */
.edit-mode .widget {
    cursor: move;
}

.edit-mode .widget:hover {
    border: 2px dashed #4CAF50;
}

/* Manejador de redimensionamiento */
.widget .resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 20px;
    height: 20px;
    cursor: nwse-resize;
    background: transparent;
    z-index: 10;
    display: none;
    /* Oculto por defecto */
    touch-action: none;
    /* Prevenir scroll en dispositivos táctiles */
}

.widget .resize-handle::before {
    content: '';
    position: absolute;
    bottom: 3px;
    right: 3px;
    width: 10px;
    height: 10px;
    border-right: 2px solid #aaa;
    border-bottom: 2px solid #aaa;
    opacity: 0.7;
}

.edit-mode .widget .resize-handle {
    display: block;
    /* Visible en modo edición */
}

/* Ajustes para dispositivos táctiles */
.touch-device .edit-mode .widget .resize-handle {
    width: 30px;
    height: 30px;
}

.touch-device .edit-mode .widget .resize-handle::before {
    width: 15px;
    height: 15px;
    border-width: 3px;
}

/* Estilos para el modo de edición de widget */
.edit-widget-mode .widget {
    cursor: pointer;
}

.edit-widget-mode .widget:hover {
    border: 2px dashed #2196F3;
}

/* Estilos para los selectores de color */
.color-picker-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.reset-color-btn {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.reset-color-btn:hover {
    background-color: #e0e0e0;
}

/* Estilos para los botones en el diálogo de edición */
.button-group {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    align-items: center;
    /* Asegurar alineación vertical */
}

/* Estilos para los botones del diálogo de configurar tablero */
/* Excepción para los botones del diálogo de configurar tablero - FORZAR UNA ÚNICA FILA */
.dashboard-config-buttons {
    flex-direction: row !important;
    justify-content: space-between !important;
    gap: 5px !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}

.dashboard-config-buttons button {
    flex: 1 !important;
    padding: 0 !important;
    min-width: 0 !important;
    width: 32% !important;
    max-width: 32% !important;
    border-radius: 4px !important;
    height: 36px !important;
    margin: 0 !important;
}

/* Mostrar solo iconos en móvil */
.dashboard-config-buttons .btn-text {
    display: none !important;
}

.dashboard-config-buttons .btn-icon {
    display: block !important;
    font-size: 20px !important;
    line-height: 1 !important;
}

/* Asegurar que los botones tengan el contenido centrado */
.dashboard-config-buttons button {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

#dashboard-config-modal .form-group {
    margin-bottom: 12px;
}

#dashboard-config-modal .form-group {
    margin-bottom: 12px;
}

#dashboard-config-modal .form-group label {
    display: inline-block;
    margin-bottom: 5px;
    width: 45%;
}

#dashboard-config-modal .form-group input,
#dashboard-config-modal .form-group select {
    width: 49%;
}


/* Estilos para los iconos y texto en los botones */
.btn-text {
    display: inline-block;
}

.btn-icon {
    display: none;
    /* Oculto por defecto, visible solo en móvil */
}

/* Estilo para el botón de cancelar */
.cancel-btn {
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    flex: 1;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cancel-btn:hover {
    background-color: #e0e0e0;
}

.primary-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    flex: 1;
    margin-right: 10px;
    height: 40px;
    /* Altura fija para todos los botones */
    display: flex;
    align-items: center;
    justify-content: center;
}

.primary-btn:disabled {
    background-color: #a5d6a7;
    cursor: not-allowed;
    opacity: 0.7;
}

.secondary-btn {
    background-color: #2196F3;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    flex: 1;
    margin-right: 10px;
    height: 40px;
    /* Altura fija para todos los botones */
    display: flex;
    align-items: center;
    justify-content: center;
}

.danger-btn {
    background-color: #f44336;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    flex: 1;
    height: 40px;
    /* Altura fija para todos los botones */
    display: flex;
    align-items: center;
    justify-content: center;
}

.primary-btn:hover {
    background-color: #45a049;
}

.secondary-btn:hover {
    background-color: #0b7dda;
}

.danger-btn:hover {
    background-color: #d32f2f;
}

/* Estilos para el popup de opciones de tablero */
.popup-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 200;
    /* Mayor que los modales normales */
}

.popup-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    width: 300px;
    max-width: 90%;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
}

.popup-header h3 {
    margin: 0;
    padding: 0;
    text-align: center;
    flex: 1;
}

.popup-content h3:not(.popup-header h3) {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    text-align: center;
}

.popup-option {
    display: block;
    width: 100%;
    text-align: left;
    padding: 12px 15px;
    margin-bottom: 8px;
    background-color: #f5f5f5;
    color: #333;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.popup-option:hover {
    background-color: #e0e0e0;
    /* transform: translateY(-2px);*/
}

.popup-option.danger-option {
    color: #f44336;
}

.popup-option.danger-option:hover {
    background-color: #ffebee;
}

.popup-option.selected {
    background-color: #bbdefb;
    border-left: 4px solid #2196F3;
    /*transform: translateY(-2px);*/
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(33, 150, 243, 0.3);
}

.popup-option.selected:hover {
    background-color: #90caf9;
    box-shadow: 0 3px 8px rgba(33, 150, 243, 0.4);
}

.delete-mode .widget:hover {
    border: 2px dashed red;
}

/* Estilos para el widget durante el arrastre */
.dragging {
    opacity: 0.8;
    cursor: move;
}

/* Estilos para los gauges */
.gauge {
    width: 100%;
    height: 100%;
    position: relative;
}

.gauge-value {
    position: absolute;
    bottom: 10px;
    width: 100%;
    text-align: center;
    font-weight: bold;
}

.gauge-arc {
    fill: none;
    stroke-width: 20;
}

.gauge-background {
    stroke: #eee;
}

.gauge-foreground {
    stroke: #4CAF50;
}

.percentage-gauge-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
}

.percentage-title {
    font-size: 16px;
    color: #555;
    margin-bottom: 5px;
}

.percentage-value {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 15px;
}

.percentage-gauge {
    width: 100%;
    height: 30px;
    background-color: #eee;
    border-radius: 15px;
    overflow: hidden;
    margin-top: 5px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.percentage-gauge-fill {
    height: 100%;
    background-color: #4CAF50;
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-size: 40px 40px;
    border-radius: 15px;
    transition: width 0.5s ease;
}

/* Estilos para el widget de últimos por períodos */
.latest-by-periods-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    text-align: center;
}

.latest-by-periods-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.latest-by-periods-table th,
.latest-by-periods-table td {
    padding: 5px;
    text-align: center;
    border-bottom: 1px solid #ddd;
}

.latest-by-periods-table th {
    background-color: rgba(0, 0, 0, 0.05);
    font-weight: bold;
    position: relative;
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;
}

.latest-by-periods-table th:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.latest-by-periods-table th::after {
    content: "";
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    opacity: 0.5;
}

.latest-by-periods-table tr:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

/* Estilos para la lista de tableros */
.dashboard-list {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 20px;
}

.dashboard-item {
    display: block;
    width: 100%;
    text-align: left;
    padding: 12px 15px;
    margin-bottom: 8px;
    background-color: #f5f5f5;
    color: #333;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dashboard-item:hover {
    background-color: #e0e0e0;
    transform: translateY(-2px);
}

.dashboard-item.current {
    background-color: #ffebee;
    color: #f44336;
    cursor: not-allowed;
}

.dashboard-item.current:hover {
    background-color: #ffebee;
    transform: none;
}

/* Estilos para el nombre de usuario en la lista de tableros */
.dashboard-name {
    display: block;
    font-weight: bold;
}

.dashboard-user {
    display: block;
    font-size: 0.85em;
    color: #666;
    margin-top: 2px;
}

/* Estilos para el botón de crear nuevo tablero */
.btn-primary {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.btn-primary:hover {
    background-color: #0069d9;
}

.btn-primary:active {
    background-color: #0062cc;
}

/* Media queries para dispositivos móviles */
@media (max-width: 768px) {
    .header-title-container {
        margin-left: 10px;
    }

    header h1 {
        font-size: 1.5rem;
        margin-bottom: 2px;
    }

    .dashboard-quick-actions {
        margin-top: 0;
    }

    .icon-button {
        width: 24px;
        height: 24px;
        font-size: 14px;
    }

    .icon-new {
        font-size: 16px;
    }

    .icon-add-widget {
        font-size: 12px;
    }
}


#dashboard-options-popup {
    z-index: 1200;
}

/* Estilos para el popup de datos de período */
.period-data-popup {
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.period-data-popup .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--background-color);
}

.period-data-popup .popup-header h3 {
    margin: 0;
    font-size: 16px;
    color: var(--text-color);
}

.period-data-popup .close-btn {
    cursor: pointer;
    font-size: 18px;
    color: var(--text-secondary-color);
}

.period-data-popup .close-btn:hover {
    color: var(--error-color);
}

.period-data-popup .popup-content {
    padding: 15px;
}

.period-data-popup p {
    margin: 0 0 10px 0;
    color: var(--text-color);
}

.period-data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.period-data-table th,
.period-data-table td {
    padding: 8px;
    text-align: center;
    border: 1px solid var(--border-color);
}

.period-data-table th {
    background-color: var(--background-color);
    color: var(--text-color);
    font-weight: bold;
}

.period-data-table td {
    color: var(--text-color);
}

/* Estilos para la tabla de puntos de datos */
.period-points-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 5px;
    font-size: 0.9em;
}

.period-points-table th,
.period-points-table td {
    padding: 6px;
    text-align: center;
    border: 1px solid var(--border-color);
}

.period-points-table th {
    background-color: var(--background-color);
    color: var(--text-color);
    font-weight: bold;
}

.period-points-table td {
    color: var(--text-color);
}

/* Estilos para las pestañas del popup */
.popup-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 15px;
}

.popup-tab {
    padding: 8px 15px;
    background-color: transparent;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    font-weight: normal;
    color: var(--text-secondary-color);
    transition: all 0.2s ease;
}

.popup-tab.active {
    border-bottom: 2px solid var(--accent-color);
    font-weight: bold;
    color: var(--text-color);
}

.popup-tab:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.period-selector {
    margin-bottom: 10px;
}

.period-selector select {
    padding: 5px;
    border-radius: 3px;
    border: 1px solid var(--border-color);
    background-color: var(--surface-color);
    color: var(--text-color);
}

.no-data-message {
    font-style: italic;
    color: var(--text-secondary-color);
    text-align: center;
    padding: 20px;
}

.icono-boton {
    max-width: min-content;
}

/* Estilo específico para grupos de formulario con inputs de tipo color */
.form-group.color-field-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
}

/* Estilo para la etiqueta del campo de color - ocupa el 50% del espacio */
.form-group.color-field-container label {
    flex: 0 0 50%;
    margin: 0;
    padding-right: 10px;
}

/* Contenedor para el color picker y el botón reset - ocupa el otro 50% */
.form-group.color-field-container .color-controls {
    display: flex;
    flex: 0 0 50%;
    align-items: center;
}

/* Estilo para el input de tipo color (selector de color) - se adapta al espacio disponible */
.form-group.color-field-container .color-controls input[type="color"] {
    flex: 1;
    min-width: 30px;
    height: 30px;
    padding: 0;
    border: 1px solid #ccc;
    cursor: pointer;
    background: none;
    vertical-align: middle;
    -webkit-appearance: none;
    appearance: none;
    margin-right: 10px;
}

/* Estilo para que se muestre correctamente en distintos navegadores */
.form-group.color-field-container .color-controls input[type="color"]::-webkit-color-swatch-wrapper {
    padding: 0;
}

.form-group.color-field-container .color-controls input[type="color"]::-webkit-color-swatch {
    border: none;
}

.form-group.color-field-container .color-controls input[type="color"]::-moz-color-swatch {
    border: none;
}

/* Estilo para el botón de reset */
.form-group.color-field-container .color-controls .reset-button {
    padding: 4px 8px;
    font-size: 12px;
    height: auto;
    cursor: pointer;
    white-space: nowrap;
}

/* Ajustes para temas específicos */
.theme-neumorphic .form-group.color-field-container .color-controls input[type="color"] {
    border-radius: 10px;
    box-shadow: var(--neumorphic-shadow);
}

.theme-tron .form-group.color-field-container .color-controls input[type="color"] {
    border: 1px solid var(--tron-border-color);
    box-shadow: 0 0 5px var(--tron-glow-color);
}

/* === css/styles2.css === */
  /* Animaciones para el cambio de tema */
  @keyframes fadeOut {
      from {
          opacity: 1;
      }

      to {
          opacity: 0.1;
      }
  }

  @keyframes fadeIn {
      from {
          opacity: 0.1;
      }

      to {
          opacity: 1;
      }
  }

  .theme-transition {
      animation-duration: 0.3s;
      animation-fill-mode: forwards;
  }

  .theme-fade-out {
      animation-name: fadeOut;
  }

  .theme-fade-in {
      animation-name: fadeIn;
  }

  /* Estilos para notificaciones */
  #notification-container {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 5000;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 10px;
  }

  .notification {
      padding: 12px 20px;
      border-radius: 4px;
      color: white;
      max-width: 300px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      transform: translateX(100%);
      opacity: 0;
  }

  .notification.show {
      animation: notificationShow 0.3s ease forwards;
  }

  .notification.hide {
      animation: notificationHide 0.3s ease forwards;
  }

  @keyframes notificationShow {
      from {
          transform: translateX(100%);
          opacity: 0;
      }

      to {
          transform: translateX(0);
          opacity: 1;
      }
  }

  @keyframes notificationHide {
      from {
          transform: translateX(0);
          opacity: 1;
      }

      to {
          transform: translateX(100%);
          opacity: 0;
      }
  }

  /* Animaciones para contenedores */
  .container {
      transition: none;
      /* Desactivar transiciones para usar animaciones */
  }

  .container.container-entering {
      animation: containerEnter 0.3s ease forwards;
  }

  .container.container-exiting {
      animation: containerExit 0.3s ease forwards;
  }

  @keyframes containerEnter {
      from {
          opacity: 0;
          transform: translate(-50%, -50%) scale(0.95);
      }

      to {
          opacity: 1;
          transform: translate(-50%, -50%) scale(1);
      }
  }

  @keyframes containerExit {
      from {
          opacity: 1;
          transform: translate(-50%, -50%) scale(1);
      }

      to {
          opacity: 0;
          transform: translate(-50%, -50%) scale(0.95);
      }
  }

  /* Animaciones para modales */
  .modal.modal-entering {
      animation: modalEnter 1.5s ease forwards;
  }

  .modal.modal-exiting {
      animation: modalExit 0.5s ease forwards;
  }

  @keyframes modalEnter {
      from {
          opacity: 0;
      }

      to {
          opacity: 1;
      }
  }

  @keyframes modalExit {
      from {
          opacity: 1;
      }

      to {
          opacity: 0;
      }
  }

  /* Ajuste para contenedores en móvil */
  @media (max-width: 768px) {
      @keyframes containerEnter {
          from {
              opacity: 0;
              transform: scale(0.95);
          }

          to {
              opacity: 1;
              transform: scale(1);
          }
      }

      @keyframes containerExit {
          from {
              opacity: 1;
              transform: scale(1);
          }

          to {
              opacity: 0;
              transform: scale(0.95);
          }
      }
  }

  .notification.info {
      background-color: #2196F3;
  }

  .notification.success {
      background-color: #4CAF50;
  }

  .notification.warning {
      background-color: #FF9800;
  }

  .notification.error {
      background-color: #F44336;
  }

  /* Estilos para diálogos */
  #dialog-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      animation: dialogFadeIn 0.3s ease forwards;
  }

  #dialog-container.dialog-hiding {
      animation: dialogFadeOut 0.3s ease forwards;
  }

  #dialog-container.hidden {
      display: none;
  }

  @keyframes dialogFadeIn {
      from {
          opacity: 0;
      }

      to {
          opacity: 1;
      }
  }

  @keyframes dialogFadeOut {
      from {
          opacity: 1;
      }

      to {
          opacity: 0;
      }
  }

  .dialog {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      animation: dialogFadeIn 0.3s ease forwards;
  }

  .dialog.dialog-exiting {
      animation: dialogFadeOut 0.3s ease forwards;
      /*   pointer-events: none;*/
      /* Prevent capturing mouse events during exit animation */
  }

  .dialog.hidden {
      display: none;
  }

  .dialog-content {
      background-color: white;
      border-radius: 8px;
      width: 400px;
      max-width: 90%;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
      position: fixed;
      overflow: hidden;
  }

  .dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      border-bottom: 1px solid #eee;
      cursor: move;
  }

  .dialog-header h3 {
      margin: 0;
      font-size: 18px;
  }

  .dialog-header .close-btn {
      font-size: 24px;
      line-height: 1;
      cursor: pointer;
      padding: 0 5px;
      transition: color 0.2s ease;
  }

  .dialog-header .close-btn:hover {
      color: #f44336;
  }

  .dialog-body {
      padding: 20px;
  }

  .dialog-body p {
      margin-top: 0;
      margin-bottom: 20px;
  }

  .dialog-content .button-group {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      padding: 0 20px 20px;
  }

  .dialog-content input[type="text"] {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 16px;
  }

  .dialog-content input[type="text"]:focus {
      outline: 2px solid var(--primary-color);
      border-color: transparent;
  }

  /* Estilos específicos para cada tipo de diálogo */
  .confirm-dialog .dialog-content {
      border-top: 4px solid var(--primary-color);
  }

  .alert-dialog .dialog-content {
      border-top: 4px solid var(--warning-color);
  }

  .prompt-dialog .dialog-content {
      border-top: 4px solid var(--info-color);
  }

  /* Estilos para el diálogo de confirmación de eliminación */
  .delete-confirm-dialog .dialog-content {
      border-top: 4px solid var(--error-color);
      background-color: rgba(var(--error-color-rgb, 244, 67, 54), 0.05);
  }

  .delete-confirm-dialog .dialog-header {
      background-color: rgba(var(--error-color-rgb, 244, 67, 54), 0.1);
      border-bottom: 1px solid rgba(var(--error-color-rgb, 244, 67, 54), 0.2);
  }

  .delete-confirm-dialog .dialog-header h3 {
      color: var(--error-color);
  }

  .delete-confirm-dailog .dialog-body p {
      text-shadow: 1px 1px #ff0808a3;
  }

  /* Estilos para el menú principal */
  #main-menu-container {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;
      max-width: 400px;
      height: 500px;
      flex-direction: column;
      margin-top: calc((100vh - 500px)/ 2);
      position: relative !important;
      transform: none !important;
      top: 0 !important;
      left: 0 !important;
  }

  #main-menu-container header {
      width: 100%;
  }

  /* Estilos para el main dentro del contenedor de entidades */
  #entity-management-container main {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
  }

  .main-menu-container {
      min-height: unset;
  }

  .main-menu {
      display: flex;
      flex-direction: column;
      gap: 20px;
      align-items: center;
      width: 100%;
      max-width: 600px;
      position: relative;
      /* Necesario para el posicionamiento absoluto del borde */
  }

  /* Estilo para checkboxes en formularios */
  .form-group input[type="checkbox"] {
      margin-right: 0;
      /* Quitamos el margen derecho porque ya lo añadimos en el label */
      cursor: pointer;
      width: auto;
      /* Evitar que tome el ancho del 100% */
  }

  /* Estilo para el contenedor de checkbox */
  .form-group .checkbox-container {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
  }

  /* Estilo para las etiquetas de checkbox */
  .form-group .checkbox-container label {
      margin-bottom: 0;
      cursor: pointer;
  }

  /* Estilo para los campos de tipo textarea en formularios de entidades */
  .form-group textarea {
      width: 100%;
      min-height: 100px;
      padding: 8px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      resize: vertical;
  }

  .menu-button {
      display: flex;
      align-items: center;
      width: 100%;
      padding: 15px 20px;
      font-size: 18px;
      border-radius: 8px;
      background-color: var(--surface-color);
      color: var(--text-color);
      border: 1px solid var(--border-color);
      cursor: pointer;
      /* Estilos adicionales para los divs */
      box-sizing: border-box;
      user-select: none;
      outline: none;
  }

  .menu-button .icon {
      margin-right: 15px;
      font-size: 24px;
  }

  /* Div con borde para el menú principal */
  .menu-border {
      position: absolute;
      width: 100%;
      height: 100%;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      pointer-events: none;
      /* Para que no interfiera con los clics */
      box-sizing: border-box;
      transition: top 0.3s ease;
      /* Transición suave para el movimiento vertical */
      z-index: 10;
      /* Alto para que quede por encima de las opciones */
      background-color: rgba(var(--primary-color-rgb, 76, 175, 80), 0.05);
      /* Fondo semi-transparente */
  }

  .logout-btn {
      background-color: transparent;
      color: var(--text-color);
      border: none;
      padding: 8px;
      cursor: pointer;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.3s ease;
  }

  .logout-btn:hover {
      background-color: rgba(var(--primary-color-rgb, 76, 175, 80), 0.1);
  }

  .logout-icon {
      font-size: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: transform 0.3s ease;
  }

  .logout-btn:hover .logout-icon {
      transform: rotate(90deg);
  }

  .back-btn {
      background-color: transparent;
      color: var(--text-color);
      border: none;
      padding: 8px 15px;
      cursor: pointer;
      font-size: 14px;
  }

  /* Estilos para la gestión de entidades */
  .entity-container {
      padding: 10px;
      padding-top: 0px;
      /* Eliminar el espacio superior */
      max-width: 100%;
      width: calc(100vw - 6em);
      /* Ancho para ordenador */
      display: flex;
      flex-direction: column;
      flex: 1;
      /* Tomar todo el espacio disponible */
      overflow: hidden;
      /* Evitar scroll en el contenedor principal */
  }

  .entity-count {
      font-size: 14px;
      color: var(--text-secondary-color);
      margin-left: 10px;
  }

  .entity-filter-container {
      display: flex;
      margin-bottom: 10px;
      position: relative;
      flex-shrink: 0;
      /* Evita que el filtro se encoja */
      z-index: 5;
      /* Subir el z-index para que esté por encima de otros elementos */
  }

  .entity-filter {
      flex: 1;
      padding: 10px;
      padding-right: 30px;
      /* Espacio para el botón de limpiar */
      border: none;
      border-radius: 4px;
      font-size: 16px;
      background-color: rgba(var(--primary-color-rgb, 76, 175, 80), 0.05);
      transition: border-color 0.2s ease;
  }

  .entity-filter:focus {
      border: 1px solid var(--border-color);
      outline: none;
  }

  .clear-filter-btn {
      position: absolute;
      right: 10px;
      top: 20px;
      background: none;
      border: none;
      font-size: 20px;
      color: var(--text-secondary-color);
      cursor: pointer;
      padding: 0;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1;
      /* Asegurar que esté por encima del input */
  }

  .entity-table-container {
      overflow-y: auto;
      /* Permitir scroll vertical */
      overflow-x: auto;
      /* Mantener scroll horizontal */
      width: 100%;
      flex-grow: 1;
      /* Tomar el espacio restante */
      min-height: 300px;
      /* Altura mínima para asegurar que se vea el scroll */
      max-height: calc(100vh - 225px);
      /* Altura máxima para asegurar que no se salga de la pantalla */
      scrollbar-width: thin;
      /* Barra de desplazamiento delgada en Firefox */
      scrollbar-color: var(--primary-color) transparent;
      /* Color de la barra de desplazamiento en Firefox */
      border: 1px solid var(--border-color);
      /* Borde para visualizar mejor el contenedor */
      padding-right: 5px;
      /* Espacio para la barra de scroll */
  }

  /* Estilos para barras de desplazamiento en WebKit */
  .entity-table-container::-webkit-scrollbar {
      width: 16px;
      height: 16px;
  }

  .entity-table-container::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.05);
      border-radius: 6px;
  }

  .entity-table-container::-webkit-scrollbar-thumb {
      background-color: var(--primary-color);
      border-radius: 6px;
      border: 2px solid transparent;
      background-clip: padding-box;
  }

  .entity-table-container::-webkit-scrollbar-thumb:hover {
      background-color: var(--secondary-color);
  }

  /* Estilos específicos para temas */
  .theme-tron .entity-table-container {
      scrollbar-color: var(--accent-color) rgba(0, 0, 0, 0.2);
  }

  .theme-tron .entity-table-container::-webkit-scrollbar-track {
      background: rgba(0, 162, 255, 0.1);
  }

  .theme-tron .entity-table-container::-webkit-scrollbar-thumb {
      background-color: var(--accent-color);
      box-shadow: 0 0 5px var(--glow-effect);
  }

  .theme-neumorphic .entity-table-container {
      scrollbar-color: var(--primary-color) var(--surface-color);
  }

  .theme-neumorphic .entity-table-container::-webkit-scrollbar-track {
      background: var(--surface-color);
      box-shadow: var(--shadow-inset);
  }

  .theme-neumorphic .entity-table-container::-webkit-scrollbar-thumb {
      background-color: var(--primary-color);
      box-shadow: var(--shadow-small);
  }

  .entity-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
      border: 2px solid var(--border-color);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      table-layout: fixed;
      /* Fijar el ancho de las columnas */
  }

  /* Ancho mínimo para las columnas (excepto las dos primeras) */
  .entity-table th:not(:nth-child(1)):not(:nth-child(2)),
  .entity-table td:not(:nth-child(1)):not(:nth-child(2)) {
      min-width: 70px;
      width: max-content;
  }

  .entity-table th {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid var(--border-color);
      border-right: 1px solid var(--border-color);
      overflow-wrap: normal;
      /* Solo romper por espacios en blanco */
      word-break: normal;
      /* Solo romper por espacios en blanco */
      white-space: normal;
      /* Permitir múltiples líneas */
  }

  .entity-table td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid var(--border-color);
      border-right: 1px solid var(--border-color);
      overflow-wrap: break-word;
      /* Permitir que el texto se rompa para evitar desbordamiento */
      word-break: break-word;
      /* Compatibilidad adicional */
  }

  .entity-table th:last-child,
  .entity-table td:last-child {
      border-right: none;
  }

  .entity-table th {
      background-color: var(--primary-color);
      color: white;
      font-weight: bold;
      position: relative;
      cursor: pointer;
      border-bottom: 2px solid var(--border-color);
  }

  .entity-table th:hover {
      background-color: var(--secondary-color);
  }

  .entity-table th.sortable::after {
      content: '';
      display: inline-block;
      width: 0;
      height: 0;
      margin-left: 5px;
  }

  .entity-table th.sort-asc::after {
      content: '▲';
      font-size: 10px;
  }

  .entity-table th.sort-desc::after {
      content: '▼';
      font-size: 10px;
  }

  .entity-table tr:nth-child(odd) {
      background-color: var(--surface-color);
  }

  .entity-table tr:nth-child(even) {
      background-color: rgba(0, 0, 0, 0.03);
  }

  .entity-table tr:hover {
      background-color: rgba(var(--primary-color-rgb, 76, 175, 80), 0.1);
  }

  .entity-table .checkbox-cell {
      width: 40px;
      text-align: center;
      vertical-align: middle;
      padding-left: 0;
      padding-right: 0;
  }

  .entity-table .action-cell {
      width: 40px;
      text-align: center;
      vertical-align: middle;
      padding-left: 0;
      padding-right: 0;
  }

  .entity-table .action-button {
      background: none;
      border: none;
      cursor: pointer;
      font-size: 18px;
      line-height: 1;
      padding: 0;
      margin: 0;
      height: 24px;
      width: 24px;
      border-radius: 3px;
      transition: all 0.2s ease;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      vertical-align: middle;
  }

  .entity-table .action-button:hover {
      background-color: rgba(0, 0, 0, 0.05);
  }

  .entity-action-menu {
      position: absolute;
      background-color: var(--surface-color);
      border: 1px solid var(--border-color);
      border-radius: 4px;
      box-shadow: 0 2px 10px var(--shadow-color);
      z-index: 1205;
      min-width: 150px;
  }

  .entity-action-menu button {
      display: block;
      width: 100%;
      text-align: left;
      padding: 10px 15px;
      background: none;
      border: none;
      border-bottom: 1px solid var(--border-color);
      color: var(--text-color);
      cursor: pointer;
  }

  .entity-action-menu button:last-child {
      border-bottom: none;
  }

  .entity-action-menu button:hover {
      background-color: var(--primary-color);
      color: white;
  }

  .entity-action-menu .danger-option {
      color: var(--error-color);
  }

  .entity-action-menu button.disabled {
      opacity: 0.5;
      cursor: not-allowed;
  }

  .entity-action-menu button.disabled:hover {
      background-color: var(--surface-color);
      color: var(--text-color);
  }

  .entity-action-menu .danger-option.disabled:hover {
      color: var(--error-color);
  }

  /* Estilos para navegación de entidades */
  .entity-nav-container {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
      padding: 5px 0;
  }

  .nav-button {
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 15px;
      font-size: 18px;
      cursor: pointer;
      transition: all 0.2s ease;
  }

  .nav-button:hover {
      background-color: var(--secondary-color);
      transform: scale(1.05);
  }

  .nav-button:disabled {
      background-color: #ccc;
      cursor: not-allowed;
      opacity: 0.6;
      transform: none;
  }

  /* Estilos para las flechas de navegación */
  .form-with-nav-container {
      position: relative;
      width: 100%;
  }

  /* Botones de navegación en escritorio */
  .side-nav-button {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }

  .side-nav-button.prev-button {
      left: -20px;
  }

  .side-nav-button.next-button {
      right: -20px;
  }

  /* Botones de navegación comunes */
  .nav-button {
      font-size: 16px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 38px;
  }

  /* Botones de icono */
  .icon-btn {
      min-width: 40px !important;
      width: auto !important;
      padding: 0 10px !important;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 38px !important;
      /* Altura fija para todos los botones */
  }

  /* Contenedor para botones de navegación en escritorio */
  .desktop-nav-container {
      display: flex;
      gap: 5px;
  }

  /* Botones de navegación en escritorio */
  .desktop-nav-button {
      min-width: 40px !important;
      width: auto !important;
      padding: 0 10px !important;
  }

  /* Botones de navegación en móvil */
  @media (max-width: 768px) {

      /* Ocultar botones laterales en móvil */
      .side-nav-button {
          display: none;
      }

      /* Mostrar botones de navegación en la barra de botones */
      .mobile-nav-button {
          font-size: 16px;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 38px;
      }

      /* Ajustar tamaño de botones en móvil */
      .button-group button {
          min-width: unset !important;
          padding: 8px 12px;
          font-size: 14px;
          height: 38px;
      }

      /* Ajustar el grupo de botones para que use flexbox */
      .button-group {
          display: flex !important;
          flex-direction: row !important;
          gap: 3px !important;
          width: 100%;
      }

      /* Filas de botones */
      .button-row {
          display: flex;
          width: 100%;
          gap: 5px;
      }

      /* Contenedor para botones de navegación */
      .nav-buttons-container {
          display: flex;
          gap: 5px;
          flex: 1;
      }

      /* Ajustar el botón primario */
      .button-group .primary-btn {
          height: 38px;
          display: flex;
          align-items: center;
          justify-content: center;
      }

      /* Ajustar el botón de opciones */
      #entity-options-btn {
          height: 38px;
          display: flex;
          align-items: center;
          justify-content: center;
      }

      /* Ajustar el botón de cancelar */
      #entity-cancel-btn {
          height: 38px;
          display: flex;
          align-items: center;
          justify-content: center;
      }
  }

  /* Estilos para escritorio */
  @media (min-width: 769px) {

      /* Ajustar el grupo de botones para escritorio */
      .button-group {
          display: flex !important;
          flex-direction: row !important;
          gap: 10px !important;
          width: 100%;
          justify-content: flex-end;
          align-items: center;
          /* Alinear verticalmente todos los botones */
      }

      /* Ajustar el botón primario para escritorio */
      .button-group .primary-btn {
          margin-right: auto;
          /* Empuja los otros botones hacia la derecha */
          height: 38px !important;
          /* Altura fija para todos los botones */
      }

      /* Ajustar todos los botones en escritorio para que tengan la misma altura */
      .button-group button {
          height: 38px !important;
          display: flex;
          align-items: center;
          justify-content: center;
      }

      /* Ajustar el botón de opciones en escritorio */
      #entity-options-btn {
          height: 38px !important;
          display: flex;
          align-items: center;
          justify-content: center;
      }

      /* Ajustar el botón de cancelar en escritorio */
      #entity-cancel-btn {
          height: 38px !important;
          display: flex;
          align-items: center;
          justify-content: center;
      }

      /* Ajustar el contenedor de navegación en escritorio */
      .desktop-nav-container {
          height: 38px !important;
          display: flex;
          align-items: center;
      }

      /* Ajustar el ancho del modal para escritorio */
      .modal-content {
          width: 600px;
          max-width: calc(100vw - 4em);
      }
  }

  /* Icono de hamburguesa para el botón de opciones */
  .hamburger-icon {
      font-size: 20px;
      transition: transform 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
  }

  .hamburger-icon.menu-opening {
      transform: rotate(90deg);
  }

  .hamburger-icon.menu-closing {
      transform: rotate(0deg);
  }

  /* Ajustes para el menú de opciones en móvil */
  @media (max-width: 768px) {
      .entity-options-menu {
          max-height: 80vh;
          overflow-y: auto;
          position: fixed;
          /* Cambiado de absolute a fixed para asegurar que esté por encima de todo */
          width: fit-content;
          max-width: 300px;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
          /* Asegurar que esté por encima del formulario */
          /* Se eliminó bottom: 100% para permitir posicionamiento dinámico */
          left: 0 !important;
          margin-bottom: 10px;
          /* Espacio entre el menú y el botón */
      }
  }

  /* Estilos para la configuración de columnas */
  .columns-container {
      padding: 10px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      margin-bottom: 20px;
  }

  /* Animaciones para modales */
  .modal {
      transition: none;
      /* Desactivar transiciones para usar animaciones */
  }

  .modal.modal-entering {
      animation: modalFadeIn 0.5s ease forwards;
  }

  .modal.modal-exiting {
      animation: modalFadeOut 0.5s ease forwards;
  }

  @keyframes modalFadeIn {
      from {
          opacity: 0;
      }

      to {
          opacity: 1;
      }
  }

  @keyframes modalFadeOut {
      from {
          opacity: 1;
      }

      to {
          opacity: 0;
      }
  }

  /* Estilos específicos para el modal de configuración de columnas */
  #column-config-modal .modal-content {
      width: 430px !important;
      /* Ancho fijo */
      overflow: hidden;
      /* Solo permitir scroll en el contenedor de columnas */
      display: flex;
      flex-direction: column;
  }

  #column-config-modal .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 5px;
      /* Reducir espacio superior */
      margin-bottom: 10px;
  }

  #column-config-modal .modal-header h2 {
      margin: 0;
  }

  #column-config-modal .close-btn {
      float: none;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
  }

  .column-item {
      display: flex;
      align-items: center;
      padding: 10px;
      border-bottom: 1px solid var(--border-color);
      cursor: grab;
      flex-wrap: wrap;
      /* Permitir que los elementos se envuelvan */
      row-gap: 5px;
  }

  .column-item:last-child {
      border-bottom: none;
  }

  .column-item.drag-over {
      background-color: rgba(var(--primary-color-rgb, 76, 175, 80), 0.1);
  }

  .column-item .drag-handle {
      margin-right: 10px;
      color: var(--text-secondary-color);
      cursor: grab;
  }

  .column-item input[type="checkbox"] {
      margin-right: 10px;
  }

  .column-item label {
      flex: 1;
      cursor: pointer;
      word-break: break-word;
      /* Permitir que el texto se rompa */
      white-space: normal;
      /* Permitir múltiples líneas */
  }

  /* Estilos para widget de gráfica */
  .chart-widget .widget-content {
      height: 100%;
      width: 100%;
      padding: 5px;
      box-sizing: border-box;
  }

  .chart-container {
      height: 100%;
      width: 100%;
      position: relative;
  }

  /* Asegurar que el contenedor de la gráfica ocupe todo el espacio disponible */
  .chart-widget .widget-content {
      display: flex;
      justify-content: center;
      align-items: center;
  }

  /* Ajustar el botón de login */
  #login-btn {
      width: 100%;
  }



  .modal {
      /* Estilos base para móvil vertical y ordenador */
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      max-width: 90%;
      max-height: 95vh;
      /* Aumentado de 90vh a 95vh */
      overflow: auto;
      /* Añade estos estilos adicionales según necesidades de diseño */
      /* width: auto;
      background: white;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
      */
      padding: 20px;
      border-radius: 8px;
  }

  @media (max-width: 767px) and (orientation: landscape) {
      .modal {
          /* Estilos específicos para móvil horizontal */
          height: 100vh;
          max-height: none;
          top: 0;
          transform: translateX(-50%);
          border-radius: 0;
      }
  }


  /* Animaciones para mostrar y ocultar elementos.*/
  .show-animation {
      animation: show 0.3s forwards;
  }

  .hide-animation {
      animation: hide 0.3s forwards;
  }

  @keyframes show {
      from {
          opacity: 0;
      }

      to {
          opacity: 1;
      }
  }

  @keyframes hide {
      from {
          opacity: 1;
      }

      to {
          opacity: 0;
      }
  }

  /* Estilo para todos los inputs numéricos */
  .form-group:has(input[type="number"]) {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
  }

  .form-group:has(input[type="number"]) label {
      flex: 0.5;
      /* Toma el 50% del espacio disponible */
      margin-bottom: 0;
      /* Anula el margen inferior existente si lo hubiera */
  }

  .form-group:has(input[type="number"]) input[type="number"] {
      flex: 0.5;
      /* Toma el 50% del espacio disponible */
      min-width: 80px;
      /* Ancho mínimo para usabilidad */
  }

  /* Alternativa para navegadores que no soportan :has */
  .form-group input[type="number"] {
      width: 50%;
      display: inline-block;
      vertical-align: middle;
  }

  .form-group label {
      display: inline-block;
      vertical-align: middle;
  }

  .widget.selected::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: var(--selection-overlay);
      border: 2px solid var(--selection-border);
      pointer-events: none;
      z-index: 1;
  }

  .widget.selected::after {
      content: '✓';
      position: absolute;
      top: 5px;
      right: 5px;
      width: 20px;
      height: 20px;
      background-color: var(--selection-check-background);
      color: var(--selection-check);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: bold;
      z-index: 2;
  }

  .widget .widget-checkbox-container {
      position: absolute;
      top: 5px;
      right: 5px;
      z-index: 2;
      display: none;
      /* Oculto por defecto */
  }

  /* Mostrar checkboxes cuando el body tiene la clase selection-mode */
  body.selection-mode .widget .widget-checkbox-container {
      display: block;
  }

  /* Efecto de flash al pegar widgets - Estilos base */
  .widget-pasted {
      animation: widget-paste-flash 1s ease-out;
  }

  @keyframes widget-paste-flash {
      0% {
          transform: scale(0.95);
          opacity: 0.7;
      }

      50% {
          transform: scale(1.02);
          opacity: 1;
      }

      100% {
          transform: scale(1);
      }
  }

  /* Estilos genéricos para botones de series */
  .add-series-btn,
  .remove-series-btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      padding: 0;
      margin: 0 5px;
      border-radius: 0;
      position: relative;
      z-index: 5;
      cursor: pointer;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      border: none;
      color: white;
      text-align: center;
      font-size: 18px;
      font-weight: bold;
      line-height: 1;
      font-family: Arial, sans-serif;
  }

  .add-series-btn:hover,
  .remove-series-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }

  .add-series-btn {
      background-color: var(--success-color, #4CAF50);
  }

  .remove-series-btn {
      background-color: var(--error-color, #F44336);
  }

/* === css/responsive.css === */
/**
 * Estilos responsive para iPRA Dashboard
 */

/* Estilos generales responsive */
@media (max-width: 768px) {
    body {
        font-size: 14px;
        overflow-x: hidden;
        /* Evitar scroll horizontal en el body */
    }

    .container {
        padding: 10px;
        max-width: 100%;
        max-height: 100vh;
        overflow-x: hidden;
        /* Evitar scroll horizontal en el contenedor principal */
        position: relative;
        top: 0;
        left: 0;
        transform: none;
        margin: 0;
    }

    .entity-management-container {
        height: 100vh;
    }

    .entity-table-container {
        height: calc(100vh - 150px);
        max-height: calc(100vh - 150px);
    }

    .clear-filter-btn {
        top: 22px;
        /* Ajustar posición en móviles */
    }

    #login-container {
        height: 100vh;
    }

    main {
        width: 100%;
        max-width: 100%;
        overflow-x: auto;
        /* Permitir scroll horizontal en el main */
        -webkit-overflow-scrolling: touch;
        /* Scroll suave en iOS */
    }

    /* Ajustar el ancho del contenedor de entidades en móvil */
    .entity-container {
        width: 100%;
    }

    header {
        padding: 10px 0;
        flex-direction: row !important;
        /* Forzar dirección horizontal */
        align-items: center;
        justify-content: space-between;
    }

    header h1 {
        font-size: 18px;
        margin-bottom: 0;
        white-space: wrap;
    }

    /* Ocultar el botón de opciones normal y mostrar el icono de menú móvil */
    #options-btn {
        display: none;
    }

    #mobile-menu-btn {
        display: flex !important;
    }

    /* Hacer que el menú desplegable se abra hacia abajo en móviles */
    .dropdown-menu {
        top: 100%;
        bottom: auto;
        width: 160px;
        /* Ancho reducido para que quepa en pantalla */
        max-width: 70%;
        /* Asegurar que no ocupe más del 70% del ancho */
        right: 0;
        left: auto;
        transform: none;
        max-height: 80vh;
        /* Limitar altura máxima */
        overflow-y: auto;
        /* Permitir scroll si hay muchas opciones */
        z-index: 1000;
        /* Asegurar que esté por encima de otros elementos */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        border-radius: 4px;
        font-size: 14px;
        /* Texto más pequeño para móviles */
    }

    /* Asegurar que el menú no se salga de la pantalla */
    .menu-container {
        position: relative;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        z-index: 1201;
        top: -2em;
    }

    #main-menu-container {
        width: 300px;
        margin-right: auto;
        margin-left: auto;
        height: fit-content;
    }

    /* Ajustar posición del menú para que no se salga de la pantalla */
    @media (max-width: 320px) {
        .dropdown-menu {
            right: -10px;
            /* Ajustar posición en pantallas muy pequeñas */
            width: 140px;
        }
    }

    /* Ajustar tamaño de botones para mejor toque */
    button {
        padding: 12px 15px;
        min-height: 45px;
        /* Mínimo recomendado para elementos táctiles */
    }

    /* Botones más compactos en el menú desplegable */
    .dropdown-menu button {
        padding: 8px 10px;
        min-height: 36px;
        font-size: 13px;
        white-space: normal;
        /* Permitir que el texto se envuelva */
        text-align: left;
        line-height: 1.2;
    }

    /* Estilos para botones de iconos en móvil */
    .icon-btn {
        min-width: 40px !important;
        width: auto !important;
        padding: 0 10px !important;
        font-size: 20px !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        text-align: center !important;
    }

    /* Distribuir botones en una sola fila */
    .button-row {
        display: flex !important;
        flex-direction: row !important;
        justify-content: space-between !important;
        width: 100% !important;
        gap: 5px !important;
    }

    /* Ajuste específico para el botón de menú móvil */
    .mobile-menu-btn {
        min-height: 30px;
        padding: 0;
        display: flex !important;
        justify-content: center;
        align-items: center;
        text-align: center;
        line-height: 30px;
        /* Igual a la altura para centrar verticalmente */
    }

    /* Ajustar modales para pantallas pequeñas */
    .modal-content {
        width: 90%;
        max-width: 400px;
        padding: 15px;
    }

    .modal-content h2 {
        margin-bottom: 5px;
        /* Reducido de 7px a 5px */
        margin-top: 0;
        /* Cambiado de -5px a 0 */
    }

    .modal-content .close-btn {
        margin-bottom: 5px;
        /* Reducido de 7px a 5px */
        margin-top: -5px;
        /* Cambiado de -7px a -5px */
    }

    /* Estilos para la fila de cabecera del widget con acciones en móvil */
    .widget-header-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        flex-wrap: nowrap;
    }

    .widget-type-label {
        margin-bottom: 0 !important;
        white-space: nowrap;
        font-size: 14px;
        flex: 1;
    }

    .widget-actions-container {
        display: flex;
        gap: 5px;
        margin-left: 5px;
    }

    /* Estilos para la fila del selector de tipo de widget en móvil */
    .widget-type-row {
        margin-bottom: 12px;
        padding-left: 0;
    }

    .widget-type-select {
        width: 100%;
        font-size: 14px;
    }

    .icon-action-btn {
        width: 32px;
        height: 32px;
        min-height: 32px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
    }

    .action-icon {
        font-size: 18px;
    }

    /* Estilos específicos para el modal de configuración de columnas */
    #column-config-modal .modal-content {
        width: 430px !important;
        /* Mantener ancho fijo */
        max-width: 95vw !important;
        /* Pero asegurar que no se salga de la pantalla */
    }

    /* En modo horizontal, ajustar altura */
    @media (orientation: landscape) {
        #column-config-modal .modal-content {
            height: 100vh;
            max-height: 100vh;
            border-radius: 0;
        }
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
    }

    #dashboard-config-modal .form-group label {
        display: inline-block;
        margin-bottom: 5px;
        width: 45%;
    }

    #dashboard-config-modal .form-group input,
    #dashboard-config-modal .form-group select {
        width: 49%;
    }

    .form-group input,
    .form-group select {
        width: 100%;
    }

    /* Ajustar el dashboard para scroll */
    .dashboard {
        width: 800px !important;
        /* Mantener ancho fijo para posicionamiento correcto */
        height: 600px !important;
        /* Mantener altura fija para posicionamiento correcto */
        overflow: visible;
        /* Permitir que el contenido sobresalga */
        position: relative;
        margin: 0 auto;
        border-radius: 0 !important;
        /* Sin bordes redondeados en móviles */
        transform: none !important;
        /* Evitar transformaciones que afecten al posicionamiento */
        min-width: 800px;
        /* Asegurar que el dashboard mantenga su ancho mínimo */
    }

    /* Contenedor para el dashboard con scroll horizontal */
    .dashboard-container {
        width: 100%;
        max-width: 100%;
        overflow-x: auto !important;
        /* Forzar scroll horizontal */
        overflow-y: visible;
        -webkit-overflow-scrolling: touch;
        /* Scroll suave en iOS */
        padding-bottom: 20px;
        /* Espacio para los botones de navegación */
        position: relative;
        /* Asegurar que el contenedor tenga posición relativa */
        display: block;
        /* Asegurar que el contenedor se muestre como bloque */
        white-space: nowrap;
        /* Evitar que el contenido se envuelva */
        scrollbar-width: thin;
        /* Barra de desplazamiento delgada en Firefox */
        scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
        /* Color de la barra de desplazamiento en Firefox */
    }

    /* Indicador de scroll horizontal */
    .dashboard-container::after {
        content: '← Desliza →';
        position: absolute;
        bottom: 5px;
        right: 10px;
        background-color: rgba(0, 0, 0, 0.5);
        color: white;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        opacity: 0.8;
        /*    pointer-events: none;*/
        /* No interferir con los eventos del mouse */
        animation: fadeOut 3s forwards 2s;
        /* Desaparecer después de 5 segundos */
        z-index: 100;
    }

    @keyframes fadeOut {
        from {
            opacity: 0.8;
        }

        to {
            opacity: 0;
        }
    }

    /* Estilos para barras de desplazamiento en WebKit */
    .dashboard-container::-webkit-scrollbar {
        height: 6px;
    }

    .dashboard-container::-webkit-scrollbar-track {
        background: transparent;
    }

    .dashboard-container::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 3px;
    }

    /* Botones de navegación en móvil */
    .navigation-buttons {
        position: absolute;
        bottom: 20px;
        right: 20px;
        top: auto;
        /* Anular la posición top de escritorio */
        opacity: 0.95;
        /* Más visible en móvil */
        transform: none !important;
        /* Evitar transformaciones que puedan afectar la posición */
    }

    .nav-button {
        width: 50px;
        height: 50px;
        /* Botones más grandes para facilitar el toque */
        font-size: 28px;
        /* Texto más grande */
    }

    /* Estilos específicos para botones de navegación en formularios móviles */
    .mobile-nav-button {
        width: auto !important;
        height: auto !important;
        min-height: 45px !important;
        border-radius: 4px !important;
        font-size: 20px !important;
        flex: 1 !important;
    }

    /* Ajustes para el formulario de login en móviles */
    .login-form {
        width: 280px !important;
        /* Ancho más estrecho en móviles */
        padding: 20px;
    }

    .login-form h2 {
        font-size: 1.2rem;
        margin-bottom: 15px;
    }

    .form-group {
        margin-bottom: 12px;
    }

    .form-group label {
        font-size: 0.9rem;
    }

    .form-group input,
    .form-group select {
        padding: 8px;
        font-size: 0.9rem;
    }

    /* Estilos para el selector de tema */
    .theme-selector {
        margin-top: 15px;
        width: 100%;
    }

    .theme-selector select {
        width: 100%;
        padding: 8px;
        border-radius: 4px;
        background-color: var(--surface-color);
        border: 1px solid var(--border-color);
        color: var(--text-color);
        font-size: 0.9rem;
    }

    /* Ajustar el botón de login */
    #login-btn {
        width: 100%;
        margin-top: 15px;
    }
}

/* Configuración para pantallas de entidades en modo vertical */
@media (max-width: 768px) and (orientation: portrait) {

    /* Mostrar el botón de volver atrás y ocultar el menú móvil */
    #entity-back-btn,
    [id^="entity-back-btn-"] {
        display: flex !important;
        /* Hacemos visible el botón de volver atrás */
    }

    /* Ocultamos el botón de menú móvil en las entidades */
    #entity-mobile-menu-btn,
    [id^="entity-mobile-menu-btn-"] {
        display: none !important;
    }
}

/* Ajustes para pantallas muy pequeñas */
@media (max-width: 480px) {
    header h1 {
        font-size: 1.2rem;
    }

    /* Estilo general para grupos de botones en móvil */
    .button-group {
        flex-direction: column;
        gap: 10px;
    }

    .button-group button {
        width: 100%;
    }


    /* Estilo para el botón de cancelar */
    .cancel-btn {
        background-color: #f5f5f5;
        color: #333;
        border: 1px solid #ddd;
        padding: 10px 15px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        flex: 1;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .cancel-btn:hover {
        background-color: #e0e0e0;
    }
}

/* Posición de los botones de navegación en escritorio */
.navigation-buttons {
    position: absolute;
    top: 80px;
    /* Ajustado para que no quede oculto por el header */
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 1;
    /* Aumentado para asegurar que esté por encima de todo */
    opacity: 0.9;
    /* Más visible por defecto */
    transition: all 0.3s ease;
}

.navigation-buttons:hover {
    opacity: 1;
    transform: scale(1.1);
    /* Efecto de escala al pasar el cursor */
}

.nav-button {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
    font-size: 24px;
    border: 2px solid white;
    transition: all 0.3s ease;
    font-weight: bold;
}

.nav-button:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.4);
}

/* Ajustes específicos para temas en escritorio */
.theme-tron .nav-button {
    background-color: rgba(0, 136, 204, 0.9);
    box-shadow: 0 0 15px rgba(0, 136, 204, 0.7);
    border: 2px solid var(--accent-color);
    color: white;
    text-shadow: 0 0 5px white;
}

.theme-tron .nav-button:hover {
    background-color: rgba(0, 162, 255, 1);
    box-shadow: 0 0 20px rgba(0, 162, 255, 0.9);
    transform: translateY(-2px) scale(1.05);
}

.theme-neumorphic .nav-button {
    background-color: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-medium);
    border: 2px solid var(--surface-color);
}

.theme-neumorphic .nav-button:hover {
    box-shadow: var(--shadow-large);
    transform: translateY(-3px);
}

/* Ajustes para eventos táctiles */
@media (hover: none) and (pointer: coarse) {

    /* Aumentar áreas táctiles */
    button,
    .dropdown-menu button,
    .close-btn,
    .resize-handle {
        min-height: 44px;
        min-width: 44px;
    }

    /* Tamaño normal para checkboxes en pantallas táctiles */
    .checkbox-group input[type="checkbox"] {
        min-height: 20px;
        min-width: 20px;
    }

    /* Mejorar visibilidad del manejador de redimensionamiento */
    .widget .resize-handle {
        width: 30px;
        height: 30px;
    }

    .widget .resize-handle::before {
        width: 15px;
        height: 15px;
    }

    /* Estilo para widgets cuando están siendo tocados */
    .widget.touch-active {
        opacity: 0.8;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
        z-index: 1000;
    }

    /* Estilo para el widget durante el arrastre táctil */
    .widget.dragging {
        opacity: 0.7;
        z-index: 1001;
        transition: none !important;
        /* Desactivar transiciones durante el arrastre */
        touch-action: none !important;
        /* Prevenir gestos del navegador */
        -webkit-touch-callout: none !important;
        /* Prevenir menú contextual en iOS */
        -webkit-user-select: none !important;
        /* Prevenir selección de texto */
        user-select: none !important;
        /* Prevenir selección de texto */
    }

    /* Prevenir gestos del navegador en el dashboard en modo edición */
    .edit-mode {
        touch-action: none !important;
        -webkit-overflow-scrolling: auto !important;
    }

    /* Eliminar efectos hover que no funcionan en táctil */
    .dropdown-menu button:hover,
    button:hover {
        transform: none;
    }
}

/* === css/accordion.css === */
/* Accordion styles for widget configuration */
:root {
    --accordion-bg-color: var(--card-bg-color, #ffffff);
    --accordion-border-color: var(--border-color, #cccccc);
    --accordion-header-bg: var(--primary-color, #4a6fa5);
    --accordion-header-color: var(--primary-text-color, #ffffff);
    --accordion-header-hover-bg: var(--primary-hover-color, #3a5f95);
    --accordion-content-bg: var(--card-bg-color, #ffffff);
    --accordion-shadow: var(--card-shadow, 0 2px 5px rgba(0, 0, 0, 0.1));
    --accordion-icon-color: var(--primary-text-color, #ffffff);
}

/* Contenedor principal del acordeón */
.form-fields-accordion {
    max-height: 550px;
    /* Aumentado de 500px a 550px para aprovechar el espacio adicional */
    overflow-y: auto;
    /* Scroll vertical cuando sea necesario */
}

/* Estructura base del acordeón */
.accordion-section {
    border: 1px solid var(--accordion-border-color);
    margin-bottom: 0.5rem;
    border-radius: 4px;
    overflow: hidden;
    transition: opacity 0.3s ease-in-out;
}

/* Checkbox oculto que controla el estado */
.accordion-toggle {
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0;
    z-index: -1;
}

/* Cabecera del acordeón */
.accordion-header {
    display: flex;
    align-items: center;
    padding: 0.6rem 1rem;
    /* Reducido de 0.75rem a 0.6rem */
    background-color: var(--accordion-header-bg);
    color: var(--accordion-header-color);
    cursor: pointer;
    transition: background-color 0.2s ease;
    user-select: none;
}

.accordion-header:hover {
    background-color: var(--accordion-header-hover-bg);
}

/* Título dentro de la cabecera */
.accordion-header .title {
    flex-grow: 1;
    font-weight: 500;
    margin: 0 0.5rem;
}

/* Iconos en la cabecera */
.accordion-header .material-icons {
    font-family: sans-serif;
    font-size: 0;
    position: relative;
    width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-style: normal;
}

/* Icono de zona */
.accordion-header .zone-icon::before {
    font-size: 18px;
    position: absolute;
    content: "⚙";
}

.accordion-header[data-zone-id="posicion"] .zone-icon::before {
    content: "⌖";
}

.accordion-header[data-zone-id="series"] .zone-icon::before {
    content: "⊞";
}

/* Estilos específicos para la sección de Series */
.accordion-section:has(.accordion-header[data-zone-id="series"]) {
    position: relative;
    z-index: 10;
    /* Mayor z-index para la sección de Series */
}

/* Icono de toggle */
.accordion-header .toggle-icon {
    margin-left: auto;
}

.accordion-header .toggle-icon::before {
    font-size: 18px;
    position: absolute;
    content: "▸";
}

.accordion-toggle:not(:checked)+.accordion-header .toggle-icon::before {
    content: "▾";
}

/* Contenido del acordeón */
.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-in-out, padding 0.3s ease-in-out;
    padding: 0 1rem;
    background-color: var(--accordion-content-bg);
    box-sizing: border-box;
    position: relative;
    /* Asegurar que el contenido se muestre correctamente */
    z-index: 0;
    /* Valor base para el z-index */
}

.accordion-toggle:not(:checked)~.accordion-content {
    max-height: 400px;
    /* Aumentado de 350px a 400px para aprovechar el espacio adicional */
    overflow-y: auto;
    /* Añadir scroll vertical cuando sea necesario */
    padding: 0.8rem;
    padding-bottom: 0;
    /* Reducido de 1rem a 0.8rem */
    scrollbar-width: thin;
    /* Para Firefox */
    transition: max-height 0.3s ease-in-out, padding 0.3s ease-in-out, opacity 0.3s ease-in-out;
    opacity: 1;
    z-index: 1;
    /* Aumentar z-index cuando está abierto */
}

/* Estilo para la barra de desplazamiento en navegadores WebKit */
.accordion-content::-webkit-scrollbar {
    width: 6px;
}

.accordion-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.accordion-content::-webkit-scrollbar-thumb {
    background: var(--accordion-header-bg);
    border-radius: 3px;
}

.accordion-content::-webkit-scrollbar-thumb:hover {
    background: var(--accordion-header-hover-bg);
}

/* Estilos de los campos dentro del acordeón */
.accordion-content .field-group {
    margin-bottom: 0.7rem;
    /* Reducido de 1rem a 0.7rem */
}

.accordion-content .field-group:last-child {
    margin-bottom: 0;
}

.accordion-content label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.accordion-content input[type="text"],
.accordion-content input[type="number"],
.accordion-content select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--accordion-border-color);
    border-radius: 4px;
    font-size: 0.9rem;
}

.accordion-content input[type="checkbox"] {
    margin-right: 0.5rem;
}

/* === css/series_buttons.css === */
/* Estilos para los botones de series */

/* Contenedor para el selector de series y el botón de añadir */
.series-selector-container {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 10px;
}

/* Selector de series */
.series-selector-container select {
    flex: 1;
    margin-right: 10px;
}

/* Botón de añadir serie */
.series-selector-container .add-series-btn {
    flex-shrink: 0;
}

/* Estilos para la lista de series */
.series-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

/* Cada elemento de la lista de series */
.series-list li {
    display: flex;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

.series-list li:last-child {
    border-bottom: none;
}

/* Color de la serie */
.series-color {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 8px;
}

/* Nombre de la serie */
.series-name {
    flex: 1;
    font-size: 14px;
}

/* Botón de eliminar serie */
.series-list li .remove-series-item-btn {
    width: 24px;
    height: 24px;
    padding: 0;
    margin-left: 10px;
    border-radius: 0;
    position: relative;
    z-index: 5;
    cursor: pointer;
    border: none;
    color: white;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    line-height: 1;
    font-family: Arial, sans-serif;
    background-color: var(--error-color, #F44336);
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.series-list li .remove-series-item-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}


/* === css/widget_rules.css === */
/**
 * Estilos para la sección de reglas de widgets
 */

/* Contenedor de reglas */
.reglas-container {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 4px;
    margin-bottom: 0;
}

/* Título de la regla */
.regla-titulo {
    font-weight: bold;
    margin-bottom: 6px;
    font-size: 0.9em;
    color: #444;
    border-bottom: 1px dotted rgba(0, 0, 0, 0.1);
    padding-bottom: 3px;
}

/* Etiquetas */
.reglas-container label {
    display: inline-block;
    font-size: 0.85em;
    width: 40px;
    margin-right: 4px;
    vertical-align: middle;
}

/* Campos de número */
.reglas-container input[type="number"] {
    min-width: 60px;
    margin-right: 4px;
    vertical-align: middle;
}

/* Campos de color */
.reglas-container input[type="color"] {
    width: 30px;
    height: 30px;
    padding: 0;
    margin-right: 4px;
    vertical-align: middle;
}

.reglas-container .form-group label {
    flex: 0 0 50% !important;
    margin: 0;
    padding-right: 10px;
}

.reglas-container .form-group.color-field-container {
    margin-bottom: 0 !important;
}

/* Asegurar alineación entre campos */
.reglas-container div:nth-child(odd) input[type="number"],
.reglas-container div:nth-child(even) input[type="color"]:first-of-type {
    margin-left: 0;
}

/* Espacio entre filas */
.reglas-container div {
    margin-bottom: 4px;
}

/* Estilos para textarea en widget de texto */
.textarea-widget {
    width: 100%;
    min-height: 80px;
    resize: vertical;
    padding: 8px;
    box-sizing: border-box;
    font-family: inherit;
    font-size: 0.9em;
    line-height: 1.4;
}

/* Estilos específicos para el tema tron */
.theme-tron .reglas-container {
    border-color: rgba(0, 162, 255, 0.3);
    background-color: rgba(0, 0, 0, 0.2);
}

.theme-tron .regla-titulo {
    color: #00a2ff;
    border-bottom-color: rgba(0, 162, 255, 0.3);
}

.theme-tron .textarea-widget {
    background-color: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 162, 255, 0.5);
    color: #00a2ff;
}

/* Estilos específicos para el tema neumorphic */
.theme-neumorphic .reglas-container {
    border: none;
    box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.1), -3px -3px 6px rgba(255, 255, 255, 0.5);
}

.theme-neumorphic .regla-titulo {
    color: #555;
    border-bottom-color: rgba(0, 0, 0, 0.1);
}

.theme-neumorphic .textarea-widget {
    border: none;
    box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.1), inset -2px -2px 5px rgba(255, 255, 255, 0.5);
}

/* Estilos específicos para el tema azul_nuboso */
.theme-azul_nuboso .reglas-container {
    border-color: rgba(74, 111, 165, 0.3);
    background-color: rgba(255, 255, 255, 0.05);
}

.theme-azul_nuboso .regla-titulo {
    color: #e0e9f5;
    border-bottom-color: rgba(74, 111, 165, 0.3);
}

.theme-azul_nuboso .textarea-widget {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(74, 111, 165, 0.5);
    color: #e0e9f5;
}

/* === css/widget_rules_new.css === */
/**
 * Estilos para la sección de reglas de widgets
 */

/* Contenedor de reglas */
.reglas-container {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 4px;
    margin-bottom: 0;
}

/* Título de la regla */
.regla-titulo {
    font-weight: bold;
    margin-bottom: 6px;
    font-size: 0.9em;
    color: #444;
    border-bottom: 1px dotted rgba(0, 0, 0, 0.1);
    padding-bottom: 3px;
}

/* Etiquetas */
.reglas-container label {
    display: inline-block;
    font-size: 0.85em;
    width: 40px;
    margin-right: 4px;
    vertical-align: middle;
}

/* Campos de número */
.reglas-container input[type="number"] {
    width: 60px;
    min-width: 60px;
    margin-right: 4px;
    vertical-align: middle;
}

.reglas-container .form-group label {
    flex: 0 0 50% !important;
    margin: 0;
    padding-right: 10px;
}

/* Campos de color */
.reglas-container input[type="color"] {
    width: 30px;
    height: 30px;
    padding: 0;
    margin-right: 4px;
    vertical-align: middle;
}

/* Asegurar alineación entre campos */
.reglas-container div:nth-child(odd) input[type="number"],
.reglas-container div:nth-child(even) input[type="color"]:first-of-type {
    margin-left: 0;
}

/* Espacio entre filas */
.reglas-container div {
    margin-bottom: 4px;
}

/* Estilos para textarea en widget de texto */
.textarea-widget {
    width: 100%;
    min-height: 80px;
    resize: vertical;
    padding: 8px;
    box-sizing: border-box;
    font-family: inherit;
    font-size: 0.9em;
    line-height: 1.4;
}

/* Estilos específicos para el tema tron */
.theme-tron .reglas-container {
    border-color: rgba(0, 162, 255, 0.3);
    background-color: rgba(0, 0, 0, 0.2);
}

.theme-tron .regla-titulo {
    color: #00a2ff;
    border-bottom-color: rgba(0, 162, 255, 0.3);
}

.theme-tron .textarea-widget {
    background-color: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 162, 255, 0.5);
    color: #00a2ff;
}

/* Estilos específicos para el tema neumorphic */
.theme-neumorphic .reglas-container {
    border: none;
    box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.1), -3px -3px 6px rgba(255, 255, 255, 0.5);
}

.theme-neumorphic .regla-titulo {
    color: #555;
    border-bottom-color: rgba(0, 0, 0, 0.1);
}

.theme-neumorphic .textarea-widget {
    border: none;
    box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.1), inset -2px -2px 5px rgba(255, 255, 255, 0.5);
}

/* Estilos específicos para el tema azul_nuboso */
.theme-azul_nuboso .reglas-container {
    border-color: rgba(74, 111, 165, 0.3);
    background-color: rgba(255, 255, 255, 0.05);
}

.theme-azul_nuboso .regla-titulo {
    color: #e0e9f5;
    border-bottom-color: rgba(74, 111, 165, 0.3);
}

.theme-azul_nuboso .textarea-widget {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(74, 111, 165, 0.5);
    color: #e0e9f5;
}

/* === css/themes/tron.css === */
/* Tema Tron - Estilo futurista inspirado en la película Tron */
/* Importar fuentes para el tema Tron */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700&family=Rajdhani:wght@300;400;500;600;700&display=swap');

/* Variables del tema */
:root.theme-tron {
    --primary-color: #00a2ff;
    --primary-color-rgb: 0, 162, 255;
    /* RGB para efectos de transparencia */
    --secondary-color: #0066cc;
    --accent-color: #00ffff;
    --tron-accent-bright: #00ffff;
    --background-color: #0a1a2a;
    --surface-color: #0c2038;
    --tron-surface: #0c2038;
    --text-color: #ffffff;
    --tron-text: #ffffff;
    --tron-surface-dark: #0a1a2a;
    --text-secondary-color: #0088cc;
    --tron-accent: #0088cc;
    /* Color más oscuro para mejor legibilidad */
    --gauge-value-color: #0099cc;
    /* Color más oscuro para valores de gauges */
    --border-color: #00a2ff;
    --success-color: #00ff9f;
    --warning-color: #ffcc00;
    --error-color: #ff3366;
    --error-color-rgb: 255, 51, 102;
    --shadow-color: rgba(0, 162, 255, 0.5);
    --glow-effect: 0 0 10px rgba(0, 162, 255, 0.7), 0 0 20px rgba(0, 162, 255, 0.4);
    --tron-accent-glow: 0 0 10px rgba(0, 162, 255, 0.7), 0 0 20px rgba(0, 162, 255, 0.4);
    --neon-effect: 0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.4);
    --grid-lines: rgba(0, 162, 255, 0.2);

    /* Fuentes */
    --font-family: 'Rajdhani', 'Orbitron', sans-serif;
    --font-weight-normal: 400;
    --font-weight-bold: 600;
}

/* Para seleccionar widgets*/
:root.theme-tron {
    --selection-overlay: rgba(0, 162, 255, 0.15);
    --selection-border: var(--accent-color);
    --selection-check: var(--tron-accent-bright);
    --selection-check-background: var(--primary-color);
}

.theme-tron .widget.selected::before {
    box-shadow: var(--tron-accent-glow);
}

.theme-tron .widget.selected::after {
    box-shadow: var(--neon-effect);
}

.theme-tron .widget .widget-checkbox-container input[type="checkbox"] {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 3px;
    background-color: rgba(0, 0, 0, 0.3);
    box-shadow: var(--neon-effect);
    cursor: pointer;
}

.theme-tron .widget .widget-checkbox-container input[type="checkbox"]:checked {
    background-color: var(--primary-color);
    border-color: var(--accent-color);
    box-shadow: var(--tron-accent-glow);
}

:root.theme-tron .widget-pasted {
    box-shadow: 0 0 20px var(--accent-color);
}

:root.theme-tron .widget-pasted {
    animation: widget-paste-flash-tron 1s ease-out;
}

@keyframes widget-paste-flash-tron {
    0% {
        box-shadow: 0 0 30px var(--accent-color), 0 0 10px var(--primary-color);
        transform: scale(0.95);
    }

    50% {
        box-shadow: 0 0 20px var(--accent-color), 0 0 15px var(--primary-color);
        transform: scale(1.02);
    }

    100% {
        box-shadow: var(--glow-effect);
        transform: scale(1);
    }
}




/* Estilos generales */
.theme-tron body {
    background-color: var(--background-color);
    color: var(--text-color);
    background-image:
        linear-gradient(0deg, var(--grid-lines) 1px, transparent 1px),
        linear-gradient(90deg, var(--grid-lines) 1px, transparent 1px);
    background-size: 20px 20px;
    font-family: var(--font-family);
}

/* Contenedores */
.theme-tron .container {
    background-color: rgba(10, 26, 42, 0.7);
    border: 1px solid var(--border-color);
    box-shadow: var(--glow-effect);
}

/* Login */
.theme-tron #login-container h1 {
    color: var(--accent-color);
    text-shadow: var(--neon-effect);
    letter-spacing: 2px;
    font-size: 3rem;
    margin-bottom: 2rem;
}

.theme-tron .login-form {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    box-shadow: var(--glow-effect);
    border-radius: 0;
    padding: 2rem;
}

.theme-tron .login-form h2 {
    color: var(--accent-color);
    text-transform: uppercase;
    letter-spacing: 1px;
    text-align: center;
    margin-bottom: 1.5rem;
}

.theme-tron .form-group label {
    color: var(--text-secondary-color);
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 1px;
}

.theme-tron .form-group input:not([type="checkbox"]),
.theme-tron .form-group select,
.theme-tron .form-group textarea {
    background-color: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 0.7rem;
    border-radius: 0;
    transition: all 0.3s ease;
}

.theme-tron .form-group input:focus,
.theme-tron .form-group select:focus,
.theme-tron .form-group textarea:focus {
    box-shadow: var(--neon-effect);
    outline: none;
}

.theme-tron button {
    background-color: var(--primary-color);
    color: var(--text-color);
    border: 1px solid var(--accent-color);
    border-radius: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 0.7rem 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.theme-tron button:hover {
    background-color: var(--secondary-color);
    box-shadow: var(--glow-effect);
}

.theme-tron button:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
            transparent,
            rgba(0, 255, 255, 0.2),
            transparent);
    transition: 0.5s;
}

.theme-tron button:hover:before {
    left: 100%;
}

.theme-tron button:disabled {
    background-color: rgba(0, 162, 255, 0.5);
    border-color: rgba(0, 255, 255, 0.3);
    box-shadow: none;
    cursor: not-allowed;
    opacity: 0.7;
}

.theme-tron button:disabled:before {
    display: none;
}

.theme-tron .error-message {
    color: var(--error-color);
    text-shadow: 0 0 5px rgba(255, 51, 102, 0.7);
}

/* Header */
.theme-tron header {
    border-bottom: 1px solid var(--border-color);
    /*padding: 1rem 0;*/
    padding: 0;
    padding-bottom: 5px;
    margin-bottom: 1.5rem;
}

.theme-tron header h1 {
    color: var(--accent-color);
    text-shadow: var(--neon-effect);
    text-transform: uppercase;
    letter-spacing: 2px;
}

.theme-tron .header-title-container {
    position: relative;
}

.theme-tron .dashboard-quick-actions {
    margin-top: 5px;
}

.theme-tron .icon-button {
    background-color: rgba(0, 0, 0, 0.3);
    color: var(--accent-color);
    border: 1px solid var(--border-color);
    box-shadow: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.theme-tron .icon-button:hover {
    background-color: rgba(0, 162, 255, 0.2);
    box-shadow: var(--glow-effect);
    transform: translateY(-2px);
}

.theme-tron .icon-button:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.theme-tron .icon-button:hover:before {
    left: 100%;
}

.theme-tron .dropdown-menu {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    box-shadow: var(--glow-effect);
    border-radius: 0;
}

.theme-tron .dropdown-menu button {
    background-color: transparent;
    color: var(--text-color);
    border: none;
    border-bottom: 1px solid rgba(0, 162, 255, 0.3);
    border-radius: 0;
    text-align: left;
}

.theme-tron .dropdown-menu button:hover {
    background-color: rgba(0, 162, 255, 0.2);
    box-shadow: none;
}

/* Dashboard */
.theme-tron .dashboard {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    box-shadow: var(--glow-effect);
    border-radius: 0;
    background-image: none;
    /* Sin cuadrícula por defecto */
}

/* Dashboard con cuadrícula */
.theme-tron .dashboard.show-grid {
    background-image:
        linear-gradient(0deg, var(--grid-lines) 1px, transparent 1px),
        linear-gradient(90deg, var(--grid-lines) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Widgets */
.theme-tron .widget {
    background-color: rgba(200, 210, 220, 0.15);
    border: 0;
    /* Sin borde por defecto */
    box-shadow: none;
    /* Sin sombra por defecto */
    border-radius: 0;
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
    position: relative;
    /* Para posicionar el resize handle */
}

/* Manejador de redimensionamiento */
.theme-tron .widget .resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 15px;
    height: 15px;
    cursor: nwse-resize;
    background: transparent;
    z-index: 10;
    display: none;
    /* Oculto por defecto */
}

.theme-tron .widget .resize-handle::before {
    content: '';
    position: absolute;
    bottom: 3px;
    right: 3px;
    width: 8px;
    height: 8px;
    border-right: 2px solid var(--border-color);
    border-bottom: 2px solid var(--border-color);
    opacity: 0.7;
}

.theme-tron .edit-mode .widget .resize-handle {
    display: block;
    /* Visible en modo edición */
}

/* Aplicar bordes y sombra solo cuando está activada la opción */
.theme-tron .show-widget-borders .widget {
    border: 1px solid var(--border-color);
    box-shadow: var(--glow-effect);
}

/* Efecto hover solo cuando los bordes están activados */
.theme-tron .show-widget-borders .widget:hover {
    box-shadow: var(--neon-effect);
}

.theme-tron .widget-header {
    border-bottom: 1px solid var(--border-color);
}

.theme-tron .widget-title {
    color: var(--accent-color);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.8rem;
}

.theme-tron .text-widget .widget-content {
    color: var(--text-secondary-color);
    font-weight: var(--font-weight-bold);
    text-shadow: 0 0 5px rgba(0, 136, 204, 0.3);
    /* Sombra más sutil */
}

.theme-tron .value-widget .widget-content {
    color: var(--gauge-value-color);
    text-shadow: 0 0 5px rgba(0, 153, 204, 0.7);
    font-size: 1.8rem;
    font-weight: var(--font-weight-bold);
}

/* Gauges */
.theme-tron .gauge-arc.gauge-background {
    stroke: rgba(0, 162, 255, 0.2);
}

.theme-tron .gauge-arc.gauge-foreground {
    stroke: var(--accent-color);
    filter: drop-shadow(0 0 3px var(--accent-color));
}

.theme-tron .gauge-value {
    color: var(--gauge-value-color);
    text-shadow: 0 0 5px rgba(0, 153, 204, 0.7);
    font-size: 1.2rem;
    font-weight: var(--font-weight-bold);
}

.theme-tron .percentage-title {
    color: var(--text-secondary-color);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
    font-weight: var(--font-weight-bold);
}

.theme-tron .percentage-value {
    color: var(--gauge-value-color);
    text-shadow: 0 0 5px rgba(0, 153, 204, 0.7);
    font-size: 1.8rem;
    font-weight: var(--font-weight-bold);
}

.theme-tron .percentage-gauge {
    background-color: rgba(0, 162, 255, 0.2);
    border-radius: 0;
    height: 20px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.5);
    width: 80%;
    /* Más ancho según preferencia del usuario */
}

.theme-tron .percentage-gauge-fill {
    background-color: var(--accent-color);
    background-image: linear-gradient(45deg,
            rgba(255, 255, 255, 0.15) 25%,
            transparent 25%,
            transparent 50%,
            rgba(255, 255, 255, 0.15) 50%,
            rgba(255, 255, 255, 0.15) 75%,
            transparent 75%,
            transparent);
    background-size: 20px 20px;
    border-radius: 0;
    box-shadow: 0 0 10px var(--accent-color);
    animation: tron-pulse 1.5s infinite alternate;
}

/* Modales */
.theme-tron .modal {
    backdrop-filter: blur(5px);
}

.theme-tron .modal-content {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    box-shadow: var(--glow-effect);
    border-radius: 0;
}

.theme-tron .close-btn {
    color: var(--accent-color);
}

/* Diálogo de confirmación de eliminación */
.theme-tron .delete-confirm-dialog .dialog-content {
    border: 2px solid var(--error-color);
    background-color: rgba(var(--error-color-rgb), 0.05);
    box-shadow: 0 0 15px rgba(var(--error-color-rgb), 0.3), 0 0 30px rgba(var(--error-color-rgb), 0.1);
}

.theme-tron .delete-confirm-dialog .dialog-header {
    background-color: rgba(var(--error-color-rgb), 0.1);
    border-bottom: 1px solid rgba(var(--error-color-rgb), 0.2);
}

.theme-tron .delete-confirm-dialog .dialog-header h3 {
    color: var(--error-color);
    text-shadow: 0 0 5px rgba(var(--error-color-rgb), 0.5);
}

/* Botón de eliminar en el diálogo de confirmación y en modales */
.theme-tron .delete-confirm-dialog .danger-btn,
.theme-tron .danger-btn {
    background-color: rgba(var(--error-color-rgb), 0.8);
    color: white;
    border: 1px solid var(--error-color);
    box-shadow: 0 0 10px rgba(var(--error-color-rgb), 0.5);
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

.theme-tron .delete-confirm-dialog .danger-btn:hover,
.theme-tron .danger-btn:hover {
    background-color: var(--error-color);
    box-shadow: 0 0 15px rgba(var(--error-color-rgb), 0.7), 0 0 30px rgba(var(--error-color-rgb), 0.4);
}

.theme-tron .delete-confirm-dialog .danger-btn:before,
.theme-tron .danger-btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.theme-tron .delete-confirm-dialog .danger-btn:hover:before,
.theme-tron .danger-btn:hover:before {
    left: 100%;
}

/* Popup de opciones */
.theme-tron .popup-menu {
    backdrop-filter: blur(5px);
}

.theme-tron .popup-content {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    box-shadow: var(--glow-effect);
    border-radius: 0;
}

.theme-tron .popup-content h3 {
    color: var(--accent-color);
    text-transform: uppercase;
    letter-spacing: 1px;
    border-bottom: 1px solid var(--border-color);
}

.theme-tron .popup-option {
    background-color: rgba(0, 0, 0, 0.3);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    /* Para asegurar que los efectos se apliquen correctamente */
    z-index: 1;
    /* Para evitar problemas de superposición */
    margin-bottom: 8px;
    /* Espacio entre opciones */
}

.theme-tron .popup-option:first-child {
    border-top: 1px solid var(--border-color);
    /* Asegurar que el primer elemento tenga borde superior */
}

.theme-tron .popup-option:hover {
    background-color: rgba(0, 162, 255, 0.2);
    box-shadow: var(--glow-effect);
    border-color: var(--accent-color);
    /* Borde más visible al hacer hover */
}

.theme-tron .popup-option.danger-option {
    color: var(--error-color);
    border-color: var(--error-color);
}

.theme-tron .popup-option.danger-option:hover {
    background-color: rgba(255, 51, 102, 0.2);
    box-shadow: 0 0 10px rgba(255, 51, 102, 0.7);
}

/* Estilos para el popup de datos de período en el tema Tron */
.theme-tron .period-data-popup {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    box-shadow: var(--glow-effect);
    border-radius: 0;
    backdrop-filter: blur(5px);
}

.theme-tron .period-data-popup .popup-header {
    background-color: rgba(0, 162, 255, 0.2);
    border-bottom: 1px solid var(--border-color);
}

.theme-tron .period-data-popup .popup-header h3 {
    color: var(--accent-color);
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 0 5px rgba(0, 162, 255, 0.5);
}

.theme-tron .period-data-popup .close-btn {
    color: var(--accent-color);
}

.theme-tron .period-data-popup .close-btn:hover {
    color: var(--error-color);
    text-shadow: 0 0 5px rgba(255, 51, 102, 0.7);
}

.theme-tron .period-data-popup p {
    color: var(--text-color);
}

.theme-tron .period-data-table {
    border-collapse: collapse;
}

.theme-tron .period-data-table th {
    background-color: rgba(0, 162, 255, 0.2);
    color: var(--accent-color);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
    border: 1px solid var(--border-color);
}

.theme-tron .period-data-table td {
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.theme-tron .period-points-table {
    border: 1px solid var(--border-color);
    box-shadow: var(--glow-effect);
}

.theme-tron .period-points-table th {
    background-color: rgba(0, 162, 255, 0.2);
    color: var(--accent-color);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.8rem;
    border: 1px solid var(--border-color);
}

.theme-tron .period-points-table td {
    color: var(--text-color);
    border: 1px solid var(--border-color);
    font-size: 0.9rem;
}

.theme-tron .popup-option.selected {
    background-color: rgba(0, 162, 255, 0.5);
    border: 1px solid var(--accent-color);
    /* Borde completo en lugar de solo izquierdo */
    border-left: 4px solid var(--accent-color);
    /* Mantener el borde izquierdo más grueso */
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.7);
    text-shadow: 0 0 5px var(--accent-color);
    font-weight: var(--font-weight-bold);
    color: var(--accent-color);
}

.theme-tron .popup-option.selected:hover {
    background-color: rgba(0, 162, 255, 0.6);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.9);
}

/* Modo edición */
.theme-tron .edit-mode .widget:hover {
    border: 2px dashed var(--accent-color);
    box-shadow: 0 0 15px var(--accent-color);
}

.theme-tron .delete-mode .widget:hover {
    border: 2px dashed var(--error-color);
    box-shadow: 0 0 15px var(--error-color);
}

/* Estilo para el widget durante el arrastre */
.theme-tron .widget.dragging {
    opacity: 0.8;
    z-index: 100;
}

/* Checkbox personalizado */
.theme-tron .checkbox-group {
    display: flex;
    align-items: center;
}

.theme-tron .checkbox-group input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    background-color: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    position: relative;
    margin-right: 10px;
    cursor: pointer;
}

.theme-tron .checkbox-group input[type="checkbox"]:checked {
    background-color: var(--primary-color);
}

.theme-tron .checkbox-group input[type="checkbox"]:checked::before {
    content: '✓';
    position: absolute;
    color: var(--text-color);
    font-size: 14px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.theme-tron .checkbox-group label {
    color: var(--text-secondary-color);
}

/* Animaciones */
@keyframes tron-pulse {
    from {
        box-shadow: 0 0 5px var(--accent-color);
    }

    to {
        box-shadow: 0 0 15px var(--accent-color);
    }
}

/* Scrollbar personalizada */
.theme-tron ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.theme-tron ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
}

.theme-tron ::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 0;
}

.theme-tron ::-webkit-scrollbar-thumb:hover {
    background: var(--accent-color);
}

/* Transparencia de widgets */
.theme-tron .transparent-widgets .widget {
    background-color: transparent;
    backdrop-filter: blur(0);
}
