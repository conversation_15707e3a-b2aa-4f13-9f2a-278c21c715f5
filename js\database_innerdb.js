/**
 * Módulo para la gestión de la base de datos usando IndexedDB
 * Implementado con promesas para simular una API REST
 * Permite acceso directo a tableros por ID y edición colaborativa de widgets
 */
class DatabaseManager {
    constructor() {
        this.dbName = 'ipram_innerdb';
        this.dbVersion = 1;
        this.initialized = false;
        this.db = null;
    }

    /**
     * Inicializa la base de datos IndexedDB
     * @returns {Promise} - Promesa que se resuelve cuando la base de datos está inicializada
     */
    init() {
        return new Promise((resolve, reject) => {
            if (this.initialized && this.db) {
                resolve();
                return;
            }

            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => {
                console.error('Error al abrir la base de datos:', request.error);
                reject(request.error);
            };

            request.onsuccess = () => {
                this.db = request.result;
                this.initialized = true;
                console.log('Base de datos IndexedDB inicializada correctamente');
                resolve();
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // Crear object stores
                if (!db.objectStoreNames.contains('companies')) {
                    const companiesStore = db.createObjectStore('companies', { keyPath: 'id' });
                    companiesStore.createIndex('nombre', 'nombre', { unique: false });
                    companiesStore.createIndex('activo', 'activo', { unique: false });
                }

                if (!db.objectStoreNames.contains('users')) {
                    const usersStore = db.createObjectStore('users', { keyPath: 'id' });
                    usersStore.createIndex('login', 'login', { unique: true });
                    usersStore.createIndex('empresaId', 'empresaId', { unique: false });
                    usersStore.createIndex('tipo', 'tipo', { unique: false });
                }

                if (!db.objectStoreNames.contains('dashboards')) {
                    const dashboardsStore = db.createObjectStore('dashboards', { keyPath: 'id' });
                    dashboardsStore.createIndex('empresaId', 'empresaId', { unique: false });
                    dashboardsStore.createIndex('userId', 'userId', { unique: false });
                    dashboardsStore.createIndex('name', 'name', { unique: false });
                }

                if (!db.objectStoreNames.contains('widgets')) {
                    const widgetsStore = db.createObjectStore('widgets', { keyPath: 'id' });
                    widgetsStore.createIndex('dashboardId', 'dashboardId', { unique: false });
                    widgetsStore.createIndex('empresaId', 'empresaId', { unique: false });
                    widgetsStore.createIndex('type', 'type', { unique: false });
                }

                if (!db.objectStoreNames.contains('company_settings')) {
                    const settingsStore = db.createObjectStore('company_settings', { keyPath: 'empresaId' });
                }

                console.log('Estructura de base de datos creada');
            };
        });
    }

    /**
     * Ejecuta una operación en un object store
     * @param {string} storeName - Nombre del object store
     * @param {string} mode - Modo de transacción ('readonly' o 'readwrite')
     * @param {Function} operation - Función que ejecuta la operación
     * @returns {Promise} - Promesa que se resuelve con el resultado
     */
    executeTransaction(storeName, mode, operation) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('Base de datos no inicializada'));
                return;
            }

            try {
                const transaction = this.db.transaction([storeName], mode);
                const store = transaction.objectStore(storeName);

                transaction.onerror = () => {
                    console.error('Error en transacción:', transaction.error);
                    reject(transaction.error);
                };

                const result = operation(store);

                // Si la operación devuelve una promesa, usarla
                if (result && typeof result.then === 'function') {
                    result.then(resolve).catch(reject);
                }
                // Si la operación devuelve un IDBRequest
                else if (result && typeof result.onsuccess !== 'undefined') {
                    result.onsuccess = (event) => {
                        resolve(event.target.result);
                    };
                    result.onerror = (event) => {
                        console.error('Error en IDBRequest:', event.target.error);
                        reject(event.target.error);
                    };
                }
                // Si la operación devuelve un valor directo
                else {
                    resolve(result);
                }
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Busca un tablero por su ID y retorna también la información de la empresa
     * @param {number} dashboardId - ID del tablero
     * @returns {Promise} - Promesa que se resuelve con {dashboard, company} o null
     */
    findDashboardById(dashboardId) {
        return new Promise((resolve, reject) => {
            this.executeTransaction('dashboards', 'readonly', (store) => {
                return store.get(dashboardId);
            })
            .then(dashboard => {
                if (!dashboard) {
                    resolve(null);
                    return;
                }

                // Obtener la información de la empresa
                return this.getCompany(dashboard.empresaId)
                    .then(company => {
                        resolve({ dashboard, company });
                    });
            })
            .catch(error => reject(error));
        });
    }

    /**
     * Obtiene los datos de una empresa
     * @param {number} companyId - ID de la empresa
     * @returns {Promise} - Promesa que se resuelve con los datos de la empresa
     */
    getCompanyData(companyId) {
        return new Promise((resolve, reject) => {
            Promise.all([
                this.getCompany(companyId),
                this.getUsers(companyId),
                this.getDashboards(companyId),
                this.getCompanySettings(companyId)
            ])
            .then(([company, users, dashboards, settings]) => {
                if (!company) {
                    resolve(null);
                    return;
                }

                const companyData = {
                    info: company,
                    usuarios: users,
                    tableros: dashboards,
                    tableroActual: settings?.tableroActual || null,
                    temaActual: settings?.temaActual || 'tron'
                };

                resolve(companyData);
            })
            .catch(error => reject(error));
        });
    }

    /**
     * Guarda los datos de una empresa (método de compatibilidad)
     * @param {number} companyId - ID de la empresa
     * @param {Object} data - Datos a guardar
     * @returns {Promise} - Promesa que se resuelve cuando los datos se han guardado
     */
    saveCompanyData(companyId, data) {
        return new Promise((resolve, reject) => {
            try {
                const promises = [];

                // Guardar información de la empresa
                if (data.info) {
                    promises.push(this.saveCompany(data.info));
                }

                // Guardar usuarios
                if (data.usuarios && Array.isArray(data.usuarios)) {
                    data.usuarios.forEach(user => {
                        promises.push(this.saveUser(user));
                    });
                }

                // Guardar tableros y widgets
                if (data.tableros && Array.isArray(data.tableros)) {
                    data.tableros.forEach(dashboard => {
                        promises.push(this.saveDashboard(dashboard, companyId));
                        
                        // Guardar widgets del tablero
                        if (dashboard.widgets && Array.isArray(dashboard.widgets)) {
                            dashboard.widgets.forEach(widget => {
                                widget.dashboardId = dashboard.id;
                                widget.empresaId = companyId;
                                promises.push(this.saveWidget(widget));
                            });
                        }
                    });
                }

                // Guardar configuración de la empresa
                const settings = {
                    empresaId: companyId,
                    tableroActual: data.tableroActual,
                    temaActual: data.temaActual || 'tron'
                };
                promises.push(this.saveCompanySettings(settings));

                Promise.all(promises)
                    .then(() => resolve())
                    .catch(error => reject(error));
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Obtiene todas las empresas
     * @returns {Promise} - Promesa que se resuelve con la lista de empresas
     */
    getAllCompanies() {
        return this.executeTransaction('companies', 'readonly', (store) => {
            const companies = [];
            const request = store.openCursor();
            
            return new Promise((resolve, reject) => {
                request.onsuccess = (event) => {
                    const cursor = event.target.result;
                    if (cursor) {
                        companies.push(cursor.value);
                        cursor.continue();
                    } else {
                        resolve(companies);
                    }
                };
                request.onerror = () => reject(request.error);
            });
        });
    }

    /**
     * Obtiene una empresa por su ID
     * @param {number} companyId - ID de la empresa
     * @returns {Promise} - Promesa que se resuelve con la empresa
     */
    getCompany(companyId) {
        return this.executeTransaction('companies', 'readonly', (store) => {
            return store.get(companyId);
        });
    }

    /**
     * Guarda una empresa
     * @param {Object} company - Empresa a guardar
     * @returns {Promise} - Promesa que se resuelve cuando la empresa se ha guardado
     */
    saveCompany(company) {
        return this.executeTransaction('companies', 'readwrite', (store) => {
            return store.put(company);
        });
    }

    /**
     * Elimina una empresa
     * @param {number} companyId - ID de la empresa
     * @returns {Promise} - Promesa que se resuelve con true si se eliminó correctamente
     */
    deleteCompany(companyId) {
        return new Promise((resolve, reject) => {
            // Eliminar todos los datos relacionados con la empresa
            Promise.all([
                this.executeTransaction('companies', 'readwrite', (store) => store.delete(companyId)),
                this.executeTransaction('users', 'readwrite', (store) => {
                    const index = store.index('empresaId');
                    const request = index.openCursor(IDBKeyRange.only(companyId));
                    const deletePromises = [];
                    
                    return new Promise((resolve, reject) => {
                        request.onsuccess = (event) => {
                            const cursor = event.target.result;
                            if (cursor) {
                                deletePromises.push(cursor.delete());
                                cursor.continue();
                            } else {
                                Promise.all(deletePromises).then(() => resolve()).catch(reject);
                            }
                        };
                        request.onerror = () => reject(request.error);
                    });
                }),
                this.executeTransaction('dashboards', 'readwrite', (store) => {
                    const index = store.index('empresaId');
                    const request = index.openCursor(IDBKeyRange.only(companyId));
                    const deletePromises = [];
                    
                    return new Promise((resolve, reject) => {
                        request.onsuccess = (event) => {
                            const cursor = event.target.result;
                            if (cursor) {
                                deletePromises.push(cursor.delete());
                                cursor.continue();
                            } else {
                                Promise.all(deletePromises).then(() => resolve()).catch(reject);
                            }
                        };
                        request.onerror = () => reject(request.error);
                    });
                }),
                this.executeTransaction('widgets', 'readwrite', (store) => {
                    const index = store.index('empresaId');
                    const request = index.openCursor(IDBKeyRange.only(companyId));
                    const deletePromises = [];
                    
                    return new Promise((resolve, reject) => {
                        request.onsuccess = (event) => {
                            const cursor = event.target.result;
                            if (cursor) {
                                deletePromises.push(cursor.delete());
                                cursor.continue();
                            } else {
                                Promise.all(deletePromises).then(() => resolve()).catch(reject);
                            }
                        };
                        request.onerror = () => reject(request.error);
                    });
                }),
                this.executeTransaction('company_settings', 'readwrite', (store) => store.delete(companyId))
            ])
            .then(() => resolve(true))
            .catch(error => {
                console.error('Error al eliminar empresa:', error);
                resolve(false);
            });
        });
    }

    /**
     * Obtiene los usuarios de una empresa
     * @param {number} companyId - ID de la empresa
     * @returns {Promise} - Promesa que se resuelve con la lista de usuarios
     */
    getUsers(companyId) {
        return this.executeTransaction('users', 'readonly', (store) => {
            const users = [];
            const index = store.index('empresaId');
            const request = index.openCursor(IDBKeyRange.only(companyId));

            return new Promise((resolve, reject) => {
                request.onsuccess = (event) => {
                    const cursor = event.target.result;
                    if (cursor) {
                        users.push(cursor.value);
                        cursor.continue();
                    } else {
                        resolve(users);
                    }
                };
                request.onerror = () => reject(request.error);
            });
        });
    }

    /**
     * Obtiene todos los usuarios de todas las empresas
     * @returns {Promise} - Promesa que se resuelve con la lista de todos los usuarios
     */
    getAllUsers() {
        return this.executeTransaction('users', 'readonly', (store) => {
            const users = [];
            const request = store.openCursor();

            return new Promise((resolve, reject) => {
                request.onsuccess = (event) => {
                    const cursor = event.target.result;
                    if (cursor) {
                        users.push(cursor.value);
                        cursor.continue();
                    } else {
                        resolve(users);
                    }
                };
                request.onerror = () => reject(request.error);
            });
        });
    }

    /**
     * Obtiene un usuario por su login
     * @param {string} login - Login del usuario
     * @returns {Promise} - Promesa que se resuelve con el usuario
     */
    getUserByLogin(login) {
        return this.executeTransaction('users', 'readonly', (store) => {
            const index = store.index('login');
            return index.get(login);
        });
    }

    /**
     * Guarda un usuario
     * @param {Object} user - Usuario a guardar
     * @returns {Promise} - Promesa que se resuelve cuando el usuario se ha guardado
     */
    saveUser(user) {
        return this.executeTransaction('users', 'readwrite', (store) => {
            return store.put(user);
        });
    }

    /**
     * Elimina un usuario
     * @param {number} userId - ID del usuario
     * @param {number} companyId - ID de la empresa
     * @returns {Promise} - Promesa que se resuelve con true si se eliminó correctamente
     */
    deleteUser(userId, companyId) {
        return this.executeTransaction('users', 'readwrite', (store) => {
            return store.delete(userId);
        }).then(() => true).catch(() => false);
    }

    /**
     * Obtiene los tableros de una empresa
     * @param {number} companyId - ID de la empresa
     * @param {number} [userId] - ID del usuario para filtrar (opcional)
     * @returns {Promise} - Promesa que se resuelve con la lista de tableros
     */
    getDashboards(companyId, userId) {
        return this.executeTransaction('dashboards', 'readonly', (store) => {
            const dashboards = [];
            const index = store.index('empresaId');
            const request = index.openCursor(IDBKeyRange.only(companyId));

            return new Promise((resolve, reject) => {
                request.onsuccess = (event) => {
                    const cursor = event.target.result;
                    if (cursor) {
                        const dashboard = cursor.value;

                        // Filtrar por usuario si se especifica
                        if (!userId || dashboard.userId === userId || !dashboard.userId) {
                            dashboards.push(dashboard);
                        }

                        cursor.continue();
                    } else {
                        // Cargar widgets para cada tablero
                        Promise.all(dashboards.map(dashboard =>
                            this.getWidgetsByDashboard(dashboard.id).then(widgets => {
                                dashboard.widgets = widgets;
                                return dashboard;
                            })
                        )).then(dashboardsWithWidgets => {
                            // Añadir información de usuario
                            Promise.all(dashboardsWithWidgets.map(async dashboard => {
                                if (dashboard.userId) {
                                    const user = await this.executeTransaction('users', 'readonly', (store) => {
                                        return store.get(dashboard.userId);
                                    });
                                    dashboard.userName = user ? user.nombre : 'Usuario desconocido';
                                } else {
                                    dashboard.userName = 'Usuario desconocido';
                                }
                                dashboard.empresaId = companyId;
                                return dashboard;
                            })).then(resolve).catch(reject);
                        }).catch(reject);
                    }
                };
                request.onerror = () => reject(request.error);
            });
        });
    }

    /**
     * Guarda un tablero
     * @param {Object} dashboard - Tablero a guardar
     * @param {number} companyId - ID de la empresa
     * @returns {Promise} - Promesa que se resuelve cuando el tablero se ha guardado
     */
    saveDashboard(dashboard, companyId) {
        return new Promise((resolve, reject) => {
            // Asegurar que el tablero tenga el ID de empresa
            dashboard.empresaId = companyId;

            // Extraer widgets del tablero para guardarlos por separado
            const widgets = dashboard.widgets || [];
            const dashboardWithoutWidgets = { ...dashboard };
            delete dashboardWithoutWidgets.widgets;

            // Guardar el tablero
            this.executeTransaction('dashboards', 'readwrite', (store) => {
                return store.put(dashboardWithoutWidgets);
            })
            .then(() => {
                // Guardar widgets por separado
                const widgetPromises = widgets.map(widget => {
                    widget.dashboardId = dashboard.id;
                    widget.empresaId = companyId;
                    return this.saveWidget(widget);
                });

                return Promise.all(widgetPromises);
            })
            .then(() => resolve())
            .catch(error => reject(error));
        });
    }

    /**
     * Elimina un tablero
     * @param {number} dashboardId - ID del tablero
     * @param {number} companyId - ID de la empresa
     * @returns {Promise} - Promesa que se resuelve con true si se eliminó correctamente
     */
    deleteDashboard(dashboardId, companyId) {
        return new Promise((resolve, reject) => {
            // Eliminar widgets del tablero primero
            this.executeTransaction('widgets', 'readwrite', (store) => {
                const index = store.index('dashboardId');
                const request = index.openCursor(IDBKeyRange.only(dashboardId));
                const deletePromises = [];

                return new Promise((resolve, reject) => {
                    request.onsuccess = (event) => {
                        const cursor = event.target.result;
                        if (cursor) {
                            deletePromises.push(cursor.delete());
                            cursor.continue();
                        } else {
                            Promise.all(deletePromises).then(() => resolve()).catch(reject);
                        }
                    };
                    request.onerror = () => reject(request.error);
                });
            })
            .then(() => {
                // Eliminar el tablero
                return this.executeTransaction('dashboards', 'readwrite', (store) => {
                    return store.delete(dashboardId);
                });
            })
            .then(() => {
                // Actualizar configuración de empresa si era el tablero actual
                return this.getCompanySettings(companyId);
            })
            .then(settings => {
                if (settings && settings.tableroActual === dashboardId) {
                    // Buscar otro tablero para establecer como actual
                    return this.getDashboards(companyId).then(dashboards => {
                        if (dashboards.length > 0) {
                            settings.tableroActual = dashboards[0].id;
                        } else {
                            settings.tableroActual = null;
                        }
                        return this.saveCompanySettings(settings);
                    });
                }
            })
            .then(() => resolve(true))
            .catch(error => {
                console.error('Error al eliminar tablero:', error);
                resolve(false);
            });
        });
    }

    /**
     * Obtiene el ID del tablero actual de una empresa
     * @param {number} companyId - ID de la empresa
     * @returns {Promise} - Promesa que se resuelve con el ID del tablero actual
     */
    getCurrentDashboardId(companyId) {
        return this.getCompanySettings(companyId)
            .then(settings => settings ? settings.tableroActual : null);
    }

    /**
     * Establece el tablero actual de una empresa
     * @param {number} companyId - ID de la empresa
     * @param {number} dashboardId - ID del tablero
     * @returns {Promise} - Promesa que se resuelve cuando se ha establecido el tablero actual
     */
    setCurrentDashboardId(companyId, dashboardId) {
        return this.getCompanySettings(companyId)
            .then(settings => {
                if (!settings) {
                    settings = { empresaId: companyId, temaActual: 'tron' };
                }
                settings.tableroActual = dashboardId;
                return this.saveCompanySettings(settings);
            });
    }

    /**
     * Obtiene el tema actual de una empresa
     * @param {number} companyId - ID de la empresa
     * @returns {Promise} - Promesa que se resuelve con el nombre del tema actual
     */
    getCurrentTheme(companyId) {
        return this.getCompanySettings(companyId)
            .then(settings => settings && settings.temaActual ? settings.temaActual : 'tron');
    }

    /**
     * Establece el tema actual de una empresa
     * @param {number} companyId - ID de la empresa
     * @param {string} theme - Nombre del tema
     * @returns {Promise} - Promesa que se resuelve cuando se ha establecido el tema actual
     */
    setCurrentTheme(companyId, theme) {
        return this.getCompanySettings(companyId)
            .then(settings => {
                if (!settings) {
                    settings = { empresaId: companyId, tableroActual: null };
                }
                settings.temaActual = theme;
                return this.saveCompanySettings(settings);
            });
    }

    /**
     * Obtiene la configuración de una empresa
     * @param {number} companyId - ID de la empresa
     * @returns {Promise} - Promesa que se resuelve con la configuración
     */
    getCompanySettings(companyId) {
        return this.executeTransaction('company_settings', 'readonly', (store) => {
            return store.get(companyId);
        });
    }

    /**
     * Guarda la configuración de una empresa
     * @param {Object} settings - Configuración a guardar
     * @returns {Promise} - Promesa que se resuelve cuando se ha guardado
     */
    saveCompanySettings(settings) {
        // Crear una copia limpia del objeto settings
        const cleanSettings = {
            empresaId: settings.empresaId,
            tableroActual: settings.tableroActual,
            temaActual: settings.temaActual || 'tron'
        };

        return this.executeTransaction('company_settings', 'readwrite', (store) => {
            return store.put(cleanSettings);
        });
    }

    /**
     * Obtiene los widgets de un tablero
     * @param {number} dashboardId - ID del tablero
     * @returns {Promise} - Promesa que se resuelve con la lista de widgets
     */
    getWidgetsByDashboard(dashboardId) {
        return this.executeTransaction('widgets', 'readonly', (store) => {
            const widgets = [];
            const index = store.index('dashboardId');
            const request = index.openCursor(IDBKeyRange.only(dashboardId));

            return new Promise((resolve, reject) => {
                request.onsuccess = (event) => {
                    const cursor = event.target.result;
                    if (cursor) {
                        widgets.push(cursor.value);
                        cursor.continue();
                    } else {
                        resolve(widgets);
                    }
                };
                request.onerror = () => reject(request.error);
            });
        });
    }

    /**
     * Guarda un widget
     * @param {Object} widget - Widget a guardar
     * @returns {Promise} - Promesa que se resuelve cuando el widget se ha guardado
     */
    saveWidget(widget) {
        // Limpiar propiedades no serializables
        const cleanWidget = this.createCleanCopy(widget);

        return this.executeTransaction('widgets', 'readwrite', (store) => {
            return store.put(cleanWidget);
        });
    }

    /**
     * Elimina un widget
     * @param {string} widgetId - ID del widget
     * @returns {Promise} - Promesa que se resuelve con true si se eliminó correctamente
     */
    deleteWidget(widgetId) {
        return this.executeTransaction('widgets', 'readwrite', (store) => {
            return store.delete(widgetId);
        }).then(() => true).catch(() => false);
    }

    /**
     * Crea una copia limpia de un objeto, eliminando propiedades problemáticas
     * Usa app.stringify que ya maneja la propiedad 'chart' y funciones
     * @param {Object} obj - Objeto a limpiar
     * @returns {Object} - Copia limpia del objeto
     */
    createCleanCopy(obj) {
        try {
            // Usar app.stringify que ya maneja propiedades problemáticas como 'chart'
            if (typeof app !== 'undefined' && app.stringify) {
                return JSON.parse(app.stringify(obj));
            } else {
                // Fallback si app.stringify no está disponible
                return JSON.parse(JSON.stringify(obj));
            }
        } catch (error) {
            console.warn('Error al crear copia limpia, usando método manual:', error);
            return this.createCleanCopyManual(obj);
        }
    }

    /**
     * Método manual para crear copia limpia cuando JSON falla
     * @param {Object} obj - Objeto a limpiar
     * @returns {Object} - Copia limpia del objeto
     */
    createCleanCopyManual(obj) {
        if (typeof obj !== 'object' || obj === null) {
            return obj;
        }

        if (Array.isArray(obj)) {
            return obj.map(item => this.createCleanCopyManual(item));
        }

        const cleanObj = {};
        const excludeProps = [
            'chart', '_chart', 'chartInstance', 'canvas', 'ctx', 'periodChart',
            'onsuccess', 'onerror', 'onupgradeneeded', 'onblocked', 'onversionchange'
        ];

        for (const key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                if (excludeProps.includes(key)) continue;
                if (typeof obj[key] === 'function') continue;
                if (obj[key] && typeof obj[key] === 'object' && obj[key].constructor &&
                    obj[key].constructor.name && obj[key].constructor.name.includes('IDB')) continue;

                try {
                    if (typeof obj[key] === 'object' && obj[key] !== null) {
                        cleanObj[key] = this.createCleanCopyManual(obj[key]);
                    } else {
                        cleanObj[key] = obj[key];
                    }
                } catch (error) {
                    // Si hay error con una propiedad específica, omitirla
                    console.warn(`Omitiendo propiedad ${key} debido a error:`, error);
                }
            }
        }

        return cleanObj;
    }

    /**
     * Genera un nombre de empresa aleatorio con sentido
     * @returns {string} - Nombre de empresa generado
     */
    generateCompanyName() {
        // Prefijos para empresas tecnológicas
        const techPrefixes = ['Tech', 'Cyber', 'Digital', 'Smart', 'Intelligent', 'Advanced', 'Future', 'Next', 'Modern', 'Dynamic'];
        const techRoots = ['Soft', 'Tech', 'Systems', 'Solutions', 'Logic', 'Data', 'Net', 'Web', 'Cloud', 'Mobile'];
        const techSuffixes = ['Corp', 'Inc', 'Ltd', 'Group', 'Systems', 'Solutions', 'Technologies', 'Enterprises'];

        // Nombres para empresas industriales
        const industrialNames = ['Aceros', 'Hierros', 'Metales', 'Construcciones', 'Materiales', 'Maquinaria', 'Equipos'];
        const industrialSuffixes = ['Industrial', 'Mecánica', 'Técnica', 'Constructora', 'Ingeniería'];

        // Nombres para empresas de servicios
        const serviceNames = ['Asesoría', 'Consultoría', 'Gestión', 'Servicios', 'Soluciones'];
        const serviceAreas = ['Fiscal', 'Contable', 'Jurídica', 'Financiera', 'Comercial'];

        // Elegir un tipo de empresa al azar
        const companyTypes = [
            {
                prefixes: techPrefixes,
                roots: techRoots,
                suffixes: techSuffixes,
                format: (p, r, s) => `${p}${r} ${s}`
            },
            {
                prefixes: industrialNames,
                suffixes: industrialSuffixes,
                format: (p, s) => `${p} ${s}`
            },
            {
                prefixes: serviceNames,
                suffixes: serviceAreas,
                format: (p, s) => `${p} ${s}`
            }
        ];

        const companyType = companyTypes[Math.floor(Math.random() * companyTypes.length)];

        if (companyType.roots) {
            const prefix = companyType.prefixes[Math.floor(Math.random() * companyType.prefixes.length)];
            const root = companyType.roots[Math.floor(Math.random() * companyType.roots.length)];
            const suffix = companyType.suffixes[Math.floor(Math.random() * companyType.suffixes.length)];
            return companyType.format(prefix, root, suffix);
        } else {
            const prefix = companyType.prefixes[Math.floor(Math.random() * companyType.prefixes.length)];
            const suffix = companyType.suffixes[Math.floor(Math.random() * companyType.suffixes.length)];
            return companyType.format(prefix, suffix);
        }
    }

    /**
     * Inicializa la base de datos con datos de prueba si está vacía
     * @returns {Promise} - Promesa que se resuelve cuando se han inicializado los datos de prueba
     */
    initializetestdata() {
        return new Promise(async (resolve, reject) => {
            try {
                // Verificar si ya hay empresas en la base de datos
                const companies = await this.getAllCompanies();
                if (companies.length >= 500) {
                    console.log('La base de datos ya tiene 500 o más empresas, omitiendo inicialización');
                    resolve();
                    return;
                }

                console.log(`Inicializando datos de prueba en IndexedDB (${companies.length} empresas existentes, generando hasta 500)...`);

                const now = new Date().toISOString();
                let nextUserId = 1;
                let nextDashboardId = 1;

                // Solo crear empresas 1 y 2 si no existen
                const company1 = await this.getCompany(1);
                if (!company1) {
                    // Crear empresa por defecto (id=1, nombre=iPRA, activo=true)
                    const defaultCompany = { id: 1, nombre: 'iPRA', activo: true };
                    await this.saveCompany(defaultCompany);

                    // Crear usuario administrador (login=admin, clave=super)
                    const adminUser = {
                        id: nextUserId++, login: 'admin', nombre: 'Administrador', clave: 'super',
                        telefono: '', email: '<EMAIL>', tipo: 'admin', empresaId: 1
                    };
                    await this.saveUser(adminUser);

                    // Crear usuario normal (login=usu1, clave=usu1)
                    const normalUser = {
                        id: nextUserId++, login: 'usu1', nombre: 'Usuario Normal', clave: 'usu1',
                        telefono: '', email: '<EMAIL>', tipo: 'usuario', empresaId: 1
                    };
                    await this.saveUser(normalUser);

                    // Crear tablero para empresa 1
                    const defaultDashboard = {
                        id: nextDashboardId++, name: 'Tablero principal', width: 800, height: 600,
                        backgroundColor: '#ffffff', showWidgetBorders: true,
                        transparentWidgets: false, showGrid: true, gridColor: '#1a3a5a',
                        widgetCount: 0, theme: 'tron', widgets: [], userId: adminUser.id,
                        userName: adminUser.nombre, empresaId: 1,
                        fechaAlta: now, fechaModificacion: now,
                        usuarioAltaId: adminUser.id, usuarioModificacionId: adminUser.id
                    };
                    await this.saveDashboard(defaultDashboard, 1);
                    await this.setCurrentDashboardId(1, 1);
                }

                const company2 = await this.getCompany(2);
                if (!company2) {
                    // Segunda empresa (id=2, nombre=empresa2, activo=true)
                    const secondCompany = { id: 2, nombre: 'empresa2', activo: true };
                    await this.saveCompany(secondCompany);

                    // Usuario administrador para empresa 2 (login=admin2, clave=super2)
                    const admin2User = {
                        id: nextUserId++, login: 'admin2', nombre: 'Administrador Empresa 2', clave: 'super2',
                        telefono: '', email: '<EMAIL>', tipo: 'admin', empresaId: 2
                    };
                    await this.saveUser(admin2User);

                    // Usuario normal para empresa 2 (login=usu2, clave=usu2)
                    const normal2User = {
                        id: nextUserId++, login: 'usu2', nombre: 'Usuario Normal Empresa 2', clave: 'usu2',
                        telefono: '', email: '<EMAIL>', tipo: 'usuario', empresaId: 2
                    };
                    await this.saveUser(normal2User);

                    // Tablero para empresa 2
                    const secondDashboard = {
                        id: nextDashboardId++, name: 'tablero empresa 2', width: 800, height: 600,
                        backgroundColor: '#ffffff', showWidgetBorders: true,
                        transparentWidgets: false, showGrid: true, gridColor: '#1a3a5a',
                        widgetCount: 0, theme: 'tron', widgets: [], userId: admin2User.id,
                        userName: admin2User.nombre, empresaId: 2,
                        fechaAlta: now, fechaModificacion: now,
                        usuarioAltaId: admin2User.id, usuarioModificacionId: admin2User.id
                    };
                    await this.saveDashboard(secondDashboard, 2);
                    await this.setCurrentDashboardId(2, 2);
                }

                // Ajustar contadores basándose en datos existentes
                const allUsers = await this.getAllUsers();
                const allDashboards = await this.getDashboards(1).then(d1 => this.getDashboards(2).then(d2 => [...d1, ...d2]));
                nextUserId = Math.max(nextUserId, ...allUsers.map(u => u.id), 0) + 1;
                nextDashboardId = Math.max(nextDashboardId, ...allDashboards.map(d => d.id), 0) + 1;

                // Generar empresas adicionales (para llegar a 500 en total)
                console.log(`Continuando desde usuario ID ${nextUserId} y dashboard ID ${nextDashboardId}`);

                for (let i = 3; i <= 500; i++) {
                    const companyId = i;

                    // Verificar si la empresa ya existe
                    const existingCompany = await this.getCompany(companyId);
                    if (existingCompany) {
                        continue; // Saltar si ya existe
                    }

                    const companyName = this.generateCompanyName();

                    // Crear empresa
                    const company = {
                        id: companyId,
                        nombre: companyName,
                        activo: Math.random() > 0.2 // 80% de probabilidad de estar activo
                    };
                    await this.saveCompany(company);

                    // Crear usuario para esta empresa (usuN con clave claveN)
                    const user = {
                        id: nextUserId++,
                        login: `usu${companyId}`,
                        nombre: `Usuario de ${companyName}`,
                        clave: `clave${companyId}`,
                        telefono: '',
                        email: `usuario@${companyName.toLowerCase().replace(/[^a-z0-9]/g, '')}.com`,
                        tipo: 'usuario',
                        empresaId: companyId
                    };
                    await this.saveUser(user);

                    // Crear tablero para esta empresa
                    const dashboard = {
                        id: nextDashboardId++,
                        name: `Tablero de ${companyName}`,
                        width: 800,
                        height: 600,
                        backgroundColor: '#ffffff',
                        showWidgetBorders: true,
                        transparentWidgets: false,
                        showGrid: true,
                        gridColor: '#1a3a5a',
                        widgetCount: 0,
                        theme: 'tron',
                        widgets: [],
                        userId: user.id,
                        userName: user.nombre,
                        empresaId: companyId,
                        fechaAlta: now,
                        fechaModificacion: now,
                        usuarioAltaId: user.id,
                        usuarioModificacionId: user.id
                    };
                    await this.saveDashboard(dashboard, companyId);
                    await this.setCurrentDashboardId(companyId, dashboard.id);

                    // Log progreso cada 50 empresas
                    if (i % 50 === 0) {
                        console.log(`Procesadas ${i} empresas...`);
                    }
                }

                console.log('Datos de prueba inicializados correctamente en IndexedDB (500 empresas)');
                resolve();

            } catch (error) {
                console.error('Error al inicializar datos de prueba:', error);
                reject(error);
            }
        });
    }
}

// Crear instancia del gestor de base de datos
const dbManager = new DatabaseManager();
