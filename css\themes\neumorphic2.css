/* Estilos adicionales para el tema Neumorphic */

/* Estilos para diálogos */
.theme-neumorphic .dialog-content {
    background-color: var(--neumorphic-background);
    border: none;
    border-radius: 20px;
    box-shadow: var(--neumorphic-shadow-out);
}

.theme-neumorphic .dialog-header {
    background-color: var(--neumorphic-background);
    border-bottom: 1px solid var(--neumorphic-border);
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
}

.theme-neumorphic .dialog-header h3 {
    color: var(--neumorphic-primary);
    font-weight: 600;
}

.theme-neumorphic .dialog-header .close-btn {
    color: var(--neumorphic-text);
    background-color: var(--neumorphic-background);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--neumorphic-shadow-in);
}

.theme-neumorphic .dialog-header .close-btn:hover {
    color: var(--neumorphic-primary);
}

.theme-neumorphic .primary-btn {
    background-color: var(--neumorphic-background);
    color: var(--neumorphic-primary);
    border: none;
    padding: 10px 20px;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 600;
    box-shadow: var(--neumorphic-shadow-out);
    transition: all 0.3s ease;
}

.theme-neumorphic .primary-btn:hover {
    box-shadow: var(--neumorphic-shadow-out-hover);
    transform: translateY(-2px);
}

.theme-neumorphic .primary-btn:active {
    box-shadow: var(--neumorphic-shadow-in);
    transform: translateY(0);
}

.theme-neumorphic .secondary-btn {
    background-color: var(--neumorphic-background);
    color: var(--neumorphic-text);
    border: none;
    padding: 10px 20px;
    border-radius: 10px;
    cursor: pointer;
    box-shadow: var(--neumorphic-shadow-out);
    transition: all 0.3s ease;
}

.theme-neumorphic .secondary-btn:hover {
    box-shadow: var(--neumorphic-shadow-out-hover);
    transform: translateY(-2px);
}

.theme-neumorphic .secondary-btn:active {
    box-shadow: var(--neumorphic-shadow-in);
    transform: translateY(0);
}

.theme-neumorphic .dialog-content input[type="text"] {
    background-color: var(--neumorphic-background);
    color: var(--neumorphic-text);
    border: none;
    border-radius: 10px;
    padding: 12px 15px;
    box-shadow: var(--neumorphic-shadow-in);
}

.theme-neumorphic .dialog-content input[type="text"]:focus {
    outline: none;
    box-shadow: var(--neumorphic-shadow-in-focus);
}

.theme-neumorphic .menu-button {
    background-color: var(--neumorphic-background);
    color: var(--neumorphic-text);
    border: 1px solid var(--neumorphic-primary);
    border-radius: 15px;
}

/* Estilo del borde para el tema Neumorphic */
.theme-neumorphic .menu-border {
    border: 2px solid var(--primary-color);
    border-radius: 15px;
    box-shadow: var(--shadow-small);
}

.theme-neumorphic .menu-button .icon {
    color: var(--neumorphic-primary);
}

/* Estilo para el botón de logout en tema Neumorphic */
.theme-neumorphic .logout-btn {
    color: var(--primary-color);
    box-shadow: var(--shadow-small);
}

.theme-neumorphic .logout-btn:hover {
    background-color: var(--surface-color);
    box-shadow: var(--shadow-inset);
}

.theme-neumorphic .entity-table {
    border-radius: 15px;
    box-shadow: var(--shadow-small);
    background-color: var(--neumorphic-background);
    overflow: hidden;
    border: 2px solid var(--primary-color);
}

.theme-neumorphic .entity-table th {
    background-color: var(--neumorphic-primary);
    color: white;
    border-bottom: 2px solid var(--neumorphic-border);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    font-weight: 600;
    text-transform: none;
    padding: 15px 10px;
}

.theme-neumorphic .entity-table th:last-child {
    border-right: none;
}

.theme-neumorphic .entity-table td {
    border-bottom: 1px solid var(--neumorphic-border);
    border-right: 1px solid var(--neumorphic-border);
    padding: 12px 10px;
    background-color: var(--neumorphic-background);
}

.theme-neumorphic .entity-table td:last-child {
    border-right: none;
}

.theme-neumorphic .entity-table tr:last-child td {
    border-bottom: none;
}

.theme-neumorphic .entity-table tr:hover td {
    background-color: var(--neumorphic-hover);
}

.theme-neumorphic .entity-table .checkbox-cell {
    width: 40px;
    vertical-align: middle;
    text-align: center;
    padding-left: 0;
    padding-right: 0;
}

.theme-neumorphic .entity-table .action-cell {
    width: 40px;
    vertical-align: middle;
    text-align: center;
    padding-left: 0;
    padding-right: 0;
}

.theme-neumorphic .entity-action-menu {
    border-radius: 10px;
    box-shadow: var(--neumorphic-shadow-out);
    background-color: var(--neumorphic-background);
    border: 1px solid var(--neumorphic-border);
}

.theme-neumorphic .entity-action-menu button {
    border-radius: 5px;
    margin: 5px;
    transition: all 0.3s ease;
}

.theme-neumorphic .entity-action-menu button:hover {
    background-color: var(--neumorphic-hover);
    transform: translateY(-2px);
}

.theme-neumorphic .entity-filter {
    background-color: var(--neumorphic-background);
    color: var(--neumorphic-text);
    border: none;
    border-radius: 10px;
    box-shadow: var(--neumorphic-shadow-in);
    padding: 12px 15px;
}

.theme-neumorphic .entity-filter:focus {
    outline: none;
    box-shadow: var(--neumorphic-shadow-in-focus);
}

.theme-neumorphic .entity-action-menu {
    background-color: var(--neumorphic-background);
    border: none;
    border-radius: 15px;
    box-shadow: var(--neumorphic-shadow-out);
}

.theme-neumorphic .entity-action-menu button {
    color: var(--neumorphic-text);
    border-bottom: 1px solid var(--neumorphic-border);
}

.theme-neumorphic .entity-action-menu button:hover {
    background-color: var(--neumorphic-hover);
}

.theme-neumorphic .entity-count {
    color: var(--neumorphic-secondary);
    font-weight: 500;
}

/* Estilos para checkboxes en tema Neumorphic */
.theme-neumorphic .form-group input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    background-color: var(--surface-color);
    border-radius: 5px;
    box-shadow: var(--shadow-inset);
    position: relative;
    cursor: pointer;
}

.theme-neumorphic .form-group input[type="checkbox"]:checked {
    background-color: var(--primary-color);
    box-shadow: var(--shadow-inset);
}

.theme-neumorphic .form-group input[type="checkbox"]:checked::before {
    content: '✓';
    position: absolute;
    color: white;
    font-size: 14px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.theme-neomorphic .chart-widget .chart-container canvas {
    border-radius: 10px;
}