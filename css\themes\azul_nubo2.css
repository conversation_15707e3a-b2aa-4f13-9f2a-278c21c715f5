/* Estilos adicionales para el tema Azul Nuboso */

/* Estilos para diálogos */
.theme-azul_nuboso .dialog-content {
    background-color: #fff;
    border: 1px solid #ddd;
}

.theme-azul_nuboso .dialog-header {
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
}

.theme-azul_nuboso .dialog-header h3 {
    color: #333;
}

.theme-azul_nuboso .dialog-header .close-btn:hover {
    color: #e53935;
}

.theme-azul_nuboso .primary-btn {
    background-color: #4a6fa5;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

.theme-azul_nuboso .primary-btn:hover {
    background-color: #3a5a8c;
}

.theme-azul_nuboso .secondary-btn {
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

.theme-azul_nuboso .secondary-btn:hover {
    background-color: #e0e0e0;
}

/* Estilos para el menú principal */
.theme-azul_nuboso .main-menu-container {
    padding: 20px;
}

.theme-azul_nuboso .main-menu {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.theme-azul_nuboso .menu-button {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.theme-azul_nuboso .menu-button:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.theme-azul_nuboso .menu-button .icon {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #4a6fa5;
}

.theme-azul_nuboso .menu-button .title {
    font-weight: bold;
    color: #333;
}

/* Estilos para el menú de opciones */
.theme-azul_nuboso .entity-action-menu {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.theme-azul_nuboso .entity-action-menu button {
    color: #333;
    padding: 8px 12px;
    text-align: left;
    width: 100%;
    border: none;
    background: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.theme-azul_nuboso .entity-action-menu button:hover {
    background-color: #4a6fa5;
    color: white;
}

.theme-azul_nuboso .entity-action-menu button:active {
    background-color: #3a5a8c;
    color: white;
}

/* Estilos para el botón de cancelar */
.theme-azul_nuboso .cancel-btn {
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

.theme-azul_nuboso .cancel-btn:hover {
    background-color: #e0e0e0;
}

/* Estilos para el botón de eliminar */
.theme-azul_nuboso .delete-btn {
    background-color: #f44336;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

.theme-azul_nuboso .delete-btn:hover {
    background-color: #d32f2f;
}

/* Estilos para el diálogo de confirmación de eliminación */
.theme-azul_nuboso .delete-confirm-dialog {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.theme-azul_nuboso .delete-confirm-dialog h3 {
    color: #333;
    margin-bottom: 15px;
}

.theme-azul_nuboso .delete-confirm-dialog p {
    color: #555;
    margin-bottom: 20px;
}

.theme-azul_nuboso .delete-confirm-dialog .button-group {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Estilos para el botón de opciones */
.theme-azul_nuboso .options-btn {
    background: none;
    border: none;
    color: #4a6fa5;
    cursor: pointer;
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.theme-azul_nuboso .options-btn:hover {
    color: #3a5a8c;
}

/* Estilos para el menú desplegable de opciones del dashboard */
.theme-azul_nuboso .dashboard-options-menu {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.theme-azul_nuboso .dashboard-options-menu button {
    color: #333;
    padding: 8px 12px;
    text-align: left;
    width: 100%;
    border: none;
    background: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.theme-azul_nuboso .dashboard-options-menu button:hover {
    background-color: #4a6fa5;
    color: white;
}

.theme-azul_nuboso .dashboard-options-menu button:active {
    background-color: #3a5a8c;
    color: white;
}

/* Estilos para el diálogo de configuración del dashboard */
.theme-azul_nuboso .dashboard-config-dialog {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.theme-azul_nuboso .dashboard-config-dialog h3 {
    color: #333;
    margin-bottom: 15px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

.theme-azul_nuboso .dashboard-config-dialog .form-group {
    margin-bottom: 15px;
}

.theme-azul_nuboso .dashboard-config-dialog label {
    display: block;
    margin-bottom: 5px;
    color: #555;
}

.theme-azul_nuboso .dashboard-config-dialog input,
.theme-azul_nuboso .dashboard-config-dialog select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.theme-azul_nuboso .dashboard-config-dialog input:focus,
.theme-azul_nuboso .dashboard-config-dialog select:focus {
    border-color: #4a6fa5;
    box-shadow: 0 0 5px rgba(74, 111, 165, 0.3);
}

/* Estilos para el diálogo de edición de widget */
.theme-azul_nuboso .widget-edit-dialog {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.theme-azul_nuboso .widget-edit-dialog h3 {
    color: #333;
    margin-bottom: 15px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

.theme-azul_nuboso .widget-edit-dialog .form-group {
    margin-bottom: 15px;
}

.theme-azul_nuboso .widget-edit-dialog label {
    display: block;
    margin-bottom: 5px;
    color: #555;
}

.theme-azul_nuboso .widget-edit-dialog input,
.theme-azul_nuboso .widget-edit-dialog select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.theme-azul_nuboso .widget-edit-dialog input:focus,
.theme-azul_nuboso .widget-edit-dialog select:focus {
    border-color: #4a6fa5;
    box-shadow: 0 0 5px rgba(74, 111, 165, 0.3);
}

/* Estilos para las etiquetas de los formularios de entidades */
.theme-azul_nuboso #entity-form-container label {
    color: #4a6fa5;
    font-weight: 500;
    margin-bottom: 5px;
    display: block;
}

/* Estilos para el botón de cerrar en los formularios de entidades */
.theme-azul_nuboso #entity-edit-modal .close-btn:hover {
    background-color: #4a6fa5;
    color: white;
}

/* Estilos para el botón de volver en los listados de entidades */
.theme-azul_nuboso #entity-back-btn:hover {
    background-color: #4a6fa5;
    color: white;
}