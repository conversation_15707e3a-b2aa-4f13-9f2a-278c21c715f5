<!DOCTYPE html>
<html>
<head>
    <title><PERSON><PERSON><PERSON></title>
</head>
<body>
    <h1><PERSON><PERSON><PERSON></h1>
    <canvas id="canvas" width="32" height="32" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <button onclick="downloadFavicon()">Descargar favicon.ico</button>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Dibujar el favicon
        function drawFavicon() {
            // Fondo circular
            ctx.fillStyle = '#1a3a5a';
            ctx.beginPath();
            ctx.arc(16, 16, 15, 0, 2 * Math.PI);
            ctx.fill();
            
            // Borde
            ctx.strokeStyle = '#4a90e2';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // Widget superior izquierdo
            ctx.fillStyle = '#4a90e2';
            ctx.fillRect(6, 6, 8, 6);
            
            // Widget superior derecho
            ctx.fillStyle = '#5cb85c';
            ctx.fillRect(18, 6, 8, 6);
            
            // Widget inferior izquierdo
            ctx.fillStyle = '#f0ad4e';
            ctx.fillRect(6, 16, 8, 10);
            
            // Widget inferior derecho
            ctx.fillStyle = '#d9534f';
            ctx.fillRect(18, 16, 8, 10);
            
            // Líneas de grid
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.lineWidth = 0.5;
            ctx.beginPath();
            ctx.moveTo(6, 14);
            ctx.lineTo(26, 14);
            ctx.moveTo(16, 6);
            ctx.lineTo(16, 26);
            ctx.stroke();
        }
        
        function downloadFavicon() {
            // Crear un canvas de 16x16 para el favicon
            const faviconCanvas = document.createElement('canvas');
            faviconCanvas.width = 16;
            faviconCanvas.height = 16;
            const faviconCtx = faviconCanvas.getContext('2d');
            
            // Escalar el dibujo a 16x16
            faviconCtx.drawImage(canvas, 0, 0, 32, 32, 0, 0, 16, 16);
            
            // Convertir a blob y descargar
            faviconCanvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'favicon.ico';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 'image/x-icon');
        }
        
        // Dibujar al cargar
        drawFavicon();
    </script>
</body>
</html>
