(function (exports) {

    /**
     * Utilitarios para la creación de elementos HTML reutilizables
     * Evita la duplicación de código y centraliza la generación de UI
     */
    const uhtml = {};

    /**
     * Crea y configura un elemento HTML genérico
     * @param {string} tipo - Tipo de elemento a crear (div, span, etc)
     * @param {Object} config - Configuración del elemento
     * @param {HTMLElement} contenedor - Contenedor donde se a<PERSON> (opcional)
     * @returns {HTMLElement} - El elemento creado
     */
    uhtml.crearElemento = function (tipo, config, contenedor) {
        const elemento = document.createElement(tipo);

        // Configuración básica
        if (config.id) elemento.id = config.id;
        if (config.class) elemento.className = config.class;
        if (config.text) elemento.textContent = config.text;
        if (config.html) elemento.innerHTML = config.html;
        if (config.title) elemento.title = config.title;

        // Atributos data
        if (config.accion) elemento.dataset.accion = config.accion;
        if (config.accion2) elemento.dataset.accion2 = config.accion2;

        // Atributos personalizados
        if (config.attrs) {
            Object.entries(config.attrs).forEach(([key, value]) => {
                elemento.setAttribute(key, value);
            });
        }

        // Estilos inline (usar con moderación)
        if (config.style) {
            Object.entries(config.style).forEach(([key, value]) => {
                elemento.style[key] = value;
            });
        }

        // Añadir al contenedor si se proporciona
        if (contenedor) {
            contenedor.appendChild(elemento);
        }

        return elemento;
    };

    /**
     * Crea un botón con configuración específica
     * @param {Object} config - Configuración del botón
     * @param {HTMLElement} contenedor - Contenedor donde se añadirá
     * @returns {HTMLElement} - El botón creado
     */
    uhtml.crearBoton = function (config, contenedor) {
        return uhtml.crearElemento('button', config, contenedor);
    };

    /**
     * Crea un ícono de Material Design
     * @param {string} nombre - Nombre del ícono de Material Icons
     * @param {Object} config - Configuración adicional del ícono
     * @param {HTMLElement} contenedor - Contenedor donde se añadirá
     * @returns {HTMLElement} - El ícono creado
     */
    uhtml.crearIcono = function (nombre, config = {}, contenedor) {
        config.class = `material-icons ${config.class || ''}`.trim();

        return uhtml.crearElemento('i', config, contenedor);
    };

    /**
     * Crea una etiqueta para un grupo de formulario
     * @param {Object} config - Configuración de la etiqueta
     * @param {HTMLElement} contenedor - Contenedor donde se añadirá
     * @returns {HTMLElement} - La etiqueta creada
     */
    uhtml.crearGroupLabel = function (config, contenedor) {
        return uhtml.crearElemento('label', config, contenedor);
    };

    /**
     * Crea un input de texto
     * @param {Object} config - Configuración del input
     * @param {HTMLElement} contenedor - Contenedor donde se añadirá
     * @returns {HTMLElement} - El input creado
     */
    uhtml.crearInputText = function (config, contenedor) {
        config.attrs = config.attrs || {};
        config.attrs.type = 'text';

        return uhtml.crearElemento('input', config, contenedor);
    };

    /**
     * Crea un input numérico
     * @param {Object} config - Configuración del input
     * @param {HTMLElement} contenedor - Contenedor donde se añadirá
     * @returns {HTMLElement} - El input creado
     */
    uhtml.crearInputNumber = function (config, contenedor) {
        config.attrs = config.attrs || {};
        config.attrs.type = 'number';

        return uhtml.crearElemento('input', config, contenedor);
    };

    /**
     * Crea un checkbox
     * @param {Object} config - Configuración del checkbox
     * @param {HTMLElement} contenedor - Contenedor donde se añadirá
     * @returns {HTMLElement} - El checkbox creado
     */
    uhtml.crearCheckbox = function (config, contenedor) {
        config.attrs = config.attrs || {};
        config.attrs.type = 'checkbox';

        let nodo = uhtml.crearElemento('input', config, contenedor);
        nodo.checked = config.attrs.checked;
        return nodo;
    };

    /**
     * Crea un select
     * @param {Object} config - Configuración del select
     * @param {Array} opciones - Array de opciones {value, label}
     * @param {HTMLElement} contenedor - Contenedor donde se añadirá
     * @returns {HTMLElement} - El select creado
     */
    uhtml.crearSelect = function (config, opciones, contenedor) {
        const select = uhtml.crearElemento('select', config, contenedor);

        // Añadir opciones si se proporcionan
        if (Array.isArray(opciones)) {
            opciones.forEach(opcion => {
                uhtml.crearElemento('option', {
                    text: opcion.label,
                    attrs: {
                        value: opcion.value,
                        selected: opcion.selected ? 'selected' : null
                    }
                }, select);
            });
        }

        return select;
    };

    /**
     * Crea un grupo de campo con etiqueta e input
     * @param {Object} config - Configuración del grupo
     * @param {HTMLElement} contenedor - Contenedor donde se añadirá
     * @returns {HTMLElement} - El grupo de campo creado
     */
    uhtml.crearGrupoCampo = function (config, contenedor) {
        // Determinar la clase correcta según el tipo de campo
        const groupClass = config.type === 'checkbox' ? 'checkbox-group' : 'form-group';

        // Crear el contenedor del grupo
        const grupo = uhtml.crearElemento('div', {
            class: `${groupClass} ${config.groupClass || ''}`.trim()
        }, contenedor);

        if (config.type === 'checkbox') {
            // Para checkboxes: primero el input, luego la etiqueta
            uhtml.crearCheckbox({
                id: config.id,
                class: config.inputClass
            }, grupo);

            // Crear la etiqueta para el checkbox
            uhtml.crearGroupLabel({
                text: config.label,
                class: config.labelClass,
                for: config.id,
                type: 'checkbox'
            }, grupo);

            return
        }
        // Para otros campos: primero la etiqueta, luego el input
        uhtml.crearGroupLabel({
            text: config.label,
            class: config.labelClass,
            for: config.id
        }, grupo);

        // Crear el input según el tipo
        switch (config.type) {
            case 'select':
                uhtml.crearSelect({
                    id: config.id,
                    class: config.inputClass || '',
                }, config.opciones, grupo);
                break;

            case 'number':
                uhtml.crearInputNumber({
                    id: config.id,
                    class: config.inputClass,
                }, grupo);
                break;

            case 'text':
            default:
                uhtml.crearInputText({
                    id: config.id,
                    class: config.inputClass,
                }, grupo);
                break;
        }


        // Añadir mensaje de error si se proporciona
        if (config.errorId) {
            uhtml.crearElemento('div', {
                id: config.errorId,
                class: 'error-message'
            }, grupo);
        }

        return grupo;
    };

    exports.uhtml = uhtml;
})(window);