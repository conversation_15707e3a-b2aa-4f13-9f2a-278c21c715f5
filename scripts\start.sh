#!/bin/bash

# Script para iniciar el servidor IPRA_I (Linux)

set -e

# Obtener directorio del script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "=== INICIANDO SERVIDOR IPRA_I ==="

# Cargar configuración
if [ ! -f "$SCRIPT_DIR/config.conf" ]; then
    echo "ERROR: Archivo scripts/config.conf no encontrado."
    exit 1
fi

source "$SCRIPT_DIR/config.conf"

echo "Puerto: $SERVER_PORT"
echo "URL: http://$SERVER_HOST:$SERVER_PORT"
echo

# Verificar si ya está ejecutándose
if netstat -tuln 2>/dev/null | grep -q ":$SERVER_PORT "; then
    echo "El puerto $SERVER_PORT ya está en uso"
    echo "¿El servidor ya está ejecutándose?"
    exit 1
fi

# Crear directorios de logs
mkdir -p "$LOG_DIR"

cd "$PROJECT_ROOT"

# Procesar argumentos
if [ "$1" = "-b" ] || [ "$1" = "--background" ]; then
    echo "Iniciando servidor en background..."
    
    cd backend
    nohup node server.js > "../$LOG_DIR/server.log" 2>&1 &
    SERVER_PID=$!
    
    echo $SERVER_PID > "../$LOG_DIR/ipra-i.pid"
    
    echo "Servidor iniciado en background (PID: $SERVER_PID)"
    echo "Logs: $LOG_DIR/server.log"
    echo "Para detener: scripts/stop.sh"
else
    echo "Iniciando servidor en primer plano..."
    echo "Presiona Ctrl+C para detener"
    echo
    
    cd backend
    node server.js
fi
